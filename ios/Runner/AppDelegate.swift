import UIKit
import Flutter
import flutter_downloader
import SystemConfiguration
import Pay<PERSON>bSD<PERSON>

@main
@objc class AppDelegate: FlutterAppDelegate {
    private let CHANNEL = "com.app.thrivve.finance.thrivve.stg/proxy"
    private let PAYMOB_CHANNEL = "paymob_sdk_flutter"
    var SDKResult: FlutterResult?

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        FlutterDownloaderPlugin.setPluginRegistrantCallback(registerPlugins)

        let controller = window?.rootViewController as! FlutterViewController

        // Proxy channel
        let proxyChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
        proxyChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            if call.method == "getSystemProxy" {
                result(self.getSystemProxy())
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        // Paymob channel
        let nativeChannel = FlutterMethodChannel(name: PAYMOB_CHANNEL, binaryMessenger: controller.binaryMessenger)
        nativeChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            if call.method == "payWithPaymob",
               let args = call.arguments as? [String: Any] {
                self.SDKResult = result
                self.callNativeSDK(arguments: args, VC: controller)
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    private func getSystemProxy() -> String? {
        if let proxySettings = CFNetworkCopySystemProxySettings()?.takeRetainedValue() as? [String: Any],
           let proxies = CFNetworkCopyProxiesForURL(URL(string: "http://example.com")! as CFURL, proxySettings as CFDictionary).takeRetainedValue() as? [[String: Any]],
           let proxy = proxies.first,
           let host = proxy[kCFProxyHostNameKey as String] as? String,
           let port = proxy[kCFProxyPortNumberKey as String] as? Int {
            return "\(host):\(port)"
        }
        return nil
    }

    private func callNativeSDK(arguments: [String: Any], VC: FlutterViewController) {
        let paymob = PaymobSDK()
        var savedCards: [SavedBankCard] = []
        paymob.delegate = self

        if let appName = arguments["appName"] as? String {
            paymob.paymobSDKCustomization.appName = appName
        }
        if let buttonBackgroundColor = arguments["buttonBackgroundColor"] as? NSNumber {
            let colorInt = buttonBackgroundColor.intValue
            let alpha = CGFloat((colorInt >> 24) & 0xFF) / 255.0
            let red = CGFloat((colorInt >> 16) & 0xFF) / 255.0
            let green = CGFloat((colorInt >> 8) & 0xFF) / 255.0
            let blue = CGFloat(colorInt & 0xFF) / 255.0
            let color = UIColor(red: red, green: green, blue: blue, alpha: alpha)
            paymob.paymobSDKCustomization.buttonBackgroundColor = color
        }
        if let buttonTextColor = arguments["buttonTextColor"] as? NSNumber {
            let colorInt = buttonTextColor.intValue
            let alpha = CGFloat((colorInt >> 24) & 0xFF) / 255.0
            let red = CGFloat((colorInt >> 16) & 0xFF) / 255.0
            let green = CGFloat((colorInt >> 8) & 0xFF) / 255.0
            let blue = CGFloat(colorInt & 0xFF) / 255.0
            let color = UIColor(red: red, green: green, blue: blue, alpha: alpha)
            paymob.paymobSDKCustomization.buttonTextColor = color
        }
        if let saveCardDefault = arguments["saveCardDefault"] as? Bool {
            paymob.paymobSDKCustomization.saveCardDefault = saveCardDefault
        }
        if let showSaveCard = arguments["showSaveCard"] as? Bool {
            paymob.paymobSDKCustomization.showSaveCard = showSaveCard
        }
        
        paymob.paymobSDKCustomization.showConfirmationPage = false
       // Process saved cards (single or multiple)

               if let savedCardsData = arguments["savedBankCard"] as? [[String: String]] {
                   for cardData in savedCardsData {
                       if let token = cardData["token"],
                          let maskedPanNumber = cardData["maskedPanNumber"],
                          let cardType = cardData["cardType"] {
                           let savedCard = SavedBankCard(
                               token: token,
                               maskedPanNumber: maskedPanNumber,
                               cardType: CardType(rawValue: cardType) ?? CardType.Unknown
                           )
                           savedCards.append(savedCard)
                       }
                   }
               }

        if let publicKey = arguments["publicKey"] as? String,
           let clientSecret = arguments["clientSecret"] as? String {
            do {
                try paymob.presentPayVC(VC: VC, PublicKey: publicKey, ClientSecret: clientSecret, SavedBankCards: savedCards)
            } catch let error {
                print(error.localizedDescription)
                self.SDKResult?(FlutterError(code: "PAYMOB_ERROR", message: error.localizedDescription, details: nil))
            }
            return
        }
    }
}

private func registerPlugins(registry: FlutterPluginRegistry) {
    if (!registry.hasPlugin("FlutterDownloaderPlugin")) {
        FlutterDownloaderPlugin.register(with: registry.registrar(forPlugin: "FlutterDownloaderPlugin")!)
    }
}

extension AppDelegate: PaymobSDKDelegate {
  public func transactionRejected() {
      print("PaymobSDK: Transaction rejected")
      self.SDKResult?("Rejected")
  }

  public func transactionAccepted(transactionDetails: [String : Any]) {
      print("PaymobSDK: Transaction accepted with details: \(transactionDetails)")
      self.SDKResult?(["status": "Successfull", "details": transactionDetails])
  }

  public func transactionPending() {
      print("PaymobSDK: Transaction pending")
      self.SDKResult?("Pending")
  }
}
