<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
<key>NSDataProtectionClass</key>
<string>NSFileProtectionComplete</string>
  <key>branch_universal_link_domains</key>
    		<array>
             <string>link.thrivve.me</string>
             <string>ttttttt3432.test-app.link</string>
             <string>ttttttt3432-alternate.test-app.link</string>
              <string>ttttttt3432-alternate.app.link</string>
               <string>app.thrivve.me</string>
              </array>
<key>UIDeviceFamily</key>
<array>
    <integer>1</integer> <!-- Forces iPhone UI -->
</array>
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>tel</string>
</array>
    <key>CFBundleURLTypes</key>
    		<array>
    			<dict>
    				<key>CFBundleTypeRole</key>
    				<string>Editor</string>
    				<key>CFBundleURLSchemes</key>
    				<array>
    					<string>thrivve</string>
    				</array>
    				<key>CFBundleURLName</key>
    				<string>com.app.thrivve.finance.thrivve.stg</string>
    			</dict>
    		</array>

    	<key>branch_key</key>
    		<dict>
    			<key>live</key>
    			<string>key_live_gsfXiriTHHLEGWxV8rdCzjkeyFpbXlFq</string>
    			<key>test</key>
    			<string>key_test_czk4ptnUQOTAIXyT5BcxCmflzuelWpzP</string>
    		</dict>
    <key>FirebaseAppDelegateProxyEnabled</key>
    <false/>
    <key>CFBundleIcons</key>
    <dict>
        <key>CFBundlePrimaryIcon</key>
        <dict>
            <key>CFBundleIconFiles</key>
            <array>
                <string>AppIcon_stag</string>
            </array>
        </dict>
    </dict>
    <key>CFBundleIcons~ipad</key>
    <dict>
        <key>CFBundlePrimaryIcon</key>
        <dict>
            <key>CFBundleIconFiles</key>
            <array>
                <string>AppIcon_stag</string>
            </array>
        </dict>
    </dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Thrivve Stage</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FDAllFilesDownloadedMessage</key>
	<string>All files have been downloaded</string>
	<key>FDMaximumConcurrentTasks</key>
	<integer>5</integer>
	<key>FirebaseMessagingAutoInitEnabled</key>
	<string>NO</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires access to the Microphone based on Apple new policy </string>
	
	<key>NSCameraUsageDescription</key>
	<string>This app requires access to Camera to be able take the photo for you to upload your profile photo.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>We use Face ID to securely authenticate you and provide access to your account. Your facial recognition data will only be used for authentication purposes and will not be stored or shared with third parties.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to the photo library to upload your profile photo.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>

</dict>
</plist>
