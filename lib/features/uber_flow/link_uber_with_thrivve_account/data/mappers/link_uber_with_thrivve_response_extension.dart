import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrive_response_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/entities/link_uber_with_thrive_response_entity.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/mappers/customer_extension.dart';

extension LinkUberWithThrivveExtension on LinkUberWithThrivveResponseModel {
  LinkUberWithThrivveResponseEntity toEntity() {
    return LinkUberWithThrivveResponseEntity(
        applicationVersion: applicationVersion,
        campaignName: campaignName,
        carRegistrationAttachmentUrl: carRegistrationAttachmentUrl,
        customer: customer?.toEntity(),
        customerOwnedCar: customerOwnedCar,
        drivingLicenceUrl: drivingLicenceUrl,
        hasUberAccount: hasUberAccount,
        id: id,
        identityDocumentUrl: identityDocumentUrl,
        status: status,
        uberAccountEmail: uberAccountEmail,
        uberAccountPhone: uberAccountPhone,
        uberLead: uberLead,
        uberLeadId: uberLeadId,
        vehicleId: vehicleId);
  }
}
