import 'package:dio/dio.dart';
import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/data_sources/i_link_uber_with_thrivve_data_source.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrive_response_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';

class LinkUberWithThrivveDataSourceImplementation
    extends LinkUberWithThrivveDataSource {
  final ApiClient apiClient;
  LinkUberWithThrivveDataSourceImplementation(this.apiClient);
  @override
  Future<LinkUberWithThrivveResponseModel?> linkUberWithThrivve(
      LinkUberWithThrivveRequestParamsModel params) async {
    final response = await apiClient.request(
        method: RequestType.post,
        data: FormData.fromMap(params.toJson()),
        endpoint: ApiSettings.linkUberWithThrivve,
        fromJson: (jsonResponse) =>
            linkUberWithThrivveResponseModelFromJson(jsonResponse));
    return response.data;
  }
}
