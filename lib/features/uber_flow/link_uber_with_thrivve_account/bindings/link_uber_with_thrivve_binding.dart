import 'package:get/get.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/data_sources/i_link_uber_with_thrivve_data_source.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/data_sources/link_uber_with_thrivve_data_source_implementation.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/repositories/i_link_uber_with_thrivve_repository.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/repositories/link_uber_with_thrivve_repository_implementation.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/use_cases/link_uber_with_thrivve_use_case.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/presentation/manager/link_uber_account_controller.dart';

class LinkUberWiththrivveBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LinkUberWithThrivveDataSource>(
        () => LinkUberWithThrivveDataSourceImplementation(getIt()));
    Get.lazyPut<LinkUberWithThrivveRepository>(
        () => LinkUberWithThrivveRepositoryImplentation(Get.find(), getIt()));
    Get.lazyPut<LinkUberWithThrivveUseCase>(
        () => LinkUberWithThrivveUseCase(Get.find()));
    Get.lazyPut<LinkUberAccountController>(
        () => LinkUberAccountController(Get.find(), Get.find(), getIt.get()));
  }
}
