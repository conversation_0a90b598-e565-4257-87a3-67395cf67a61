import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/entities/link_uber_with_thrive_response_entity.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/repositories/i_link_uber_with_thrivve_repository.dart';

class LinkUberWithThrivveUseCase extends UseCase<
    LinkUberWithThrivveResponseEntity?, LinkUberWithThrivveRequestParamsModel> {
  final LinkUberWithThrivveRepository linkUberWithThrivveRepository;
  LinkUberWithThrivveUseCase(this.linkUberWithThrivveRepository);
  @override
  Future<Either<Failure, LinkUberWithThrivveResponseEntity?>?> call(
      LinkUberWithThrivveRequestParamsModel params) {
    return linkUberWithThrivveRepository.linkUberWithThrivve(params);
  }
}
