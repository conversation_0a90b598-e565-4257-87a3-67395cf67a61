import 'package:get/get.dart';
import 'package:thrivve/core/extentions/safe_cast.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/iterable_compact_map.dart';
import 'package:thrivve/features/uber_flow/progress_step_uber_flow/domain/entities/progress_step_entity.dart';
import 'package:thrivve/features/uber_flow/progress_step_uber_flow/domain/use_cases/get_application_progress_use_case.dart';
import 'package:thrivve/features/uber_flow/progress_step_uber_flow/presentation/arguments/progress_page_arguments.dart';

class ProgressController extends GetxController {
  final GetApplicationProgressUseCase getApplicationProgress;
  final IPageLoadingDialog pageLoadingDialog;

  ProgressController(
    this.getApplicationProgress,
    this.pageLoadingDialog,
  );
  final RxList<ProgressStepEntity> steps = <ProgressStepEntity>[].obs;
  RxBool isLoading = false.obs;

  final _arguments = Rx<ProgressPageArgument?>(null);
  ProgressPageArgument? get arguments => _arguments.value;
  set arguments(ProgressPageArgument? newValue) => _arguments.value = newValue;

  @override
  void onInit() {
    super.onInit();
    arguments = safeCast<ProgressPageArgument>(Get.arguments);
    fetchProgress();
  }

  Future<void> fetchProgress() async {
    isLoading.value = true;
    final result = await getApplicationProgress.call(NoParams());
    result.fold(
      (failure) {
        isLoading.value = false;
        final error = mapFailureToMessage(failure);
        errorSnackBar(context: Get.context!, title: "err".tr, message: error);
      },
      (list) {
        isLoading.value = false;
        steps.value = list?.noneNullList() ?? [];
      },
    );
  }
}
