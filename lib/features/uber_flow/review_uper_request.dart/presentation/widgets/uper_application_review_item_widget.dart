import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/const.dart';

class UperApplicationReviewWidget extends StatelessWidget {
  final String value;
  final String title;
  const UperApplicationReviewWidget(this.title, this.value, {super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      key: ValueKey(title),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title.tr,
          style: TextStyle(
              color: context.black,
              fontSize: 14.sp,
              fontWeight: FontWeight.w400),
        ),
        SizedBox(
          height: 5.sp,
        ),
        Container(
          alignment: Get.locale?.languageCode == valueEnLanguage
              ? Alignment.centerLeft
              : Alignment.centerRight,
          height: 48.sp,
          width: double.infinity,
          margin: EdgeInsets.only(bottom: 15.sp),
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.sp),
              border: Border.all(color: context.textFieldBorderColoe)),
          child: Text(
            value,
            style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400),
          ),
        ),
      ],
    );
  }
}
