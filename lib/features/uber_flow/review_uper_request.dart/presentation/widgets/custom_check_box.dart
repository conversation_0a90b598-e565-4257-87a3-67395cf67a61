import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class CustomCheckbox extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final Key chekBoxKey;

  const CustomCheckbox(
      {required this.chekBoxKey, required this.value, required this.onChanged});

  @override
  _CustomCheckboxState createState() => _CustomCheckboxState();
}

class _CustomCheckboxState extends State<CustomCheckbox> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: widget.chekBoxKey,
      onTap: () => widget.onChanged(!widget.value),
      child: Container(
        width: 18.sp,
        height: 18.sp,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: Colors.grey, width: 2),
        ),
        child: widget.value
            ? Center(
                child: Container(
                  width: 11.sp,
                  height: 11.sp,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: context.black,
                  ),
                ),
              )
            : SizedBox(),
      ),
    );
  }
}
