import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class UperApplicationReviewImageWidget extends StatelessWidget {
  final String title;
  final String imageUrl;
  final bool showTitle;
  const UperApplicationReviewImageWidget(
    this.title,
    this.imageUrl, {
    super.key,
    this.showTitle = true,
  });
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return _buildReviewSection(context,
        title: title, height: 160, imageUrl: imageUrl, showTitle: showTitle);
  }

  Widget _buildReviewSection(BuildContext context,
      {required String title,
      required String imageUrl,
      required double height,
      required bool showTitle}) {
    final bool showingPdf = _isPdfFile(imageUrl);

    return Column(
      key: Value<PERSON><PERSON>(title),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            title.tr,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              color: context.black,
              fontSize: 14.sp,
            ),
          ),
          SizedBox(height: 8.sp),
        ],
        Container(
          clipBehavior: Clip.hardEdge,
          width: double.infinity,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: showingPdf
              ? _buildPdfPreview(imageUrl)
              : Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: SpinKitWave(
                        color: context.appPrimaryColor,
                        size: 20.0,
                        itemCount: 6,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline,
                            color: context.lightBlack, size: 32.w),
                      ],
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildPdfPreview(String? path) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: PDF(
        enableSwipe: false,
        pageSnap: false,
      ).fromUrl(
        path ?? "",
        placeholder: (progress) => Stack(
          children: [
            Container(
              color: Colors.grey[200],
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.picture_as_pdf, size: 48.w, color: Colors.grey),
                    SizedBox(height: 8.h),
                  ],
                ),
              ),
            ),
            Center(
              child: SpinKitWave(
                color: Get.context!.appPrimaryColor,
                size: 20.0,
                itemCount: 6,
              ),
            ),
          ],
        ),
        errorWidget: (error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: Colors.red, size: 32.w),
            ],
          ),
        ),
      ),
    );
  }

  bool _isPdfFile(String? url) {
    if (url == null) return false;

    try {
      // Parse the URL to get the path
      final uri = Uri.parse(url);
      final path = uri.path;

      // Get the last segment of the path (filename)
      final fileName = path.split('/').last;

      // Check the extension
      return fileName.toLowerCase().endsWith('.pdf');
    } catch (e) {
      print('Error parsing URL: $e');
      return false;
    }
  }
}
