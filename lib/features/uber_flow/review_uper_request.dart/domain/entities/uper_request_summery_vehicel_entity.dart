class UperApplicationSummeryVehicleEntity {
  final String? fuelType;
  final List<String>? carColors;
  final String? mainImage;
  final DateTime? deliveryDate;
  final String? totalPrice;
  final bool? isVerifiedByUber;
  final String? manufacturer;
  final int? vehicleId;
  final int? id;
  final String? title;
  final String? vehicleType;
  final String? motorSize;
  final String? carBrand;
  final List<String>? images;
  final String? gearType;
  final String? carName;
  final String? subTitle;
  final String? carMotorPower;
  final String? dailyPriceBetween;
  final String? productionYear;
  final String? manufacturerModel;
  final List<String>? notes;
  final num? rate;

  const UperApplicationSummeryVehicleEntity({
    this.fuelType,
    this.carColors,
    this.mainImage,
    this.deliveryDate,
    this.totalPrice,
    this.isVerifiedByUber,
    this.manufacturer,
    this.vehicleId,
    this.id,
    this.title,
    this.vehicleType,
    this.motorSize,
    this.carBrand,
    this.images,
    this.gearType,
    this.carName,
    this.subTitle,
    this.carMotorPower,
    this.dailyPriceBetween,
    this.productionYear,
    this.manufacturerModel,
    this.notes,
    this.rate,
  });

  UperApplicationSummeryVehicleEntity copyWith({
    String? fuelType,
    List<String>? carColors,
    String? mainImage,
    DateTime? deliveryDate,
    String? totalPrice,
    bool? isVerifiedByUber,
    String? manufacturer,
    int? vehicleId,
    int? id,
    String? title,
    String? vehicleType,
    String? motorSize,
    String? carBrand,
    List<String>? images,
    String? gearType,
    String? carName,
    String? subTitle,
    String? carMotorPower,
    String? dailyPriceBetween,
    String? productionYear,
    String? manufacturerModel,
    List<String>? notes,
    num? rate,
  }) {
    return UperApplicationSummeryVehicleEntity(
      fuelType: fuelType ?? this.fuelType,
      carColors: carColors ?? this.carColors,
      mainImage: mainImage ?? this.mainImage,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      totalPrice: totalPrice ?? this.totalPrice,
      isVerifiedByUber: isVerifiedByUber ?? this.isVerifiedByUber,
      manufacturer: manufacturer ?? this.manufacturer,
      vehicleId: vehicleId ?? this.vehicleId,
      id: id ?? this.id,
      title: title ?? this.title,
      vehicleType: vehicleType ?? this.vehicleType,
      motorSize: motorSize ?? this.motorSize,
      carBrand: carBrand ?? this.carBrand,
      images: images ?? this.images,
      gearType: gearType ?? this.gearType,
      carName: carName ?? this.carName,
      subTitle: subTitle ?? this.subTitle,
      carMotorPower: carMotorPower ?? this.carMotorPower,
      dailyPriceBetween: dailyPriceBetween ?? this.dailyPriceBetween,
      productionYear: productionYear ?? this.productionYear,
      manufacturerModel: manufacturerModel ?? this.manufacturerModel,
      notes: notes ?? this.notes,
      rate: rate ?? this.rate,
    );
  }
}
