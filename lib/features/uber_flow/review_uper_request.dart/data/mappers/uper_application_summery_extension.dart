import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/mappers/city_extension.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/mappers/uper_application_summery_vehicle_extension.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/mappers/uper_application_summery_vehicle_pricing_extension.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/mappers/uper_application_summery_vehicle_type_extension.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_application_summery_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/mapper/model_vehicle_extention.dart';

extension UperApplicationExtension on UperApplicationSummeryModel {
  UperApplicationSummeryEntity toEntity() {
    return UperApplicationSummeryEntity(
        age: age,
        averageWorkingTimeType: averageWorkingTimeType,
        campaignName: campaignName,
        captainMobile: captainMobile,
        captainName: captainName,
        carRegistrationAttachmentUrl: carRegistrationAttachmentUrl,
        city: city?.toEntity(),
        cityId: cityId,
        customerId: customerId,
        customerOwnedCar: customerOwnedCar,
        dateOfBirth: dateOfBirth,
        drivingLicenceUrl: drivingLicenceUrl,
        hasUberAccount: hasUberAccount,
        identityDocumentUrl: identityDocumentUrl,
        nationalId: nationalId,
        nationality: nationality,
        referralId: referralId,
        stepName: stepName,
        vehicleModelEntity: vehicleModel?.toEntity(),
        uberAccountEmail: uberAccountEmail,
        uberAccountPhone: uberAccountPhone,
        vehicleId: vehicleId,
        vehiclePricingId: vehiclePricingId,
        vehicleTypeId: vehicleTypeId,
        workVehicle: workVehicle,
        workingTime: workingTime,
        vehicle: vehicle?.toEntity(),
        vehiclePricing: vehiclePricing?.toEntity(),
        vehicleType: vehicleType?.toEntity());
  }
}
