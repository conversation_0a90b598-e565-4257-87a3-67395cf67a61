import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/data_source/i_submit_uper_application_data_source%20copy.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/submit_response_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_model.dart';

class SubmitUperApplicationDataSourceImplemnation
    extends SubmitUperApplicationDataSource {
  ApiClient apiClient;
  SubmitUperApplicationDataSourceImplemnation(this.apiClient);

  @override
  Future<SubmitUperResponseModel?> submitUperApplication() async {
    // TODO: implement submitUperApplication
    final response = await apiClient.request(
        method: RequestType.post,
        endpoint: ApiSettings.submitUperApplication,
        fromJson: (jsonResponse) =>
            submitUperApplicationResponseFromJson(jsonResponse));
    return response.data;
  }

  @override
  Future<UperApplicationSummeryModel?> getUperRequestSummery() async {
    // TODO: implement getUperRequestSummery
    final response = await apiClient.request(
        endpoint: ApiSettings.getUperApplicationSummery,
        method: RequestType.get,
        fromJson: (json) => uperApplicationSummeryFromJson(json));
    return response.data;
  }
}
