import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/data_sources/i_uber_partner_data_source.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/mapper/benefits_model_extention.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/models/create_work_with_uber_model.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/entities/benefits_and_tem_entity.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/repositories/i_uber_partner_repository.dart';

class UberPartnerRepositoryImpl implements IUberPartnerRepository {
  final IUberPartnerDataSource remoteDataSource;
  final NetworkInfo? networkInfo;

  UberPartnerRepositoryImpl(
    this.remoteDataSource,
    this.networkInfo,
  );

  @override
  Future<Either<Failure, BenefitsAndTermEntity?>?> getBenefitsAndTerms() async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await remoteDataSource.getBenefitsAndTerms();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, CreateWorkWithUberModel?>>? createWorkWithUber(
      Map<String, dynamic> input) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await remoteDataSource.createWorkWithUber(input);
        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
