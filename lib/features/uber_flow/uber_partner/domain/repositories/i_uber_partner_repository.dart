import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/models/create_work_with_uber_model.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/entities/benefits_and_tem_entity.dart';

abstract class IUberPartnerRepository {
  Future<Either<Failure, BenefitsAndTermEntity?>?> getBenefitsAndTerms();
  Future<Either<Failure, CreateWorkWithUberModel?>>? createWorkWithUber(
      Map<String, dynamic> input);
}
