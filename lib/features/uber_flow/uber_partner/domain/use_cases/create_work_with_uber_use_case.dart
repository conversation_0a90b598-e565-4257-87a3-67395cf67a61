import 'package:dartz/dartz.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/models/create_work_with_uber_model.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/repositories/i_uber_partner_repository.dart';

import '../../../../../core/error/failures.dart';
import '../../../../../core/user_cases/user_case.dart';

class CreateWorkWithUberUseCase
    implements UseCase<CreateWorkWithUberModel?, Map<String, dynamic>> {
  final IUberPartnerRepository? repository;

  CreateWorkWithUberUseCase({this.repository});

  @override
  Future<Either<Failure, CreateWorkWithUberModel?>?> call(
      Map<String, dynamic> input) async {
    return await repository?.createWorkWithUber(input);
  }
}
