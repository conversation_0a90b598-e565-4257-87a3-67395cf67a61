import 'package:dartz/dartz.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/entities/benefits_and_tem_entity.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/repositories/i_uber_partner_repository.dart';

import '../../../../../core/error/failures.dart';
import '../../../../../core/user_cases/user_case.dart';

class GetUberBenefitsAndTermUseCase
    implements UseCase<BenefitsAndTermEntity?, NoParams> {
  final IUberPartnerRepository iUberPartnerRepository;

  GetUberBenefitsAndTermUseCase({
    required this.iUberPartnerRepository,
  });

  @override
  Future<Either<Failure, BenefitsAndTermEntity?>?> call(NoParams noParams) {
    return iUberPartnerRepository.getBenefitsAndTerms();
  }
}
