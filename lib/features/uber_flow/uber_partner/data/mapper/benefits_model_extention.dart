import 'package:thrivve/features/uber_flow/uber_partner/data/models/benifites_and_term_model.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/entities/benefits_and_tem_entity.dart';

extension BenefitsModelExtention on BenefitsAndTermModel {
  BenefitsAndTermEntity toEntity() {
    return BenefitsAndTermEntity(
      termsAndCondition: termsAndCondition,
      benefits: benefits,
    );
  }
}
