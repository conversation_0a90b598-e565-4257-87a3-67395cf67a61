import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/widget/border_button.dart';
import 'package:thrivve/core/widget/button.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/top_bar_widget.dart';
import 'package:thrivve/generated/assets.dart';

class WattingSheetWidget extends StatelessWidget {
  const WattingSheetWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context)
              .bottom, // Adjust for the keyboard
        ),
        child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0.r),
              topRight: Radius.circular(20.0.r),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const TopBar(),
              SizedBox(height: 16.h),
              Container(
                padding: EdgeInsets.all(15.sp),
                height: 60.sp,
                width: 60.sp,
                decoration: BoxDecoration(
                    color: context.iconBackgroundColor, shape: BoxShape.circle),
                child: Image.asset(
                  Assets.thrivvePhotosInfoUber,
                  color: context.black,
                ),
              ),
              SizedBox(height: 28.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomTextWidget(
                    title: 'have_application_under_review'.tr,
                    fontWeight: FontWeight.w700,
                    color: context.black,
                    size: 15,
                    textAlign: TextAlign.center,
                    paddingStart: 16.w,
                    paddingEnd: 16.w,
                  ),
                ],
              ),
              SizedBox(height: 8.sp),
              CustomTextWidget(
                title: 'you_already_have_application'.tr,
                fontWeight: FontWeight.w500,
                color: context.black.withValues(alpha: 0.6),
                textAlign: TextAlign.center,
                paddingStart: 16.w,
                paddingEnd: 16.w,
                height: 1.3,
                size: 12,
              ),
              SizedBox(height: 28.sp),
              Button(
                text: "got_it".tr,
                fontWeight: FontWeight.w600,
                fontSize: 11.sp,
                height: 32.h,
                onTab: () {
                  reinitializeBloc();
                  Get.offAllNamed(AppRoutes.homePage);
                },
              ),
              SizedBox(height: 16.h),
              BorderButton(
                text: "follow_application_status".tr,
                fontWeight: FontWeight.w600,
                fontSize: 11.sp,
                height: 32.h,
                onTab: () {
                  final message = getIt<DashboardBloc>().state.homePageMessage;
                  if (message != null) {
                    goToProgressApplication(message);
                  }
                },
              ),
              SizedBox(height: 16.h),
            ],
          ),
        ),
      ),
    );
  }
}

void showWattingSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: context.containerColor,
    builder: (context) => WattingSheetWidget(),
  );
}
