import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class BackIcon extends StatelessWidget {
  final VoidCallback? onClickBack;
  final bool removeSpacing;
  const BackIcon({
    super.key,
    this.onClickBack,
    this.removeSpacing = false,
  });
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Row(
      children: [
        if (removeSpacing.inverted)
          SizedBox(
            width: 10.sp,
          ),
        Container(
          height: 40.h,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.appBackgroundColor,
          ),
          child: InkWell(
            onTap: onClickBack ??
                () {
                  Get.back();
                },
            child: Icon(
              Icons.arrow_back_rounded,
              color: context.black,
              size: 20.w,
            ),
          ),
        ),
      ],
    );
  }
}
