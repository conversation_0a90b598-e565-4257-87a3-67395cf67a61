import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/widgets/decorations.dart';

class CustomTextFormField extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final String? label;
  final TextStyle? labelStyle;
  final String? Function(String?)? validator;
  final Function()? ontab;
  final TextInputType inputType;
  final bool readonly;
  final Widget? suffixWidget;
  final Widget? prefixWidget;
  final TextAlign textAlign;
  final Key? textFieldKey;
  final int? length;
  final bool hideErrorText;
  final FocusNode? focusNode;
  final TextDirection? textDirection;
  final double? radius;
  final double? height;
  final double? hintSize;
  final Color? borderColor;
  final bool enablePhoneFormatting;
  final bool showClearButton;
  final bool alwaysLtr;
  final Function(String)? onChanged;

  const CustomTextFormField({
    super.key,
    this.textFieldKey,
    required this.controller,
    required this.hintText,
    this.textDirection,
    this.label,
    this.focusNode,
    this.labelStyle,
    this.validator,
    this.textAlign = TextAlign.start,
    this.ontab,
    this.onChanged,
    this.prefixWidget,
    this.suffixWidget,
    this.alwaysLtr = false,
    this.readonly = false,
    this.length,
    this.inputType = TextInputType.text,
    this.hideErrorText = false,
    this.height,
    this.hintSize,
    this.borderColor,
    this.radius,
    this.enablePhoneFormatting = false,
    this.showClearButton = false,
  });

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  bool hasValidationError = false;
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _hasBeenBlurred = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
    widget.controller.addListener(_handleTextChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    widget.controller.removeListener(_handleTextChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _handleTextChange() {
    if (widget.inputType == TextInputType.phone) {
      if (widget.controller.text.isEmpty) {
        setState(() {
          hasValidationError = false;
          _hasBeenBlurred = false;
        });
      } else if (_hasBeenBlurred) {
        final error = widget.validator?.call(widget.controller.text);
        setState(() {
          hasValidationError = error != null;
        });
      }
    } else {
      // Original logic for non-phone fields (show error if validator returns error)
      final error = widget.validator?.call(widget.controller.text);
      setState(() {
        hasValidationError = error != null;
      });
    }
  }

  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    if (widget.inputType == TextInputType.phone) {
      if (!_focusNode.hasFocus) {
        _hasBeenBlurred = true;
        if (widget.controller.text.isNotEmpty) {
          final error = widget.validator?.call(widget.controller.text);
          setState(() {
            hasValidationError = error != null;
          });
        }
      }
    } else {
      // Original logic for non-phone fields (show error if validator returns error)
      if (!_focusNode.hasFocus) {
        final error = widget.validator?.call(widget.controller.text);
        setState(() {
          hasValidationError = error != null;
        });
      }
    }
  }

  String _formatPhoneNumber(String text) {
    if (!widget.enablePhoneFormatting) return text;

    // Remove all non-digit characters
    String digits = text.replaceAll(RegExp(r'[^\d]'), '');

    // Format the number according to the pattern (xx) xxx xx xx
    if (digits.length <= 2) {
      return digits;
    } else if (digits.length <= 5) {
      return '(${digits.substring(0, 2)}) ${digits.substring(2)}';
    } else if (digits.length <= 7) {
      return '(${digits.substring(0, 2)}) ${digits.substring(2, 5)} ${digits.substring(5)}';
    } else {
      return '(${digits.substring(0, 2)}) ${digits.substring(2, 5)} ${digits.substring(5, 7)} ${digits.substring(7, min(9, digits.length))}';
    }
  }

  void _onChanged(String value) {
    if (!widget.enablePhoneFormatting) return;

    // Get the current cursor position
    int cursorPosition = widget.controller.selection.baseOffset;

    // Count the number of formatting characters before the cursor
    int formattingCharsBeforeCursor = 0;
    for (int i = 0; i < cursorPosition; i++) {
      if (widget.controller.text[i] == '(' ||
          widget.controller.text[i] == ')' ||
          widget.controller.text[i] == ' ') {
        formattingCharsBeforeCursor++;
      }
    }

    // Format the number
    String formatted = _formatPhoneNumber(value);

    // Calculate the new cursor position
    int newCursorPosition = cursorPosition;
    if (formatted.length > widget.controller.text.length) {
      // Adding characters
      newCursorPosition += (formatted.length - widget.controller.text.length);
    } else if (formatted.length < widget.controller.text.length) {
      // Removing characters
      newCursorPosition -= (widget.controller.text.length - formatted.length);
    }

    // Update the text and cursor position
    widget.controller.text = formatted;
    widget.controller.selection = TextSelection.fromPosition(
      TextPosition(offset: newCursorPosition),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.label!.tr,
                style: widget.labelStyle ??
                    TextStyle(
                        color: context.black,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400),
              ),
              SizedBox(height: 5.sp),
            ],
          ),
        widget.alwaysLtr
            ? Directionality(
                textDirection: TextDirection.ltr,
                child: _buildTextField(context))
            : _buildTextField(context),
        if (hasValidationError && !widget.hideErrorText)
          Text(
            widget.validator?.call(widget.controller.text) ?? '',
            style: TextStyle(
              fontWeight: FontWeight.w400,
              color: Colors.red,
              fontSize: 12.sp,
            ),
          ),
      ],
    );
  }

  Widget _buildTextField(BuildContext context) {
    return TextFormField(
      buildCounter: (context,
              {required currentLength,
              required isFocused,
              required maxLength}) =>
          SizedBox.shrink(),
      maxLength: widget.enablePhoneFormatting ? 9 : widget.length,
      key: widget.textFieldKey,
      textAlign: widget.textAlign,
      autovalidateMode: AutovalidateMode.disabled, // Disable auto validation
      onTap: widget.ontab,

      readOnly: widget.readonly,
      keyboardType: widget.inputType,
      controller: widget.controller,
      focusNode: _focusNode,
      style: baseTextStyle(),
      onChanged: widget.onChanged ?? _onChanged,
      inputFormatters: widget.enablePhoneFormatting
          ? [FilteringTextInputFormatter.digitsOnly]
          : null,
      decoration: baseInputDecoration(
              context,
              _isFocused ? '' : widget.hintText.tr,
              false,
              widget.radius,
              widget.height,
              hasValidationError ? Colors.red : widget.borderColor)
          .copyWith(
        labelStyle: widget.labelStyle,
        prefixIcon: widget.prefixWidget,
        suffixIcon: widget.showClearButton && widget.controller.text.isNotEmpty
            ? IconButton(
                icon: Icon(
                  Icons.clear,
                  size: 20.sp,
                  color: context.black,
                ),
                onPressed: () {
                  widget.controller.clear();
                  setState(() {
                    if (widget.inputType == TextInputType.phone) {
                      hasValidationError = false;
                      _hasBeenBlurred = false;
                    } else {
                      hasValidationError = false;
                    }
                  });
                },
              )
            : widget.suffixWidget,
      ),
    ).marginOnly(bottom: 5.sp);
  }
}
