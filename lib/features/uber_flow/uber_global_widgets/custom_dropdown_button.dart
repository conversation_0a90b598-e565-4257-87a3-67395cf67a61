import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/widgets/decorations.dart';

class CustomDropdownFormField<T> extends StatelessWidget {
  final Key? dropDownKey;
  final List<T> items;
  final T? value;
  final String hint;
  final String label;
  final ValueChanged<T?>? onChanged;
  final String? Function(T?)? validator;

  const CustomDropdownFormField({
    super.key,
    required this.items,
    required this.value,
    required this.label,
    required this.hint,
    required this.onChanged,
    this.validator,
    this.dropDownKey,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(context),
        SizedBox(height: 5.sp),
        _buildDropdown(context, dropDownKey),
      ],
    );
  }

  /// Builds the label for the dropdown
  Widget _buildLabel(BuildContext context) {
    return Text(
      label.tr,
      style: TextStyle(
        fontWeight: FontWeight.w400,
        color: context.black,
        fontSize: 14.sp,
      ),
    );
  }

  /// Builds the main dropdown widget
  Widget _buildDropdown(BuildContext context, Key? key) {
    return DropdownButtonFormField2<T>(
      key: key,
      value: value,
      isExpanded: true,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: validator,
      onChanged: onChanged,
      iconStyleData: IconStyleData(
        icon: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.sp),
          child: Icon(Icons.keyboard_arrow_down_sharp),
        ),
      ),
      decoration: baseInputDecoration(context, hint, true),
      dropdownStyleData: DropdownStyleData(
        maxHeight: 250,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          color: context.bottomsheetColor,
        ),
        padding: const EdgeInsets.symmetric(vertical: 10),
      ),
      hint: Text(
        hint.tr,
        style:
            TextStyle(color: context.black.withOpacity(0.6), fontSize: 12.sp),
      ),
      selectedItemBuilder: (context) =>
          items.map((item) => _buildSelectedItem(context, item)).toList(),
      items: items.map((item) => _buildDropdownItem(item)).toList(),
    ).marginOnly(bottom: 15.sp);
  }

  /// Builds selected item text styling
  Widget _buildSelectedItem(BuildContext context, T item) {
    return Text(
      item.toString().tr,
      style: baseTextStyle(),
    );
  }

  /// Builds dropdown menu items
  DropdownMenuItem<T> _buildDropdownItem(T item) {
    bool isSelected = item == value;
    return DropdownMenuItem<T>(
      key:  ValueKey(item.toString().tr),
      value: item,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            item.toString().tr,
            style: baseTextStyle(),
          ),
          if (isSelected) const Icon(Icons.done),
        ],
      ),
    );
  }
}
