import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/services/config_service.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/uber_flow/info_supplier/presentation/manager/info_supplier_controller.dart';

class InfoSupplierPage extends GetView<InfoSupplierController> {
  const InfoSupplierPage({
    super.key,
  });
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // Semi-transparent background
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              color: Colors.black.withValues(alpha: 0.3),
            ),
          ),

          // Bottom Sheet Content
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: context.bottomsheetColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: IntrinsicHeight(
                child: Obx(
                  () => Column(
                    children: [
                      // Handle bar
                      if (controller.infoSupplier != null &&
                          controller.isLoading.value.inverted) ...[
                        SizedBox(height: 16.h),
                        _buildCloseButton(context),
                        SizedBox(height: 28.sp),
                        _header(context),
                        SizedBox(height: 40.sp),
                        _address(context),
                        SizedBox(height: 8.h),
                        _mapPhoto(),
                        _timeWorking(context),
                        SizedBox(height: 40.h),
                        _phones(context),
                        SizedBox(height: 40.h),
                        _okButton(),
                        SizedBox(height: 24.h),
                      ],
                      if (controller.isLoading.value)
                        Center(
                          child: SpinKitWave(
                            size: 30.sp,
                            color: context.appPrimaryColor,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(width: 16.w),
        Container(
          height: 40.h,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.appBackgroundColor,
          ),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.close,
              color: context.black,
              size: 20.w,
            ),
          ),
        ),
      ],
    );
  }

  Widget _header(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CustomTextWidget(
          title: 'car_pickup_details'.tr,
          fontWeight: FontWeight.w600,
          size: 15,
          textAlign: TextAlign.center,
          color: context.black,
        ),
        SizedBox(height: 12.h),
        CustomTextWidget(
          textAlign: TextAlign.center,
          title:
              '${'you_can_pickup_the_car_form'.tr} ${controller.infoSupplier.value?.name ?? ''}',
          fontWeight: FontWeight.w400,
          size: 12,
          color: context.black,
        ),
      ],
    );
  }

  Widget _address(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextWidget(
          paddingStart: 16.w,
          title: 'address'.tr,
          fontWeight: FontWeight.w700,
          size: 12,
          color: context.black,
        ),
        SizedBox(height: 8.h),
        CustomTextWidget(
          paddingStart: 16.w,
          title: controller.infoSupplier.value?.address ?? '',
          fontWeight: FontWeight.w400,
          size: 11,
          color: context.black,
          maxLine: 2,
        ),
      ],
    );
  }

  Widget _mapPhoto() {
    final url = 'https://maps.googleapis.com/maps/api/staticmap?'
        'center=${controller.infoSupplier.value?.latitude},${controller.infoSupplier.value?.longitude}'
        '&zoom=15'
        '&size=600x300'
        '&scale=2' // For higher resolution
        '&markers=color:red|${controller.infoSupplier.value?.latitude},${controller.infoSupplier.value?.longitude}'
        '&key=${ConfigService.googleMapsApiKey}';
    return GestureDetector(
      onTap: controller.clickInMap,
      child: Container(
        padding: EdgeInsetsDirectional.only(
          start: 8.w,
          end: 8.w,
        ),
        width: double.infinity,
        height: 167.h,
        child: CachedNetworkImage(imageUrl: url),
      ),
    );
  }

  Widget _phones(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextWidget(
          title:
              '${'to_contact'.tr} ${controller.infoSupplier.value?.name ?? ''}',
          fontWeight: FontWeight.w700,
          size: 12,
          paddingStart: 16.w,
          color: context.black,
        ),
        SizedBox(height: 8.h),
        ...(controller.infoSupplier.value?.telephone ?? []).map((item) {
          return GestureDetector(
            onTap: () => controller.dialPhone(item),
            child: Padding(
              padding: EdgeInsets.only(
                top: 8.h,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: 16.w),
                  Icon(
                    Icons.call_outlined,
                    color: context.black,
                    size: 14.w,
                  ),
                  SizedBox(width: 8.w),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: CustomTextWidget(
                      title: item,
                      fontWeight: FontWeight.w400,
                      size: 11,
                      height: null,
                      color: context.black,
                    ),
                  ),
                ],
              ),
            ),
          );
        })
      ],
    );
  }

  Widget _timeWorking(BuildContext context) {
    return Visibility(
      visible:
          (controller.infoSupplier.value?.openingWeekDays ?? '').isNotEmpty ||
              (controller.infoSupplier.value?.openingTime ?? '').isNotEmpty,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 40.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CustomTextWidget(
                title: 'time_working_title'.tr,
                fontWeight: FontWeight.w700,
                size: 12,
                paddingStart: 16.w,
                color: context.black,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if ((controller.infoSupplier.value?.openingWeekDays ?? '').isNotEmpty)
            CustomTextWidget(
              title:
                  '📅  ${controller.infoSupplier.value?.openingWeekDays ?? ''}',
              size: 11,
              paddingStart: 16.w,
              fontWeight: FontWeight.w400,
              color: context.black,
            ),
          if ((controller.infoSupplier.value?.openingTime ?? '').isNotEmpty)
            SizedBox(height: 8.h),
          if ((controller.infoSupplier.value?.openingTime ?? '').isNotEmpty)
            CustomTextWidget(
              title: '⏰  ${controller.infoSupplier.value?.openingTime ?? ''}',
              size: 11,
              paddingStart: 16.w,
              fontWeight: FontWeight.w400,
              color: context.black,
            )
        ],
      ),
    );
  }

  Widget _okButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: CustomButton(
          text: 'got_it'.tr,
          onPressed: () {
            Get.back();
          }),
    );
  }
}
