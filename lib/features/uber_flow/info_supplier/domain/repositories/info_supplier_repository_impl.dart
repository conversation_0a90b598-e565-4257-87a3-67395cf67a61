import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/uber_flow/info_supplier/data/data_sources/i_info_supplier_data_source.dart';
import 'package:thrivve/features/uber_flow/info_supplier/data/mapper/info_supplier_extention.dart';
import 'package:thrivve/features/uber_flow/info_supplier/domain/entities/info_supplier_entity.dart';
import 'package:thrivve/features/uber_flow/info_supplier/domain/repositories/i_info_supplier_repository.dart';

class InfoSupplierRepositoryImpl implements IInfoSupplierRepository {
  final IInfoSupplierDataSource remoteDataSource;
  final NetworkInfo? networkInfo;

  InfoSupplierRepositoryImpl(
    this.remoteDataSource,
    this.networkInfo,
  );

  @override
  Future<Either<Failure, InfoSupplierEntity?>> getInfoSupplierData() async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await remoteDataSource.getInfoSupplierData();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
