import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/manager/kyc_controller.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/widgets/notes_widget.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/widgets/upload_widgets.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/widgets/validators.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/arguments/select_vehicles_page_arguments.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/back_icon.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/custom_dropdown_button.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/custom_textfield.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/indicators_widget.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/next_button.dart';

class KYCPage extends StatefulWidget {
  @override
  State<KYCPage> createState() => _KYCPageState();
}

class _KYCPageState extends State<KYCPage> {
  final KYCController controller = Get.find<KYCController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: context.whiteColor,
          surfaceTintColor: context.whiteColor,
          leading: BackIcon(
            onClickBack: _onClickBack,
          ),
          leadingWidth: 50.sp,
          toolbarHeight: 80,
        ),
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Form(
            key: controller.formKey,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  IndicatorsWidget(2),
                  Expanded(
                    child: SingleChildScrollView(
                      key: ValueKey("kyc_SingleChildScrollView"),
                      child: Column(
                        children: [
                          _buildTitle(),
                          SizedBox(
                            height: 30.sp,
                          ),
                          ..._buildFormFields(controller),
                          UploaderWidgets(),
                          SizedBox(
                            height: 100.sp,
                          ),
                          UperNotesWidget(),
                          Obx(() {
                            return NextButton(
                              buttonKey: ValueKey("uber_kyc_next_button"),
                              pressFunction: controller.submitForm,
                              title: "next".tr,
                              isEnabled: controller.isNextButtonEnabled.value,
                            );
                          }),
                          SizedBox(
                            height: 20,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _buildTitle() {
    return CustomTextWidget(
      title: "upload_document_title".tr,
      fontWeight: FontWeight.w600,
      size: 22,
    );
  }

  List<Widget> _buildFormFields(KYCController controller) {
    return [
      _buildTextField(
        textFieldKey: ValueKey("uber_kyc_customer_name_text_field"),
        label: "name",
        hintText: "name_example",
        controller: controller.documentCustomerNameController,
        validator: Validators.validateNotEmpty,
        inputType: TextInputType.text,
      ),
      _buildDropdownField(
        key: ValueKey("uber_kyc_customer_nationality_dropdown"),
        label: "nationality",
        hint: "nationality_example",
        items: controller.nationalityOp,
        selectedValue: controller.selectedNationality,
      ),

      _buildTextField(
        textFieldKey: ValueKey("uber_kyc_customer_national_id_text_field"),
        label: "identification",
        hintText: "identification_example",
        controller: controller.documentCustomerIdController,
        validator: Validators.validateNumbersOnly,
        inputType: TextInputType.number,
      ),
      Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "date_of_birth".tr,
                style: TextStyle(
                    color: Get.context?.black,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400),
              ),
              Text(
                "date_calender_note".tr,
                style: TextStyle(
                    color: Get.context?.black,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400),
              ),
            ],
          ),
        ],
      ),
      SizedBox(
        height: 8.sp,
      ),
      SizedBox(
        height: 60.sp,
        child: Row(
          children: [
            Expanded(
              child: _buildTextField(
                hideErrorText: true,
                focusNode: controller.dayFocusNode,
                length: 2,
                suffixWidget: Icon(Icons.calendar_today),
                textFieldKey:
                    ValueKey("uber_kyc_customer_national_id_text_field"),
                hintText: "day",
                controller: controller.dayController,
                validator: (value) {
                  // First validate the day format
                  final dayError = Validators.validateDays(value);
                  if (dayError != null) return dayError;

                  // Then validate day against month if month exists
                  if (controller.monthController.text.isNotEmpty) {
                    return Validators.validateDayForMonth(
                        value, controller.monthController.text);
                  }
                  return null;
                },
                inputType: TextInputType.number,
              ),
            ),
            SizedBox(
              width: 10.sp,
            ),
            Expanded(
              child: _buildTextField(
                hideErrorText: true,
                focusNode: controller.monthFocusNode,
                length: 2,
                suffixWidget: Icon(Icons.calendar_today),
                textFieldKey:
                    ValueKey("uber_kyc_customer_national_id_text_field"),
                hintText: "month",
                controller: controller.monthController,
                validator: Validators.validateMonths,
                inputType: TextInputType.number,
              ),
            ),
            SizedBox(
              width: 10.sp,
            ),
            Expanded(
              child: _buildTextField(
                hideErrorText: true,
                length: 4,
                suffixWidget: Icon(Icons.calendar_today),
                textFieldKey:
                    ValueKey("uber_kyc_customer_national_id_text_field"),
                hintText: "year",
                controller: controller.yearController,
                validator: Validators.validateYears,
                inputType: TextInputType.number,
              ),
            ),
          ],
        ),
      ),

      ///////////////////////
      Obx(() {
        return _buildDropdownField(
          key: ValueKey("uber_kyc_customer_city_dropdown"),
          label: "city",
          hint: "city_example",
          items: controller.cities.value,
          selectedValue: controller.selectedCity,
        );
      }),
      _buildDropdownField(
        key: ValueKey("uber_kyc_customer_experience_dropdown"),
        label: "delivery_work_time",
        hint: "delivery_work_time_example",
        items: controller.workingExpOp,
        selectedValue: controller.selectedWorkingExpOp,
      ),
      _buildDropdownField(
        key: ValueKey("uber_kyc_customer_hours_avg_dropdown"),
        label: "weekly_hours_average",
        hint: "weekly_hours_average_example",
        items: controller.averageWorkingHoursOp,
        selectedValue: controller.selectedAverageWorkingHoursOp,
      ),
    ];
  }

  Widget _buildTextField(
      {String? label,
      required String hintText,
      required TextEditingController controller,
      String? Function(String?)? validator,
      TextInputType inputType = TextInputType.text,
      bool readOnly = false,
      VoidCallback? onTap,
      Widget? suffixWidget,
      int? length,
      FocusNode? focusNode,
      Key? textFieldKey,
      bool hideErrorText = false}) {
    return CustomTextFormField(
      focusNode: focusNode,
      length: length,
      suffixWidget: suffixWidget,
      readonly: readOnly,
      validator: validator,
      label: label,
      controller: controller,
      hintText: hintText,
      inputType: inputType,
      ontab: onTap,
      key: textFieldKey,
      hideErrorText: hideErrorText,
    );
  }

  Widget _buildDropdownField<T>({
    Key? key,
    required String label,
    required String hint,
    required List<T> items,
    required Rxn<T> selectedValue,
  }) {
    return Obx(() {
      return CustomDropdownFormField<T>(
        key: key,
        validator: Validators.validateDropdown,
        items: items,
        value: selectedValue.value,
        label: label,
        hint: hint,
        onChanged: (x) => selectedValue.value = x,
      );
    });
  }

  void _onClickBack() {
    if (controller.uploadUberDocsArgument?.fromDashboard ?? false) {
      final args = SelectVehiclesPageArguments(
          withUberAllDataEntity:
              controller.uploadUberDocsArgument?.dataWorkWithUber,
          fromDashboard: true,
          showPriceBottomsheet: false);
      Get.offNamed(
        AppRoutes.selectVehiclesUberFlowPage,
        arguments: args,
      );
    } else {
      Get.back(result: false);
    }
  }
}
