import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

TextStyle baseTextStyle() {
  return TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500);
}

InputDecoration baseInputDecoration(BuildContext context, String hint,
    [bool isDropDown = false,
    double? borderRadius,
    double? padding,
    Color? borderColor]) {
  return InputDecoration(
    hintText: hint.tr,
    hintStyle: TextStyle(
        color: context.black.withOpacity(0.4),
        fontSize: 12.sp,
        fontWeight: FontWeight.w400),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius ?? 6.sp),
      borderSide:
          BorderSide(color: borderColor ?? context.textFieldBorderColoe),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius ?? 6.sp),
      borderSide:
          BorderSide(color: borderColor ?? context.textFieldBorderColoe),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius ?? 6.sp),
      borderSide: BorderSide(color: context.black),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius ?? 6.sp),
      borderSide: BorderSide(color: context.errorColorBorder, width: 2),
    ),
    contentPadding: EdgeInsets.symmetric(
        horizontal: isDropDown ? 0 : 12.sp, vertical: padding ?? 10.sp),
  );
}
