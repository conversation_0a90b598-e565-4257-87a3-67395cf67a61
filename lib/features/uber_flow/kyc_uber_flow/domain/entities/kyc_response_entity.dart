import 'package:equatable/equatable.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/customer_entity.dart';

class KYCSubmitResponse extends Equatable {
  final String? uberAccountEmail;
  final String? identityDocumentUrl;
  final String? status;
  final String? campaignName;
  final int? applicationVersion;
  final bool? hasUberAccount;
  final String? drivingLicenceUrl;
  final bool? customerOwnedCar;
  final String? uberLead;
  final CustomerEntity? customer;
  final String? carRegistrationAttachmentUrl;
  final String? uberAccountPhone;
  final int? id;
  final int? vehicleId;
  final int? uberLeadId;

  KYCSubmitResponse(
      {this.uberAccountEmail,
      this.identityDocumentUrl,
      this.status,
      this.campaignName,
      this.applicationVersion,
      this.hasUberAccount,
      this.drivingLicenceUrl,
      this.customerOwnedCar,
      this.uberLead,
      this.customer,
      this.carRegistrationAttachmentUrl,
      this.uberAccountPhone,
      this.id,
      this.vehicleId,
      this.uberLeadId});

  @override
  // TODO: implement props
  List<Object?> get props => [id];
}
