import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/city_entity.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/kyc_response_entity.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/kyc_submit_input_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_application_summery_entity.dart';

abstract class KYCRepository {
  Future<Either<Failure, List<CityEntity?>?>> getAllCities();
  Future<Either<Failure, KYCSubmitResponse?>> submitKYC(
      KYCSubmitArguments uploadDocumentInputEntity);

  Future<Either<Failure, UperApplicationSummeryEntity?>>
      getUperRequestSummery();
}
