import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/kyc_response_entity.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/kyc_submit_input_entity.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/repositories/i_kyc_repository.dart';

class KYCSubmitUseCase
    implements UseCase<KYCSubmitResponse?, KYCSubmitArguments> {
  final KYCRepository uploadDocumentRepository;
  KYCSubmitUseCase(this.uploadDocumentRepository);

  @override
  Future<Either<Failure, KYCSubmitResponse?>?> call(KYCSubmitArguments params) {
    // TODO: implement call
    return uploadDocumentRepository.submitKYC(params);
  }
}
