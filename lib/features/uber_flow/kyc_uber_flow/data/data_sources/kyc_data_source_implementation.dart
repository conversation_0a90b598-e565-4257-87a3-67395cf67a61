import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/data_sources/i_kyc_source_implementation.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/models/city_model.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/models/kyc_submition_response_model.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/kyc_submit_input_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_model.dart';

class KYCDataSourceImplementation extends KYCDataSource {
  final ApiClient apiClient;
  KYCDataSourceImplementation(this.apiClient);
  @override
  Future<List<CityModel?>?> getAllCities() async {
    final response = await apiClient.request<List<CityModel>>(
        endpoint: ApiSettings.getAllCitiesForKyc,
        fromJson: (json) => listOfCityModelFromJson(json));
    return response.data;
  }

  @override
  Future<KYCSubmitResponseModel?> uploadDocuments(
      KYCSubmitArguments uploadDocumentInputEntity) async {
    final response = await apiClient.request<KYCSubmitResponseModel>(
        endpoint: ApiSettings.uploadDocumentForUper,
        method: RequestType.post,
        data: uploadDocumentInputEntity.toJson(),
        fromJson: (json) => upladDocumentResponseFromJson(json));
    return response.data;
  }

  @override
  Future<UperApplicationSummeryModel?> getUperRequestSummery() async {
    // TODO: implement getUperRequestSummery
    final response = await apiClient.request(
        endpoint: ApiSettings.getUperApplicationSummery,
        method: RequestType.get,
        fromJson: (json) => uperApplicationSummeryFromJson(json));
    return response.data;
  }
}
