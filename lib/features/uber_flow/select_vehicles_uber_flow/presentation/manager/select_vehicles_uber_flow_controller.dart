import 'package:flutter/cupertino.dart';
import 'package:flutter_pagewise/flutter_pagewise.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/safe_cast.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/show_bottom_sheet_input.dart';
import 'package:thrivve/core/pagination.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/iterable_compact_map.dart';
import 'package:thrivve/features/listOfVehicles/domain/entities/vehicle_list_object.dart';
import 'package:thrivve/features/listOfVehicles/domain/use_cases/get_list_of_vehicles_use_case.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/mapper/model_vehicle_extention.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/mapper/pricing_vehicle_extention.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/pricing_vehicle_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/save_vehicle_step_input_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/vehicle_model_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/use_cases/get_list_models_vehicle_use_case.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/use_cases/get_list_pricing_vehicles_use_case.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/use_cases/save_vehicle_step_use_case.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/arguments/select_vehicles_page_arguments.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/widgets/rental_pricing_sheet.dart';

class SelectVehiclesUberFlowController extends GetxController {
  final GetListOfVehiclesUseCase getListOfVehiclesUseCase;
  final IShowBottomSheetHelper iShowBottomSheetHelper;
  final GetListPricingVehiclesUseCase getListPricingVehiclesUseCase;
  final GetListModelsVehicleUseCase getListModelsVehicleUseCase;
  final SaveVehicleStepUseCase saveVehicleStepUseCase;
  final IPageLoadingDialog pageLoadingDialog;
  SelectVehiclesUberFlowController({
    required this.getListOfVehiclesUseCase,
    required this.getListPricingVehiclesUseCase,
    required this.iShowBottomSheetHelper,
    required this.pageLoadingDialog,
    required this.saveVehicleStepUseCase,
    required this.getListModelsVehicleUseCase,
  });

  RxBool selectionEmpty = false.obs;

  final RxList<VehicleListObject> vehicles = <VehicleListObject>[].obs;
  final RxList<VehicleModelEntity?> modelsVehicle = <VehicleModelEntity?>[].obs;
  final Rxn<VehicleModelEntity> modelVehicleSelection =
      Rxn<VehicleModelEntity>();

  // selection option
  final _selectedOption = Rx<String?>(null);
  String? get selectedOption => _selectedOption.value;
  set selectedOption(String? newValue) => _selectedOption.value = newValue;

  // selection item vehicles
  final _selectedDocument = Rx<String?>(null);
  String? get selectedDocument => _selectedDocument.value;
  set selectedDocument(String? newValue) {
    _selectedDocument.value = newValue;
  }

  final _vehicleSelectionId = Rx<int?>(null);
  int? get vehicleSelectionId => _vehicleSelectionId.value;
  set vehicleSelectionId(int? newValue) {
    _vehicleSelectionId.value = newValue;
  }

  // PageController for handling page transitions
  final _vehiclesPageLoaderController =
      Rx<PagewiseLoadController<VehicleListObject>?>(null);
  PagewiseLoadController<VehicleListObject>? get vehiclesPageLoaderController =>
      _vehiclesPageLoaderController.value;
  set vehiclesPageLoaderController(
          PagewiseLoadController<VehicleListObject>? newValue) =>
      _vehiclesPageLoaderController.value = newValue;

  // list pricing
  final _listPricingVehiclesSelection = Rx<List<PricingVehicleEntity>>([]);
  List<PricingVehicleEntity> get listPricingVehiclesSelection =>
      _listPricingVehiclesSelection.value;
  set listPricingVehiclesSelection(List<PricingVehicleEntity> newValue) =>
      _listPricingVehiclesSelection.value = newValue;

  // selection item pricing for vehicles
  final _pricingVehiclesItemSelection = Rx<PricingVehicleEntity?>(null);
  PricingVehicleEntity? get pricingVehiclesItemSelection =>
      _pricingVehiclesItemSelection.value;
  set pricingVehiclesItemSelection(PricingVehicleEntity? newValue) {
    _pricingVehiclesItemSelection.value = newValue;
  }

  final _vehicleItemSelection = Rx<VehicleListObject?>(null);
  VehicleListObject? get vehicleItemSelection => _vehicleItemSelection.value;
  set vehicleItemSelection(VehicleListObject? newValue) =>
      _vehicleItemSelection.value = newValue;

  final _arguments = Rx<SelectVehiclesPageArguments?>(null);
  SelectVehiclesPageArguments? get arguments => _arguments.value;
  set arguments(SelectVehiclesPageArguments? newValue) =>
      _arguments.value = newValue;

  final RxInt currentPageIndex = 0.obs;
  final ScrollController controller = ScrollController();
  @override
  void onInit() {
    super.onInit();
    clearData();
    getArguments();
    loadInitialVehicles();
  }

  Future<void> loadInitialVehicles() async {
    vehiclesPageLoaderController = PagewiseLoadController<VehicleListObject>(
      pageFuture: _getVehiclesUber,
      pageSize: Pagination.vehiclesUberFlowPagination,
    );

    vehiclesPageLoaderController?.addListener(() {
      if ((vehiclesPageLoaderController?.loadedItems ?? []).isNotEmpty) {
        scrollToSelectedVehicle();
      }
    });
  }

  void scrollToSelectedVehicle() {
    if ((vehiclesPageLoaderController?.loadedItems ?? []).isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 300), () {
        final items = vehiclesPageLoaderController?.loadedItems ?? [];
        final index = items.indexWhere((e) => e.id == vehicleSelectionId);

        if (index != -1) {
          // Calculate scroll position to center the item in viewport
          if (controller.hasClients) {
            final viewportHeight = controller.position.viewportDimension;
            const itemHeight = 180.0; // Adjusted item height
            final offset =
                (index * itemHeight) - (viewportHeight / 2) + (itemHeight / 2);

            controller.animateTo(
              offset.clamp(0.0, controller.position.maxScrollExtent),
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }

          vehicleItemSelection = items[index];
          if (showPriceBottomsheet) {
            _getPricingListVehicle(items[index],
                selectionPricing: pricingVehiclesItemSelection);
          }
        }
      });
    }
  }

  Future<List<VehicleListObject>> _getVehiclesUber(int? index) async {
    final result = await getListOfVehiclesUseCase.call(GetListOfFilterParams(
      searchText: '',
      page: index,
      perPage: Pagination.vehiclesUberFlowPagination,
    ));

    return result?.fold(
          (failure) {
            final error = mapFailureToMessage(failure);
            errorSnackBar(
                context: Get.context!, title: "err".tr, message: error);
            return <VehicleListObject>[]; // Return empty list on failure
          },
          (list) {
            vehicles.value = list ?? [];
            return list;
          }, // Return the actual list on success
        )?.noneNullList() ??
        [];
  }

  @override
  void onClose() {
    vehiclesPageLoaderController?.dispose();
    super.onClose();
  }

  void setSelectedOption(String? option) {
    selectedOption = option;
    selectionEmpty.value = false;
    modelVehicleSelection.value = null;
    if (selectedOption == 'own') {
      _getModelsVehicleList();
    }
  }

  void onClickItemRent(VehicleListObject vehicle) {
    if (vehicle.id != null) {
      if (vehicle.id != vehicleItemSelection?.id) {
        listPricingVehiclesSelection = [];
        _getPricingListVehicle(vehicle, selectionPricing: null);
      } else {
        _getPricingListVehicle(vehicle,
            selectionPricing: pricingVehiclesItemSelection);
      }
    }
  }

  Future<void> _openSheetPricing(VehicleListObject? item,
      {PricingVehicleEntity? selectionPricing}) async {
    final sheet = RentalPricingSheet(
      vehicleImageUrl: item?.image ?? '',
      pricingList: listPricingVehiclesSelection,
      pricingSelection: selectionPricing,
      priceNote: item?.priceNote,
    );
    final dynamic result = await iShowBottomSheetHelper //
        .showBottomSheet<dynamic>(
      ShowBottomSheetInput(
        isScrollControlled: true,
        sheet,
      ),
    );

    if (result is PricingVehicleEntity) {
      vehicleItemSelection = item;
      pricingVehiclesItemSelection = result;
      saveStepUberFlow();
    }
  }

  Future<void> _getPricingListVehicle(VehicleListObject? item,
      {PricingVehicleEntity? selectionPricing}) async {
    final loader = pageLoadingDialog.showLoadingDialog();
    final result = await getListPricingVehiclesUseCase.call(item?.id ?? 0);
    result?.fold(
      (failure) {
        loader.hide();
        final error = mapFailureToMessage(failure);
        errorSnackBar(context: Get.context!, title: "err".tr, message: error);
        listPricingVehiclesSelection = [];
      },
      (list) {
        loader.hide();
        listPricingVehiclesSelection = list ?? [];
        _openSheetPricing(
          item,
          selectionPricing: selectionPricing,
        );
      },
    );
  }

  Future<void> _getModelsVehicleList() async {
    if (modelsVehicle.isNotEmpty) {
      return;
    } else {
      final loader = pageLoadingDialog.showLoadingDialog();
      final result = await getListModelsVehicleUseCase.call(NoParams());
      result?.fold(
        (failure) {
          loader.hide();
          final error = mapFailureToMessage(failure);
          errorSnackBar(context: Get.context!, title: "err".tr, message: error);
          listPricingVehiclesSelection = [];
        },
        (list) {
          loader.hide();
          modelsVehicle.value = list?.noneNullList() ?? [];
        },
      );
    }
  }

  void deleteImageOwnVehicles() {
    selectedDocument = null;
  }

  void clearData() {
    selectedOption = null;
    selectedDocument = null;
  }

  bool isEnableNext() {
    bool allComplected = true;
    if (selectedOption == null) {
      allComplected = false;
      selectionEmpty.value = true;
    }
    if (selectedOption == 'own') {
      if ((selectedDocument ?? '').isEmpty) {
        allComplected = false;
      }
    } else if (selectedOption == "rent") {
      if (vehicleItemSelection == null) {
        allComplected = false;
      } else if (pricingVehiclesItemSelection == null) {
        allComplected = false;
      }
    }
    return allComplected;
  }

  void selectWantRent() {
    selectedOption = 'rent';
    modelVehicleSelection.value = null;
  }

  Future<void> saveStepUberFlow() async {
    if (isEnableNext()) {
      final loader = pageLoadingDialog.showLoadingDialog();
      final input = SaveVehicleStepInputEntity(
        commitmentMonths: pricingVehiclesItemSelection?.commitmentMonths ?? 0,
        carRegistrationAttachmentUrl:
            selectedOption == 'own' ? selectedDocument ?? '' : '',
        customerOwnedCar: selectedOption == 'own',
        vehicleId: vehicleItemSelection?.id ?? 0,
        vehiclePricingId: pricingVehiclesItemSelection?.id ?? 0,
        vehicleModelId: modelVehicleSelection.value?.id,
      );
      final result = await saveVehicleStepUseCase.call(input);
      result.fold(
        (failure) {
          loader.hide();
          final error = mapFailureToMessage(failure);
          errorSnackBar(context: Get.context!, title: "err".tr, message: error);
        },
        (data) {
          loader.hide();
          _goToKys();
        },
      );
    }
  }

  void _goToKys() {
    Get.toNamed(
      AppRoutes.KYCUberPage,
    )?.then((value) {
      if ((vehiclesPageLoaderController?.loadedItems ?? []).isNotEmpty) {
        if (vehicleItemSelection!.id != null) {
          // _getPricingListVehicle(
          //   vehicleItemSelection,
          //   selectionPricing: pricingVehiclesItemSelection,
          // );
        }
      }
    });
  }

  bool showPriceBottomsheet = true;
  Future<void> getArguments() async {
    arguments = safeCast<SelectVehiclesPageArguments>(Get.arguments);
    if (arguments != null && arguments?.withUberAllDataEntity != null) {
      showPriceBottomsheet = arguments?.showPriceBottomsheet ?? true;
      final data = arguments?.withUberAllDataEntity;
      selectedOption = data?.customerOwnedCar == true ? 'own' : 'rent';
      selectedDocument = (data?.carRegistrationAttachmentUrl ?? '').isNotEmpty
          ? data?.carRegistrationAttachmentUrl
          : null;
      pricingVehiclesItemSelection =
          data?.vehiclePricing?.toVehiclePricingEntity();
      vehicleSelectionId = data?.vehicleId;
      if (data?.vehicleModel != null) {
        modelVehicleSelection.value = data?.vehicleModel?.toEntity();
        WidgetsBinding.instance.addPostFrameCallback((value) {
          _getModelsVehicleList();
        });
      }
    }
  }

  void setModelSelection(VehicleModelEntity item) {}
}
