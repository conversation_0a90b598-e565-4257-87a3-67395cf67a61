import 'package:dartz/dartz.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/pricing_vehicle_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/repositories/i_select_vehicles_repository.dart';

import '../../../../../core/error/failures.dart';
import '../../../../../core/user_cases/user_case.dart';

class GetListPricingVehiclesUseCase
    implements UseCase<List<PricingVehicleEntity>?, int> {
  final ISelectVehiclesRepository selectVehiclesRepository;

  GetListPricingVehiclesUseCase({
    required this.selectVehiclesRepository,
  });

  @override
  Future<Either<Failure, List<PricingVehicleEntity>?>?> call(int vehicleId) {
    return selectVehiclesRepository.getAllPricingVehicles(vehicleId);
  }
}
