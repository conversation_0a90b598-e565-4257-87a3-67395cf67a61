import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/customer_entity.dart';

class SaveVehicleStepEntity {
  final String? campaignName;

  final String? uberAccountEmail;

  final String? carRegistrationAttachmentUrl;

  final String? uberAccountPhone;

  final CustomerEntity? customer;

  final String? drivingLicenceUrl;

  final int? applicationVersion;

  final bool? customerOwnedCar;

  final String? status;

  final int? vehicleId;

  final bool? hasUberAccount;

  final String? uberLead;

  final String? identityDocumentUrl;

  final int? id;

  final String? uberLeadId;

  SaveVehicleStepEntity(
      {this.campaignName,
      this.uberAccountEmail,
      this.carRegistrationAttachmentUrl,
      this.uberAccountPhone,
      this.customer,
      this.drivingLicenceUrl,
      this.applicationVersion,
      this.customerOwnedCar,
      this.status,
      this.vehicleId,
      this.hasUberAccount,
      this.uberLead,
      this.identityDocumentUrl,
      this.id,
      this.uberLeadId});
}
