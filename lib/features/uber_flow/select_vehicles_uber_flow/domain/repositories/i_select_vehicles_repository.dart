import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/pricing_vehicle_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/save_vehicle_step_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/save_vehicle_step_input_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/vehicle_model_entity.dart';

abstract class ISelectVehiclesRepository {
  Future<Either<Failure, List<PricingVehicleEntity>?>?> getAllPricingVehicles(
      int vehicleId);
  Future<Either<Failure, List<VehicleModelEntity?>?>?> getAllVehicleModels();
  Future<Either<Failure, SaveVehicleStepEntity?>> saveVehicleStepInputEntity(
      SaveVehicleStepInputEntity input);
}
