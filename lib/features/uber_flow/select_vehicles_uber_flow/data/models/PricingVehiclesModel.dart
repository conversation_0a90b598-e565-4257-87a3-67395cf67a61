import 'dart:convert';

/// title : "120 ر.س. / اليوم"
/// subtitle : "مدة الإلتزام 1 شهور"
/// id : 4
List<PricingVehiclesModel> listPricingVehiclesModelFromJson(List json) =>
    List<PricingVehiclesModel>.from(
        json.map((x) => PricingVehiclesModel.fromJson(x)));

PricingVehiclesModel pricingVehiclesModelFromJson(String str) =>
    PricingVehiclesModel.fromJson(json.decode(str));
String pricingVehiclesModelToJson(PricingVehiclesModel data) =>
    json.encode(data.toJson());

class PricingVehiclesModel {
  PricingVehiclesModel({
    String? title,
    String? subtitle,
    int? id,
    int? commitmentMonths,
  }) {
    _title = title;
    _subtitle = subtitle;
    _id = id;
    _commitmentMonths = commitmentMonths;
  }

  PricingVehiclesModel.fromJson(dynamic json) {
    _title = json['title'];
    _subtitle = json['subtitle'];
    _id = json['id'];
    _commitmentMonths = json['commitment_months'];
  }
  String? _title;
  String? _subtitle;
  int? _id;
  int? _commitmentMonths;
  PricingVehiclesModel copyWith({
    String? title,
    String? subtitle,
    int? id,
    int? commitmentMonths,
  }) =>
      PricingVehiclesModel(
        title: title ?? _title,
        subtitle: subtitle ?? _subtitle,
        id: id ?? _id,
        commitmentMonths: commitmentMonths ?? _commitmentMonths,
      );
  String? get title => _title;
  String? get subtitle => _subtitle;
  int? get id => _id;
  int? get commitmentMonths => _commitmentMonths;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['title'] = _title;
    map['subtitle'] = _subtitle;
    map['id'] = _id;
    map['commitment_months'] = _commitmentMonths;
    return map;
  }
}
