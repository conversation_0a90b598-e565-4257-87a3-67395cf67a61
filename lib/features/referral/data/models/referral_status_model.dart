import '../../domain/entities/referral_status.dart';

// ReferralInstruction model to represent each step in the referral instructions
class ReferralInstructionModel extends ReferralInstruction {
  @override
  final String? title;
  @override
  final String? details;

  const ReferralInstructionModel({
    this.title,
    this.details,
  });

  factory ReferralInstructionModel.fromJson(Map<String, dynamic> json) {
    return ReferralInstructionModel(
      title: json['title'] as String?,
      details: json['details'] as String?,
    );
  }
}

// ReferralStatusModel representing the full referral status response
class ReferralStatusModel extends ReferralStatus {
  const ReferralStatusModel({
    super.rewardValue,
    super.rewardRequirement,
    super.rewardCurrency,
    super.referralSummary,
    super.rewardRequirementStr,
    List<ReferralInstructionModel>? referralInstructions,
    super.referralNotes,
    super.referralUrl,
  }) : super(
          referralInstructions: referralInstructions,
        );

  // Factory constructor to convert JSON data into the ReferralStatusModel
  factory ReferralStatusModel.fromJson(Map<String, dynamic> json) {
    return ReferralStatusModel(
      rewardValue: json['reward_value'] as int?,
      rewardRequirement: json['reward_requirement'] as int?,
      rewardCurrency: json['reward_currency'] as String?,
      referralSummary: json['referral_summary'] as String?,
      rewardRequirementStr: json['reward_requirement_str'] as String?,
      referralInstructions: (json['referral_instructions'] as List<dynamic>?)
          ?.map((instruction) => ReferralInstructionModel.fromJson(instruction))
          .toList(),
      referralNotes: (json['referral_notes'] as List<dynamic>?)
          ?.map((note) => note as String)
          .toList(),
      referralUrl: json['referral_url'] as String?,
    );
  }
}
