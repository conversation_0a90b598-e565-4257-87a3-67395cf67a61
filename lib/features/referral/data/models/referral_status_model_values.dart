import '../../domain/entities/referral_status_values.dart';

// ReferralInstruction model to represent each step in the referral instructions
class ReferralStatusValuesModel extends ReferralStatusValues {
  @override
  final int? registeredCount;
  @override
  final int? rewardedCount;

  const ReferralStatusValuesModel({
    this.registeredCount,
    this.rewardedCount,
  }) : super(
          registeredCount: registeredCount,
          rewardedCount: rewardedCount,
        );

  factory ReferralStatusValuesModel.fromJson(Map<String, dynamic> json) {
    return ReferralStatusValuesModel(
      registeredCount: json['registered_count'] as int?,
      rewardedCount: json['rewarded_count'] as int?,
    );
  }
}
