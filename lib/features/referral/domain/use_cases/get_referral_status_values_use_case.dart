import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/referral_status_values.dart';
import '../repositories/referral_repository.dart';

class GetReferralStatusValuesUseCase {
  final ReferralRepository repository;

  GetReferralStatusValuesUseCase({required this.repository});

  Future<Either<Failure, ReferralStatusValues?>?> call() {
    return repository.getReferralStatusValues();
  }
}
