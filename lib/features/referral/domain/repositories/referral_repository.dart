// lib/domain/repositories/referral_repository.dart
import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/referral_status.dart';
import '../entities/referral_status_values.dart';

abstract class ReferralRepository {
  Future<Either<Failure, ReferralStatus?>> getReferralStatus();
  Future<Either<Failure, ReferralStatusValues?>> getReferralStatusValues();
}
