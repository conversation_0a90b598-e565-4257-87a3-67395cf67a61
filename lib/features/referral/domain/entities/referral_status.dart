// lib/domain/entities/referral_status.dart

// Entity class for referral instructions
class ReferralInstruction {
  final String? title;
  final String? details;

  const ReferralInstruction({
    this.title,
    this.details,
  });
}

// Entity class for referral status
class ReferralStatus {
  final int? rewardValue;
  final int? rewardRequirement;
  final String? rewardCurrency;
  final String? referralSummary;
  final String? rewardRequirementStr;
  final List<ReferralInstruction>? referralInstructions;
  final List<String>? referralNotes;
  final String? referralUrl;

  const ReferralStatus({
    this.rewardValue,
    this.rewardRequirement,
    this.rewardCurrency,
    this.referralSummary,
    this.rewardRequirementStr,
    this.referralInstructions,
    this.referralNotes,
    this.referralUrl,
  });
}
