import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/widget/custom_icon_button.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/referral_status.dart';
import '../../domain/entities/referral_status_values.dart';
import '../manager/referral_bloc.dart';
import '../manager/referral_event.dart';
import '../manager/referral_state.dart';

class ReferralScreen extends StatefulWidget {
  const ReferralScreen({super.key, this.showBack = true});
  final bool showBack;

  @override
  State<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends State<ReferralScreen> {
  @override
  Widget build(BuildContext context) {
    return Material(
      child: CupertinoPageScaffold(
        child: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              SliverOverlapAbsorber(
                handle:
                    NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                sliver: CupertinoSliverNavigationBar(
                  backgroundColor: context.whiteColor,
                  border: null,
                  leading: widget.showBack
                      ? Container(
                          height: 40.h,
                          width: 40.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: context.iconBackgroundColor,
                          ),
                          child: GestureDetector(
                            onTap: () {
                              Get.back();
                            },
                            child: Icon(
                              Icons.arrow_back_rounded,
                              size: 20.w,
                            ),
                          ),
                        )
                      : SizedBox.shrink(),
                  largeTitle: CustomTextWidget(
                    title: 'refer_and_earn'.tr,
                    color: context.black,
                    size: 22,
                    fontWeight: FontWeight.w600,
                  ),
                  alwaysShowMiddle: false,
                  middle: CustomTextWidget(
                    title: 'refer_and_earn'.tr,
                    color: context.black,
                    size: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ];
          },
          body: MultiBlocProvider(
            providers: [
              BlocProvider.value(
                value: getIt<MainHomeBloc>(),
              ),
              BlocProvider.value(
                value: getIt<ReferralBloc>(),
              ),
            ],
            // ,
            child: Builder(builder: (context) {
              context.read<ReferralBloc>().add(FetchReferralStatus(
                  referralStatus: context.select((MainHomeBloc mainHomeBloc) =>
                      mainHomeBloc.state.referralStatus)));
              return BlocBuilder<ReferralBloc, ReferralState>(
                builder: (context, state) {
                  if (state.status == AppStatus.loading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state.status == AppStatus.success) {
                    return _buildReferralContent(
                        state.referralStatus, state.referralStatusValues);
                  } else if (state.status == AppStatus.failure) {
                    return Center(
                        child: CustomTextWidget(
                            title: state.errorMessage ??
                                'Error loading referral data.'));
                  } else {
                    return Center(
                        child: CustomTextWidget(title: 'no_referral_data'.tr));
                  }
                },
              );
            }),
          ),
        ),
      ),
    );
  }

  // Widget to display dynamic backend data and static content
  Widget _buildReferralContent(
    ReferralStatus? referralStatus,
    ReferralStatusValues? referralStatusValues,
  ) {
    return Builder(builder: (context) {
      return CustomScrollView(
        slivers: [
          SliverOverlapInjector(
            handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                  bottom: widget.showBack ? 20.h : 100.h,
                  top: 60.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Static Main Image with custom size
                  Image.asset(
                    Assets.thrivvePhotosReferral,
                    width: 200.w, // Responsive width using ScreenUtil
                    height: 100.h, // Responsive height using ScreenUtil
                  ),
                  SizedBox(height: 60.h),

                  // Dynamic data from backend: Promotional text (headline)
                  CustomFormattedText(
                    style: TextStyle(
                      fontSize: 13.sp,
                      height: 1.3,
                      color: context.black,
                    ),
                    strongStyle: TextStyle(
                      fontSize: 13.sp,
                      height: 1.3,
                      fontWeight: FontWeight.w700,
                      color: context.black,
                    ),
                    textAlign: WrapAlignment.center,
                    text: referralStatus?.referralSummary ?? '',
                  ),

                  SizedBox(height: 18.h),

                  // Button to share the referral link (with icon)

                  CustomIconButton(
                    text: "share_link".tr,
                    radius: 16.r,
                    onTab: () {
                      getIt<IAnalyticsLogger>()
                          .logEvent(AnalyticsActions.shareReferralLinkClick);
                      _shareReferralLink(referralStatus);
                    },
                    assetsIcon: Assets.thrivvePhotosShare,
                    height: 48.h,
                    isReverse: true,
                    textStyle: TextStyle(
                      color: Colors.white,
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  SizedBox(height: 32.h),

                  // Row displaying Registered and Completed Trips
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: context.black.withValues(alpha: 0.20)),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              CustomTextWidget(
                                title: 'registered'.tr,
                                size: 12,
                                fontWeight: FontWeight.w700,
                                color: context.black,
                              ),
                              SizedBox(height: 8.h),
                              CustomTextWidget(
                                title:
                                    '${referralStatusValues?.registeredCount}',
                                size: 12,
                                fontWeight: FontWeight.w700,
                                color: context.black,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 0.5.w,
                          height: 44.h,
                          color: context.black.withValues(alpha: 0.20),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              CustomTextWidget(
                                title:
                                    referralStatus?.rewardRequirementStr ?? '',
                                size: 12,
                                fontWeight: FontWeight.w700,
                                color: context.black,
                              ),
                              SizedBox(height: 8.h),
                              CustomTextWidget(
                                title: '${referralStatusValues?.rewardedCount}',
                                size: 12.sp,
                                fontWeight: FontWeight.w700,
                                color: context.black,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20.h),
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: context.black.withValues(alpha: 0.20)),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Column(
                      children: referralStatus?.referralInstructions != null &&
                              (referralStatus?.referralInstructions ?? [])
                                  .isNotEmpty
                          ? (referralStatus?.referralInstructions ?? [])
                              .map((instruction) => Column(
                                    children: [
                                      _buildInstructionStep(
                                        context: context,
                                        step: (referralStatus
                                                        ?.referralInstructions ??
                                                    [])
                                                .indexOf(instruction) +
                                            1,
                                        title: instruction.title ?? '',
                                        description: instruction.details ?? '',
                                      ),
                                      if ((referralStatus
                                                      ?.referralInstructions ??
                                                  [])
                                              .indexOf(instruction) !=
                                          (referralStatus?.referralInstructions ??
                                                      [])
                                                  .length -
                                              1)
                                        SizedBox(height: 20.h),
                                    ],
                                  ))
                              .toList()
                          : [],
                    ),
                  ),

                  SizedBox(height: 32.h),

                  // Information at the bottom with icons in Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: referralStatus?.referralNotes != null &&
                            (referralStatus?.referralNotes ?? []).isNotEmpty
                        ? (referralStatus?.referralNotes ?? [])
                            .map((note) => Column(
                                  children: [
                                    _buildInfoRow(
                                      context: context,
                                      icon: Icons.info_outlined,
                                      text: note,
                                    ),
                                    if ((referralStatus?.referralNotes ?? [])
                                            .indexOf(note) !=
                                        (referralStatus?.referralNotes ?? [])
                                                .length -
                                            1)
                                      SizedBox(height: 12.h),
                                  ],
                                ))
                            .toList()
                        : [],
                  ),
                ],
              ),
            ),
          )
        ],
      );
    });
  }

  // Widget for Step Instructions
  Widget _buildInstructionStep(
      {required int step,
      required String title,
      required String description,
      required BuildContext context}) {
    return Row(
      children: [
        // Step Number Circle
        Container(
          width: 34.w,
          height: 34.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.numberBackgroundColor
                .withValues(alpha: 0.20), // Light purple circle background
          ),
          child: Center(
            child: CustomTextWidget(
              title: '$step',
              size: 13,
              fontWeight: FontWeight.w600,
              color: context.greenColor,
            ),
          ),
        ),
        SizedBox(width: 12.w),
        // Step Title and Description
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomFormattedText(
                  text: title,
                  style: TextStyle(
                      fontSize: 12.sp,
                      height: 1.5,
                      fontFamily: Get.locale?.languageCode == 'en'
                          ? "NotoSans"
                          : "NotoSansArabic",
                      fontWeight: FontWeight.w400,
                      color: context.black)),
              CustomTextWidget(
                title: description,
                size: 11,
                fontFamily: Get.locale?.languageCode == 'en'
                    ? "NotoSans"
                    : "NotoSansArabic",
                height: 1.5,
                fontWeight: FontWeight.w400,
                color: context.lightBlack,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Widget for Info with Icon
  Widget _buildInfoRow(
      {required IconData icon,
      required String text,
      required BuildContext context}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16.sp, color: context.lightBlack),
        // Icon with size
        SizedBox(width: 10.w),
        Expanded(
          child: CustomTextWidget(
            title: text,
            size: 11,
            color: context.lightBlack,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  // Method to share the dynamic referral link
  void _shareReferralLink(ReferralStatus? referralStatus) {
    final referralLink = referralStatus?.referralUrl ?? '';
    Share.share(referralLink);
  }
}
