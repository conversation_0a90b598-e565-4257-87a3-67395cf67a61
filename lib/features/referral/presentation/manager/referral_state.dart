import 'package:equatable/equatable.dart';
import '../../../../core/util/app_status.dart';
import '../../domain/entities/referral_status.dart';
import '../../domain/entities/referral_status_values.dart';

class ReferralState extends Equatable {
  const ReferralState({
    this.status = AppStatus.initial,
    this.errorMessage,
    this.referralStatus,
    this.referralStatusValues,
  });

  final ReferralStatus? referralStatus;
  final ReferralStatusValues? referralStatusValues;
  final AppStatus status;
  final String? errorMessage;

  ReferralState copyWith({
    ReferralStatus? Function()? referralStatus,
    ReferralStatusValues? Function()? referralStatusValues,
    AppStatus Function()? status,
    String? Function()? errorMessage,
  }) {
    return ReferralState(
      referralStatus:
          referralStatus != null ? referralStatus() : this.referralStatus,
      status: status != null ? status() : this.status,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
      referralStatusValues: referralStatusValues != null
          ? referralStatusValues()
          : this.referralStatusValues,
    );
  }

  @override
  List<Object?> get props => [
        referralStatus,
        status,
        errorMessage,
        referralStatusValues,
      ];
}
