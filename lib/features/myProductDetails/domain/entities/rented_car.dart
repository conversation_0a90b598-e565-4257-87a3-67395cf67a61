import '../../../../core/util/date_util.dart';
import '../../data/models/contract_model.dart';

class RentedCar {
  String? carName;
  String? carImage;
  String? rentStatus;
  bool? isActive;
  String? fromDate;
  String? toDate;
  String? contractId;



  RentedCar.dummyData(status) {
    carName = "Hyndai accent 2022";
    carImage =
        "https://www.hyundai.com/content/dam/hyundai/au/en/models/side-profiles/2022-side-profiles/Hyundai_KONA-EV_Side-Profile_640x331.png";
    rentStatus = status;
    isActive = rentStatus == "Active";
    fromDate = "1 Mar 2022";
    toDate = "1 Apr 2022";
    contractId = "1";
  }

  RentedCar.fromServer(RentedCarModel rentedCar) {
    carName =
        "${rentedCar.vehicle?.manufacturerModel?.manufacturer?.manufacturerNameEn} "
        "${rentedCar.vehicle?.manufacturerModel?.manufacturerModelNameEn} "
        "${rentedCar.vehicle?.manufactureYear}";
    carImage = rentedCar.vehicle?.customCardLink;
    rentStatus = rentedCar.statusContract;
    isActive = rentStatus == "Active";
    contractId = rentedCar.id.toString();
    fromDate = DateUtil.getDateFormatWithMonth(rentedCar.startDate);
    toDate = DateUtil.getDateFormatWithMonth(rentedCar.endDate);
  }
}
