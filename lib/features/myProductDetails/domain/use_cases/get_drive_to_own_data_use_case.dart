import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../data/models/StanderResponse.dart';
import '../entities/drive_to_own.dart';
import '../repositories/drive_to_own_repository.dart';

class GetDriveToOwnDataUseCase
    implements UseCase<Either<DriveToOwn, Data>?, GetDriveToOwnDataParams> {
  DriveToOwnRepository? driveToOwnRepository;

  GetDriveToOwnDataUseCase({this.driveToOwnRepository});

  @override
  Future<Either<Failure, Either<DriveToOwn, Data>?>?> call(
      GetDriveToOwnDataParams params) async {
    return await driveToOwnRepository?.getDriveToOwnData(
        contractId: params.contractId);
  }
}

class GetDriveToOwnDataParams {
  final String? contractId;

  GetDriveToOwnDataParams({this.contractId});
}
