import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/product_details.dart';
import '../repositories/drive_to_own_repository.dart';

class GetContractDetailsDataUseCase
    implements UseCase<ProductDetails?, GetContractDetailsDataParams> {
  DriveToOwnRepository? driveToOwnRepository;

  GetContractDetailsDataUseCase({this.driveToOwnRepository});

  @override
  Future<Either<Failure, ProductDetails?>?> call(
      GetContractDetailsDataParams params) async {
    return await driveToOwnRepository?.getContractDetails(
        contractId: params.contractId);
  }
}

class GetContractDetailsDataParams {
  final String? contractId;

  GetContractDetailsDataParams({this.contractId});
}
