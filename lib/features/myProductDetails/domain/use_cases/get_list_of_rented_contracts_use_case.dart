import 'package:dartz/dartz.dart';
import 'package:thrivve/features/my_products/domain/entities/my_product.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../repositories/drive_to_own_repository.dart';

class GetListOfRentedContractUseCase
    implements UseCase<List<MyProduct>?, NoParams> {
  DriveToOwnRepository? driveToOwnRepository;

  GetListOfRentedContractUseCase({this.driveToOwnRepository});

  @override
  Future<Either<Failure, List<MyProduct>?>?> call(NoParams params) async {
    return await driveToOwnRepository?.getListOfRentedContract();
  }
}
