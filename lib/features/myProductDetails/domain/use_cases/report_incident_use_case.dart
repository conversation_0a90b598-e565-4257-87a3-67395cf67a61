import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../repositories/drive_to_own_repository.dart';

class ReportIncidentUseCase implements UseCase<String?, ReportIncidentParams> {
  DriveToOwnRepository? driveToOwnRepository;

  ReportIncidentUseCase({this.driveToOwnRepository});

  @override
  Future<Either<Failure, String?>?> call(ReportIncidentParams params) async {
    return await driveToOwnRepository?.reportIncident(
      incidentType: params.incidentType,
      incidentDate: params.incidentDate,
      incidentTime: params.incidentTime,
      incidentLocation: params.incidentLocation,
      incidentDescription: params.incidentDescription,
      incidentFrontImage: params.incidentFrontImage,
      incidentBackImage: params.incidentBackImage,
      incidentSideImage: params.incidentSideImage,
      didYouCallNajm: params.didYouCallNajm,
    );
  }
}

class ReportIncidentParams {
  final String? incidentType;
  final String? incidentDate;
  final String? incidentTime;
  final String? incidentLocation;
  final String? incidentDescription;
  final String? incidentFrontImage;
  final String? incidentBackImage;
  final String? incidentSideImage;
  final bool? didYouCallNajm;

  ReportIncidentParams({
    this.incidentType,
    this.incidentDate,
    this.incidentTime,
    this.incidentLocation,
    this.incidentDescription,
    this.incidentFrontImage,
    this.incidentBackImage,
    this.incidentSideImage,
    this.didYouCallNajm,
  });
}
