import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/theme/custom_color_ex.dart';
import 'package:thrivve/features/myProductDetails/domain/entities/car_details.dart';
import 'package:thrivve/features/myProductDetails/domain/entities/installment.dart';
import 'package:thrivve/features/myProductDetails/presentation/manager/drive_to_own_bloc.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/widget/app_error_widget.dart';
import '../../../../core/widget/app_loading_widget.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/car_document.dart';
import '../../domain/entities/product_details.dart';
import '../../domain/entities/rented_car.dart';
import '../widgets/all_installments_bottom_sheet.dart';
import '../widgets/list_of_rented_cars_bottom_sheet.dart';
import '../widgets/more_items_bottom_sheet.dart';
import '../widgets/payment_widget.dart';

class MyProductDetailsScreen extends StatelessWidget {
  MyProductDetailsScreen({super.key});

  final String contractId = Get.arguments["contractId"].toString();

  @override
  Widget build(BuildContext context) {
    final currency = context.watch<MainHomeBloc>().state.currency ?? "";
    return BlocProvider(
      create: (_) => getIt<DriveToOwnBloc>()
        ..add(GetDriveToOwnDataEvent(contractId: contractId)),
      child: BlocBuilder<DriveToOwnBloc, DriveToOwnState>(
        builder: (context, state) {
          switch (state.status) {
            case DriveToOwnStatuses.loading:
              return const Scaffold(body: AppLoadingWidget());
            case DriveToOwnStatuses.failure:
              return Scaffold(
                appBar: AppBar(
                  centerTitle: true,
                  elevation: 0.0,
                  title: Text(
                    "drive_to_own".tr,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                body: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: AppErrorWidget(
                        error: state.errorMessage ?? "",
                        onPress: () {
                          context.read<DriveToOwnBloc>().add(
                              GetDriveToOwnDataEvent(contractId: contractId));
                        }),
                  ),
                ),
              );
            case DriveToOwnStatuses.success:
              return Scaffold(
                  appBar: AppBar(
                      centerTitle: true,
                      elevation: 0.0,
                      surfaceTintColor: Colors.transparent,
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            children: [
                              Text(
                                state.productDetails?.name ?? "",
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              Text(
                                state.productDetails?.type ?? "",
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ],
                      ),
                      actions: <Widget>[
                        Visibility(
                          visible: state.productDetails?.productType ==
                              ProductType.vehicle,
                          child: IconButton(
                            icon: Icon(
                              Icons.more_vert_rounded,
                              color: context.black,
                            ),
                            onPressed: () {
                              showMoreBottomSheet(
                                context: context,
                                carDetail: state.productDetails?.carDetail,
                                listOfMaintenance:
                                    state.productDetails?.documentsList ?? [],
                              );
                            },
                          ),
                        )
                      ],
                      leading: IconButton(
                        icon: Icon(
                          Icons.arrow_back_ios_rounded,
                          color: context?.black,
                        ),
                        onPressed: () {
                          Get.back();
                        },
                      )),
                  body: RefreshIndicator(
                    onRefresh: () async {
                      context
                          .read<DriveToOwnBloc>()
                          .add(GetDriveToOwnDataEvent(contractId: contractId));
                    },
                    child: SingleChildScrollView(
                      child: Column(children: [
                        Container(
                          padding: const EdgeInsets.only(
                              right: 16, left: 16, top: 22, bottom: 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(8),
                                bottomRight: Radius.circular(8)),
                            color: context.containerColor,
                          ),
                          child: Column(
                            children: [
                              state.productDetails?.imageUrl == null
                                  ? SizedBox(
                                      width: 250,
                                      height: 124,
                                      child: Center(
                                        child: Image.asset(
                                          state.productDetails?.productType ==
                                                  ProductType.vehicle
                                              ? Assets.thrivvePhotosVehicle
                                              : Assets.thrivvePhotosPhone,
                                        ),
                                      ),
                                    )
                                  : Image.network(
                                      state.productDetails?.imageUrl ?? "",
                                      errorBuilder:
                                          (context, object, stackTrace) {
                                        return SizedBox(
                                          width: 250,
                                          height: 124,
                                          child: Center(
                                            child: Image.asset(
                                              Assets.thrivvePhotosRideToOwn,
                                              color: Colors.grey.shade200,
                                            ),
                                          ),
                                        );
                                      },
                                      width: 250,
                                      height: 124,
                                    ),
                              const SizedBox(
                                height: 16,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  Flexible(
                                    child: Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            color: context.containerColor,
                                            border: Border.all(
                                                color:
                                                    context.statusBackground!,
                                                width: 1)),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Image.asset(
                                              Assets.thrivvePhotosInstallment,
                                              width: 24,
                                              height: 24,
                                              color: context
                                                  .appPrimaryColorGradient,
                                            ),
                                            const SizedBox(
                                              width: 8,
                                            ),
                                            Text(
                                              "${state.productDetails?.leftAmount} $currency",
                                              // ${"left".tr} todos
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(fontSize: 12),
                                            )
                                          ],
                                        )),
                                  ),
                                  const SizedBox(
                                    width: 12,
                                  ),
                                  Visibility(
                                    visible:
                                        state.productDetails?.remainingDate !=
                                            null,
                                    child: Flexible(
                                      child: Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              color: context.containerColor,
                                              border: Border.all(
                                                  color: Theme.of(context)
                                                      .extension<
                                                          CustomColors>()!
                                                      .statusBackground!,
                                                  width: 1)),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.calendar_month_outlined,
                                                color: context.appPrimaryColor,
                                              ),
                                              const SizedBox(
                                                width: 8,
                                              ),
                                              Text(
                                                "${state.productDetails?.remainingDate}",
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.copyWith(fontSize: 12),
                                              )
                                            ],
                                          )),
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            color: context.containerColor,
                          ),
                          child: Column(
                            children: [
                              SizedBox(
                                height: 216,
                                child: SfRadialGauge(
                                  axes: <RadialAxis>[
                                    RadialAxis(
                                        showLabels: false,
                                        showTicks: false,
                                        startAngle: 180,
                                        endAngle: 0,
                                        radiusFactor: 1,
                                        canScaleToFit: true,
                                        axisLineStyle: AxisLineStyle(
                                          thickness: 0.12,
                                          color: context.progressBarOffColor,
                                          thicknessUnit: GaugeSizeUnit.factor,
                                          cornerStyle: CornerStyle.bothCurve,
                                        ),
                                        pointers: <GaugePointer>[
                                          RangePointer(
                                            value: state.productDetails
                                                    ?.paidPercentage ??
                                                10.0,
                                            width: 0.12,
                                            sizeUnit: GaugeSizeUnit.factor,
                                            cornerStyle: CornerStyle.bothCurve,
                                            color: context.appPrimaryColor,
                                          )
                                        ],
                                        annotations: <GaugeAnnotation>[
                                          GaugeAnnotation(
                                              positionFactor: 0.1,
                                              angle: 90,
                                              widget: Column(
                                                children: [
                                                  Text(
                                                    "${state.productDetails?.paidPercentage?.toStringAsFixed(0)} %",
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .displaySmall,
                                                  ),
                                                  Text(
                                                    "${state.productDetails?.paidAmount} ",
                                                    //${controller.currency}  ${"paid".tr}
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .labelLarge,
                                                  ),
                                                ],
                                              ))
                                        ])
                                  ],
                                  enableLoadingAnimation: true,
                                ),
                              ),
                              Column(
                                children: [
                                  Divider(
                                    height: 1,
                                    color: context.lightBlack,
                                  ),
                                  InkWell(
                                    onTap: () {
                                      showAllInstallmentsBottomSheet(context,
                                          listOfInstallments: state
                                                  .productDetails
                                                  ?.listOfInstallments ??
                                              [],
                                          currency: currency,
                                          isRentContract: false);
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            // state.driveToOwn?.isDriveToRent ==
                                            //         true
                                            //     ? "view_rent_history".tr
                                            //     :
                                            "see_all_installments".tr,
                                            style: Theme.of(context)
                                                .textTheme
                                                .labelLarge,
                                          ),
                                          Icon(
                                            Icons.arrow_forward_ios_rounded,
                                            color: context.black!
                                                .withOpacity(0.20),
                                            size: 12,
                                          )
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        // Visibility(
                        //   visible: state.hasDueDateInstallments == true,
                        //   child: ListView.builder(
                        //       shrinkWrap: true,
                        //       itemCount:
                        //           state.listOfDueDateInstallments?.length,
                        //       physics: const NeverScrollableScrollPhysics(),
                        //       itemBuilder: (context, index) {
                        //         return PaymentWidget(
                        //           paymentType: "due_date_payment".tr,
                        //           nextPaymentAmount:
                        //               "${state.listOfDueDateInstallments![index].price} ${state.currency}",
                        //           nextPaymentDate: state
                        //               .listOfDueDateInstallments![index].date,
                        //         );
                        //       }),
                        // ),
                        Visibility(
                            visible:
                                state.productDetails?.nextPaymentDate != null,
                            child: PaymentWidget(
                              paymentType:
                                  // state.driveToOwn?.isDriveToRent == true
                                  //     ? "next_rent_payment".tr
                                  //     :
                                  "next_payment".tr,
                              nextPaymentAmount:
                                  "${state.productDetails?.nextPaymentAmount} $currency"
                                      .toString(),
                              nextPaymentDate:
                                  state.productDetails?.nextPaymentDate ?? "",
                              paymentTitle:
                                  // state.driveToOwn?.isDriveToRent == true
                                  //     ? "payment".tr
                                  //     :
                                  "installment".tr,
                            )),
                        // MaintenanceWidget(
                        //   maintenanceReason:
                        //       controller.driveToOwn.value.maintenanceReason,
                        //   maintenanceDistance:
                        //       controller.driveToOwn.value.maintenanceDistance,
                        // )
                        // Visibility(
                        //   visible: state.driveToOwn?.hasContract == true,
                        //   child: DocumentWidget(
                        //     docName: state.driveToOwn?.contractName,
                        //     docPdfUrl: state.driveToOwn?.contractPDF,
                        //   ),
                        // ),
                      ]),
                    ),
                  ));
            default:
              return const SizedBox.shrink();
          }
        },
      ),
    );
  }

  showMoreBottomSheet({
    required BuildContext context,
    List<CarDocument>? listOfMaintenance,
    CarDetail? carDetail,
  }) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return MoreItemBottomSheet(
          carDetail: carDetail,
          listOfDocument: listOfMaintenance,
        );
      },
    );
  }

  showListOfRentedCarsBottomSheet(
      BuildContext contextt, List<RentedCar> listOfRentedCars) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: contextt,
      builder: (BuildContext context) {
        return BlocProvider.value(
            value: contextt.read<DriveToOwnBloc>(),
            child: ListOfRentedCarsBottomSheet(
              listOfRentedCars: listOfRentedCars,
            ));
      },
    );
  }

  showAllInstallmentsBottomSheet(BuildContext context,
      {required List<Installment> listOfInstallments,
      String? currency,
      bool? isRentContract}) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return AllInstallmentsBottomSheet(
            listOfInstallments: listOfInstallments,
            currency: currency,
            isRentContract: isRentContract);
      },
    );
  }
}
