import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../domain/entities/customer_support.dart';
import 'expansion_title_customer_support.dart';

class CustomerSupportItems extends StatefulWidget {
  CustomerSupport customerSupport;
  bool? isExpanded = false;

  CustomerSupportItems({
    required this.customerSupport,
    required this.isExpanded,
    super.key,
  });

  @override
  State<CustomerSupportItems> createState() => _CustomerSupportItemsState();
}

class _CustomerSupportItemsState extends State<CustomerSupportItems> {
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: Key(widget.customerSupport.id.toString()),
        onExpansionChanged: (isExpanded) {
          setState(() {
            widget.isExpanded = isExpanded;
          });
        },
        title: ExpansionTitleCustomerSupport(
          title: widget.customerSupport.title,
          date: widget.customerSupport.createdAt,
        ),
        trailing: SizedBox(
            width: 32,
            height: 32,
            child: widget.isExpanded == true
                ? Icon(
                    Icons.expand_less,
                    color: context.black!.withOpacity(0.1),
                  )
                : Icon(
                    Icons.expand_more,
                    color: context.black!.withOpacity(0.1),
                  )),
        childrenPadding: const EdgeInsets.symmetric(
          horizontal: 16.0,
        ),
        initiallyExpanded: widget.isExpanded ?? false,
        expandedAlignment: Alignment.centerLeft,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "description".tr,
                style: TextStyle(
                    fontSize: 14,
                    overflow: TextOverflow.ellipsis,
                    color: context.black,
                    fontWeight: FontWeight.w400),
              ),
              const SizedBox(
                height: 2.0,
              ),
              Text(
                widget.customerSupport.description ?? "",
                style: TextStyle(
                    fontSize: 12,
                    overflow: TextOverflow.ellipsis,
                    color: context.statusBackground,
                    fontWeight: FontWeight.w400),
              ),
            ],
          )
        ],
      ),
    );
  }
}
