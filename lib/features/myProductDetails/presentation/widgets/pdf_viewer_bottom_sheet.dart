import 'dart:io';
import 'package:app_settings/app_settings.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class PdfViewerBottomSheet extends StatefulWidget {
  final String? url;
  final String? title;
  final bool? isPDF;
  final bool? showShareBtn;

  const PdfViewerBottomSheet({
    super.key,
    this.title,
    this.url,
    this.isPDF,
    this.showShareBtn = false,
  });

  @override
  State<PdfViewerBottomSheet> createState() => _PdfViewerBottomSheetState();
}

class _PdfViewerBottomSheetState extends State<PdfViewerBottomSheet> {
  final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory(() => EagerGestureRecognizer())
  };

  final UniqueKey _key = UniqueKey();

  var downloading = false;
  var icon = Icons.download;
  var error = '';

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 44),
      padding: const EdgeInsets.only(bottom: 32),
      decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0), topRight: Radius.circular(16.0))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          SizedBox(
            height: 50,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 0,
                  bottom: 0,
                  child: IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: Colors.grey,
                    ),
                    color: context.black,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                ),
                Center(
                  child: Text(
                    widget.title ?? "",
                    style: TextStyle(
                        color: context.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                Positioned(
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Visibility(
                    visible: widget.showShareBtn == true && error.isEmpty,
                    // visible: false,
                    child: IconButton(
                      onPressed: () {
                        requestPermission(url: widget.url ?? "");
                      },
                      icon: const Icon(Icons.ios_share_rounded,
                          color: Colors.grey),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: context.cardHomeContainer,
          ),
          Expanded(
            child: widget.isPDF == true
                ? PDF(
                    gestureRecognizers: gestureRecognizers,
                    pageFling: false,
                  ).fromUrl(
                    key: _key,
                    widget.url ?? "",
                    placeholder: (progress) =>
                        Center(child: Text('$progress %')),
                    errorWidget: (error) =>
                        Center(child: Text(error.toString())),
                  )
                : Center(
                    child: Image.network(
                      widget.url ?? "",
                      fit: BoxFit.fill,
                      errorBuilder: (BuildContext context, Object errorObject,
                          StackTrace? stackTrace) {
                        Future.microtask(() {
                          setState(() {
                            error = "error_can_not_load_image"
                                .tr; // Update the variable to true
                          });
                        });
                        return Center(
                            child: Text(
                          "error_can_not_load_image".tr,
                          style: const TextStyle(
                            color: Colors.red,
                          ),
                        ));
                      },
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Future<void> requestPermission({required String url}) async {
    try {
      final status = await Permission.storage.request();
      PermissionStatus permissionStatus = status;
      if (permissionStatus.isGranted) {
        openFile(url);
      } else if (permissionStatus.isDenied) {
      } else if (permissionStatus.isPermanentlyDenied) {
        AppSettings.openAppSettings();
      }
    } on PlatformException catch (e) {
      if (e.code == 'PERMISSION_DENIED') {
        error = "Could not get permission from the system to open the file";
      } else if (e.code == 'PERMISSION_DENIED_NEVER_ASK') {
        error =
            "Permission Denied - ask the user to enable it from the app settings";
      }
      if (Platform.isIOS) {
        if (kDebugMode) {
          print("Could not get permission from the system to open the file");
        }
      }
    } catch (_) {
      if (Platform.isIOS) {
        if (kDebugMode) {
          print("Could not get permission from the system to open the file");
        }
      }
      return;
    }
  }

  void openFile(String url) async {
    String? dir;
    if (Platform.isAndroid) {
      dir = (await getExternalStorageDirectory())?.path;
    } else {
      dir = (await getApplicationDocumentsDirectory()).path;
    }
    var filePath = "$dir/${url.substring(url.lastIndexOf('/') + 1)}";
    File file = File(filePath);
    var isExist = await file.exists();
    if (isExist) {
      final xFile = XFile(filePath);
      Share.shareXFiles([xFile], subject: 'share_reports'.tr);
    } else {
      downloadFile(url, filePath);
    }
  }

  Future<void> downloadFile(String url, String filePath) async {
    Dio dio = Dio();
    setState(() {
      downloading = true;
    });

    try {
      await dio.download(url, filePath, onReceiveProgress: (rec, total) {});

      final xFile = XFile(filePath);
      Share.shareXFiles([xFile], subject: 'share_reports'.tr);
    } catch (e) {
      Get.snackbar("Error", "Error downloading file from server",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Get.context!.containerColor,
          margin: const EdgeInsets.all(10),
          borderRadius: 10,
          duration: const Duration(seconds: 2));
      if (kDebugMode) {
        print(e);
      }
    }

    setState(() {
      downloading = false;
      icon = Icons.file_open;
    });
  }
}
