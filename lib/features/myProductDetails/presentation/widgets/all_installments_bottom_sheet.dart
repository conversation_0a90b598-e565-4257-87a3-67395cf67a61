import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../core/widget/list_tile_settings_widget.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/installment.dart';
import 'bottom_sheet_title_widget.dart';

class AllInstallmentsBottomSheet extends StatelessWidget {
  List<Installment> listOfInstallments;
  String? currency;

  bool? isRentContract = false;

  AllInstallmentsBottomSheet({
    super.key,
    required this.listOfInstallments,
    this.currency,
    this.isRentContract,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 64),
      padding: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0), topRight: Radius.circular(16.0))),
      // color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BottomSheetTitleWidget(
            title: isRentContract == true
                ? "all_payments".tr
                : "all_installments".tr,
          ),
          Flexible(
              fit: FlexFit.tight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: ListView.builder(
                  shrinkWrap: false,
                  itemCount: listOfInstallments.length,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return ListTileSettingsWidget(
                      title: "${listOfInstallments[index].name} $currency",
                      supTitle: "${listOfInstallments[index].date}",
                      widget: Text(
                        listOfInstallments[index].status == "Pending"
                            ? listOfInstallments[index].statusLabel ?? ""
                            : "${listOfInstallments[index].statusLabel}",
                        style: TextStyle(
                          color: listOfInstallments[index].status == "Pending"
                              ? context?.appPrimaryColor
                              : listOfInstallments[index].status == "Paid"
                                  ? context.greenBackgroundColor
                                  : context.errorMessageColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      imageIcon: Assets.thrivvePhotosDriveToOwn,
                      tap: () {},
                    );
                  },
                ),
              )),
        ],
      ),
    );
  }
}
