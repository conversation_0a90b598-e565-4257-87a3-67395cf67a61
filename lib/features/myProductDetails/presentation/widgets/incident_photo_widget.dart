import 'dart:io';

import 'package:flutter/material.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class IncidentPhotoWidget extends StatelessWidget {
  final bool isImageEmpty;

  Function()? onTap;
  Function()? onDeleted;
  String? title;
  String? image;

  IncidentPhotoWidget({
    super.key,
    required this.isImageEmpty,
    required this.onTap,
    required this.onDeleted,
    required this.title,
    required this.image,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        isImageEmpty
            ? InkWell(
                onTap: onTap,
                child: Container(
                  height: 84,
                  width: 100,
                  decoration: BoxDecoration(
                      color: context.black!.withOpacity(0.04),
                      borderRadius: BorderRadius.circular(4)),
                  child: Center(
                    child: Image.asset(
                      Assets.thrivvePhotosUploadImg,
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
              )
            : Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    height: 84,
                    width: 100,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.0),
                      child: Image.file(
                        File(image!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: onDeleted,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                          color: context?.containerColor!.withAlpha(200),
                          borderRadius: BorderRadius.circular(32)),
                      child: const Icon(
                        Icons.delete_outline_outlined,
                        color: Colors.red,
                      ),
                    ),
                  )
                ],
              ),
        const SizedBox(
          height: 8,
        ),
        Text(
          title ?? "",
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: context.fillIconColor,
          ),
        ),
      ],
    );
  }
}
