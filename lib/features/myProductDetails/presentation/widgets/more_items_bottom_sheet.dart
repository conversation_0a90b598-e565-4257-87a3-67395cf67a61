import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/myProductDetails/domain/entities/car_details.dart';
import 'package:thrivve/features/myProductDetails/domain/entities/incident_type.dart';
import 'package:thrivve/features/myProductDetails/presentation/widgets/report_incident_bottom_sheet.dart';
import '../../../../core/widget/list_tile_settings_widget.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/car_document.dart';
import 'car_details_bottom_sheet.dart';
import 'documents_bottom_sheet.dart';
import 'maintenance_bottom_sheet.dart';

class MoreItemBottomSheet extends StatelessWidget {
  final CarDetail? carDetail;

  final List<CarDocument>? listOfDocument;

  const MoreItemBottomSheet({super.key, this.carDetail, this.listOfDocument});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  right: 16.0, left: 16.0, top: 16.0, bottom: 8),
              child: Text(
                "more".tr,
                style: TextStyle(
                    color: context.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w600),
              ),
            ),
            ListTileSettingsWidget(
              title: "car_details".tr,
              imageIcon: Assets.thrivvePhotosDriveToOwn,
              tap: () {
                showCarDetailsBottomSheet(context, carDetail: carDetail);
              },
            ),
            // ListTileSettingsWidget(
            //   title: "report_an_incident".tr,
            //   imageIcon: Assets.thrivvePhotosReportIncident,
            //   tap: () {
            //     showReportAnIncidentBottomSheet(
            //       context,
            //     );
            //   },
            // ),
            // ListTileSettingsWidget(
            //   title: "accident_reports".tr,
            //   imageIcon: Assets.thrivvePhotosAccidentReports,
            //   tap: () {
            //     showMaintenanceBottomSheet(context,
            //         listOfMaintenance: listOfMaintenance);
            //   },
            // ),
            ListTileSettingsWidget(
              title: "documents".tr,
              imageIcon: Assets.thrivvePhotosDocuments,
              tap: () {
                showDocumentBottomSheet(context, listOfDocument ?? []);
              },
            ),
            // ListTileSettingsWidget(
            //   title: "customer_support".tr,
            //   imageIcon: Assets.thrivvePhotosCustomerSupport,
            //   tap: () {},
            // ),
            const SizedBox(
              height: 24,
            ),
          ],
        ),
      ),
    );
  }

  showCarDetailsBottomSheet(BuildContext context, {CarDetail? carDetail}) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return CarDetailsBottomSheet(carDetail: carDetail);
      },
    );
  }

  showReportAnIncidentBottomSheet(
    BuildContext context,
  ) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return const ReportIncidentBottomSheet(
            listOfIncidentType: [IncidentType(id: 1, name: "test")]);
      },
    );
  }

  showDocumentBottomSheet(
      BuildContext context, List<CarDocument> listOfDocument) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return DocumentsBottomSheet(
          listOfCarDocuments: listOfDocument,
        );
      },
    );
  }

  showMaintenanceBottomSheet(BuildContext context,
      {List<CarDocument>? listOfMaintenance}) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return MaintenanceBottomSheet(
          listOfMaintenance: listOfMaintenance,
        );
      },
    );
  }
}
