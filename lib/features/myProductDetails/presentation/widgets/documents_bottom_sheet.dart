import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/myProductDetails/domain/entities/car_document.dart';
import 'package:thrivve/features/myProductDetails/presentation/widgets/pdf_viewer_bottom_sheet.dart';

import '../../../../core/util/general_helper.dart';
import '../../../../core/widget/list_tile_settings_widget.dart';
import '../../../../generated/assets.dart';
import 'bottom_sheet_title_widget.dart';

class DocumentsBottomSheet extends StatelessWidget {
  final List<CarDocument> listOfCarDocuments;

  const DocumentsBottomSheet({super.key, required this.listOfCarDocuments});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(bottom: 16),
      margin: const EdgeInsets.only(top: 64),
      decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0), topRight: Radius.circular(16.0))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BottomSheetTitleWidget(
            title: "documents".tr,
          ),
          listOfCarDocuments.isEmpty == true
              ? Flexible(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          Assets.thrivvePhotosNoTransactions,
                          width: 150,
                          height: 150,
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        Text(
                          "no_documents".tr,
                          style: TextStyle(
                              color: context?.black,
                              fontSize: 16,
                              fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  ),
                )
              : Flexible(
                  fit: FlexFit.tight,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ListView.builder(
                      shrinkWrap: false,
                      itemCount: listOfCarDocuments.length,
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return ListTileSettingsWidget(
                          title: listOfCarDocuments[index].name,
                          supTitle: "${listOfCarDocuments[index].date} "
                              " ${listOfCarDocuments[index].size?.isEmpty == true ? "" : "- ${listOfCarDocuments[index].size}"}",
                          imageIcon: Assets.thrivvePhotosDocument2,
                          widget: InkWell(
                              onTap: () async {
                                if (listOfCarDocuments[index].link != null &&
                                    listOfCarDocuments[index]
                                            .link
                                            ?.isNotEmpty ==
                                        true) {
                                  showDocumentInPDF(
                                    context,
                                    listOfCarDocuments[index].link,
                                    listOfCarDocuments[index].name,
                                  );
                                  // downloadDoc(
                                  //     controller.listOfCarDocuments[index].link);
                                }
                              },
                              child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: context.iconBoarderColor),
                                  child: Image.asset(
                                    Assets.thrivvePhotosDownloadIcon,
                                    width: 24,
                                    height: 24,
                                  ))),
                          tap: () {},
                        );
                      },
                    ),
                  )),
        ],
      ),
    );
  }

  showDocumentInPDF(BuildContext context, String? docPdfUrl, String? name) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return PdfViewerBottomSheet(
          isPDF: checkPDFFile(docPdfUrl),
          url: docPdfUrl,
          title: name,
        );
      },
    );
  }

  Future<String?> _findLocalPath() async {
    String? externalStorageDirPath;
    if (Platform.isAndroid) {
      try {
        Directory tempDir = await getTemporaryDirectory();
        externalStorageDirPath = tempDir.absolute.path;
      } catch (e) {
        final directory = await getExternalStorageDirectory();
        externalStorageDirPath = directory?.absolute.path;
      }
    } else if (Platform.isIOS) {
      externalStorageDirPath =
          (await getApplicationDocumentsDirectory()).absolute.path;
    }
    return externalStorageDirPath;
  }
}
