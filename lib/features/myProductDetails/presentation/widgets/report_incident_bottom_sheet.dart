import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/myProductDetails/presentation/widgets/bottom_sheet_title_widget.dart';
import 'package:thrivve/features/myProductDetails/presentation/widgets/yes_now_row.dart';

import '../../../../core/util/date_util.dart';
import '../../../../core/widget/my_text_field_date_picker.dart';
import '../../../../core/widget/text_filed_edit.dart';
import '../../../personalInfo/presentation/widgets/camera_gallery_bottom_sheet.dart';
import '../../domain/entities/incident_type.dart';
import '../manager/drive_to_own_bloc.dart';
import 'incident_photo_widget.dart';

class ReportIncidentBottomSheet extends StatefulWidget {
  final List<IncidentType>? listOfIncidentType;

  const ReportIncidentBottomSheet({
    super.key,
    this.listOfIncidentType,
  });

  @override
  State<ReportIncidentBottomSheet> createState() =>
      _ReportIncidentBottomSheetState();
}

class _ReportIncidentBottomSheetState extends State<ReportIncidentBottomSheet> {
  TextEditingController? locationController;

  TextEditingController? whatHappenedController;

  TextEditingController? dateController;

  TextEditingController? timeController;

  String? frontImage;

  String? backImage;

  String? sideImage;

  bool? didYouCallNajm;

  IncidentType? incidentType;

  @override
  void initState() {
    super.initState();
    locationController = TextEditingController();
    whatHappenedController = TextEditingController();
    dateController = TextEditingController();
    timeController = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    locationController?.dispose();
    whatHappenedController?.dispose();
    dateController?.dispose();
    timeController?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BottomSheetTitleWidget(
              title: "report_an_incident".tr,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "type".tr,
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: context.fillIconColor),
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Container(
                    width: double.maxFinite,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10.0, horizontal: 10.0),
                    decoration: BoxDecoration(
                      color: context.black!.withOpacity(0.04),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        width: 1,
                        color: context.whiteColor!,
                      ),
                    ),
                    child: widget.listOfIncidentType == null
                        ? const Center(child: CircularProgressIndicator())
                        : DropdownButton<IncidentType>(
                            borderRadius: BorderRadius.circular(8),
                            focusColor: context.black!.withOpacity(0.04),
                            underline: const SizedBox(),
                            isExpanded: true,
                            dropdownColor: context.whiteColor,
                            value: incidentType,
                            isDense: true,
                            elevation: 5,
                            icon: const Icon(Icons.keyboard_arrow_down_rounded),
                            iconEnabledColor: context.hintTextColor,
                            items: widget.listOfIncidentType!
                                .map((IncidentType? incidentType) =>
                                    DropdownMenuItem(
                                        value: incidentType,
                                        child: Text(
                                          incidentType?.name ?? "",
                                          style: TextStyle(
                                            color: context.black,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        )))
                                .toList(),
                            hint: Text(
                              "select_incident_type".tr,
                              style: TextStyle(
                                  color: context.hintTextColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400),
                            ),
                            onChanged: (value) {
                              setState(() {
                                incidentType = value;
                              });
                            }),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    "date".tr,
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: context.fillIconColor),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  MyTextFieldDatePicker(
                    dateFormat: DateUtil.getExpiryDateFormat(),
                    controller: dateController,
                    fillColor: context.black!.withOpacity(0.04),
                    labelText: "select_date".tr,
                    hintColor: context.hintTextColor!,
                    errorColorBorder: context.errorColorBorder!,
                    suffixIcon: Icon(
                      Icons.date_range_rounded,
                      color: context.blackIconColor!,
                    ),
                    lastDate: DateTime(DateTime.now().year + 100, 1),
                    firstDate: DateTime(1930),
                    initialDate: DateTime.now().add(const Duration(days: 1)),
                    onDateChanged: (selectedDate) {},
                    isClickable: true,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    "time".tr,
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: context.fillIconColor),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  MyTextFieldDatePicker(
                    dateFormat: DateUtil.getExpiryDateFormat(),
                    controller: timeController,
                    isTimePicker: true,
                    fillColor: context.black!.withOpacity(0.04),
                    labelText: "enter_time".tr,
                    hintColor: context.hintTextColor!,
                    errorColorBorder: context.errorColorBorder!,
                    suffixIcon: Icon(
                      Icons.access_time,
                      color: context.blackIconColor!,
                    ),
                    lastDate: DateTime(DateTime.now().year + 100, 1),
                    firstDate: DateTime(1930),
                    initialDate: DateTime.now().add(const Duration(days: 1)),
                    onDateChanged: (selectedDate) {},
                    isClickable: true,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Text("location".tr,
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: context?.fillIconColor)),
                  const SizedBox(
                    height: 8,
                  ),
                  TextFiledEditProfile(
                    isBorder: true,
                    onChanged: (val) {},
                    textInputType: TextInputType.text,
                    controller: locationController,
                    fillColor: context.black!.withOpacity(0.04),
                    hintText: "enter_location".tr,
                    hintColor: context.hintTextColor!,
                    errorColorBorder: context.errorColorBorder!,
                    validator: (t) {
                      return null;
                    },
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Text("what_happened".tr,
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: context?.fillIconColor)),
                  const SizedBox(
                    height: 8,
                  ),
                  TextFiledEditProfile(
                    isBorder: true,
                    onChanged: (val) {},
                    textInputType: TextInputType.text,
                    controller: whatHappenedController,
                    fillColor: context.black!.withOpacity(0.04),
                    hintText: "enter_what_happened".tr,
                    hintColor: context.hintTextColor!,
                    errorColorBorder: context.errorColorBorder!,
                    validator: (t) {
                      return null;
                    },
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    "photos".tr,
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: context.fillIconColor),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IncidentPhotoWidget(
                        isImageEmpty: frontImage == null,
                        onTap: () {
                          showSelectOptionBottomSheet(
                            providerContext: context,
                            onImageSelected: (filePath) {
                              Navigator.pop(context);
                              setState(() {
                                frontImage = filePath;
                              });
                            },
                          );
                        },
                        onDeleted: () {
                          setState(() {
                            frontImage = null;
                          });
                        },
                        title: "front".tr,
                        image: frontImage,
                      ),
                      IncidentPhotoWidget(
                        isImageEmpty: backImage == null,
                        onTap: () {
                          showSelectOptionBottomSheet(
                            providerContext: context,
                            onImageSelected: (filePath) {
                              Navigator.pop(context);
                              setState(() {
                                backImage = filePath;
                              });
                            },
                          );
                        },
                        onDeleted: () {
                          setState(() {
                            backImage = null;
                          });
                        },
                        title: "back".tr,
                        image: backImage,
                      ),
                      IncidentPhotoWidget(
                        isImageEmpty: sideImage == null,
                        onTap: () {
                          showSelectOptionBottomSheet(
                            providerContext: context,
                            onImageSelected: (filePath) {
                              Navigator.pop(context);
                              setState(() {
                                sideImage = filePath;
                              });
                            },
                          );
                        },
                        onDeleted: () {
                          setState(() {
                            sideImage = null;
                          });
                        },
                        title: "side".tr,
                        image: sideImage,
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text("did_you_call_najm".tr,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: context.fillIconColor,
                          )),
                      YesNowRow(
                        onTab: (bool? selection) {
                          setState(() {
                            didYouCallNajm = selection!;
                          });
                        },
                        currentSelection: didYouCallNajm,
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  // done button
                  SizedBox(
                    width: double.maxFinite,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: () {
                        context.read<DriveToOwnBloc>().add(
                              ReportIncidentEvent(
                                incidentType: incidentType?.name,
                                incidentDate: dateController?.text,
                                incidentTime: timeController?.text,
                                incidentLocation: locationController?.text,
                                incidentDescription:
                                    whatHappenedController?.text,
                                incidentFrontImage: frontImage,
                                incidentBackImage: backImage,
                                incidentSideImage: sideImage,
                                didYouCallNajm: didYouCallNajm,
                              ),
                            );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.appPrimaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      child: Text(
                        "done".tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: context?.whiteColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  showSelectOptionBottomSheet({
    required BuildContext providerContext,
    required Function(String?) onImageSelected,
  }) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: providerContext,
      builder: (BuildContext context) {
        return CameraGalleryFileBottomSheet(
          onFileSelected: (filePath) {
            onImageSelected(filePath);
          },
        );
      },
    );
  }
}
