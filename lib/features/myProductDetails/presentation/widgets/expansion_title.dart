import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class ExpansionTitle extends StatelessWidget {
  const ExpansionTitle({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          flex: 4,
          child: Row(
            children: [
              Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(90.0),
                  color: context.imageBackgroundColor,
                ),
                child: Center(
                  child: Image.asset(
                    Assets.thrivvePhotosCustomerSupport,
                    height: 24,
                    width: 24,
                  ),
                ),
              ),
              const SizedBox(
                width: 8.0,
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "talk_to_support".tr,
                      style: TextStyle(
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                          color: context?.black,
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      height: 4.0,
                    ),
                    Text(
                      "have_questions_about_anything".tr,
                      style: TextStyle(
                          fontSize: 12,
                          overflow: TextOverflow.ellipsis,
                          color: context?.statusBackground,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
