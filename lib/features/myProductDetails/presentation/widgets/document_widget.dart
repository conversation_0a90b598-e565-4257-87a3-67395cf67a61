import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/myProductDetails/presentation/widgets/pdf_viewer_bottom_sheet.dart';

import '../../../../core/util/general_helper.dart';
import '../../../../generated/assets.dart';

class DocumentWidget extends StatelessWidget {
  final String? docName;
  final String? docPdfUrl;

  const DocumentWidget({super.key, this.docName, this.docPdfUrl});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8)),
          color: context.containerColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "document".tr,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(
              height: 12,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Image.asset(
                      Assets.thrivvePhotosDocument,
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "name".tr,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(
                          height: 2,
                        ),
                        Text(
                          docName ?? "",
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    )
                  ],
                ),
                InkWell(
                    onTap: () async {
                      showDocumentInPDF(context, docPdfUrl);
                    },
                    child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: context.iconBoarderColor),
                        child: Image.asset(
                          Assets.thrivvePhotosDownloadIcon,
                          width: 24,
                          height: 24,
                        )))
              ],
            )
          ],
        ));
  }

  showDocumentInPDF(BuildContext context, String? docPdfUrl) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return PdfViewerBottomSheet(
          isPDF: checkPDFFile(docPdfUrl),
          url: docPdfUrl,
          title: "document".tr,
        );
      },
    );
  }
}
