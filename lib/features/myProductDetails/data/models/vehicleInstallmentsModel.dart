// To parse this JSON data, do
//
//     final installmentFromServer = installmentFromServerFromJson(jsonString);

import 'dart:convert';

InstallmentFromServer installmentFromServerFromJson(String str) =>
    InstallmentFromServer.fromJson(json.decode(str));

class InstallmentFromServer {
  InstallmentFromServer({
    this.status,
    this.data,
    this.message,
  });

  bool? status;
  List<Datum>? data;
  String? message;

  factory InstallmentFromServer.fromJson(Map<String, dynamic> json) =>
      InstallmentFromServer(
        status: json["status"],
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        message: json["message"],
      );
}

class Datum {
  Datum({
    this.creation,
    this.status,
    this.id,
    this.total,
    this.isDeleted,
    this.paidBy,
    this.vat,
    this.value,
    this.dueDate,
    this.paidDatetime,
  });

  DateTime? creation;
  String? status;
  int? id;
  dynamic total;
  bool? isDeleted;
  dynamic paidBy;
  dynamic vat;
  double? value;
  DateTime? dueDate;
  dynamic paidDatetime;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        creation:
            json["creation"] != null ? DateTime.parse(json["creation"]) : null,
        status: json["status"],
        id: json["id"],
        total: json["total"],
        isDeleted: json["is_deleted"],
        paidBy: json["paid_by"],
        vat: json["vat"],
        value: json["value"].toDouble(),
        dueDate:
            json["due_date"] != null ? DateTime.parse(json["due_date"]) : null,
        paidDatetime: json["paid_datetime"],
      );
}
