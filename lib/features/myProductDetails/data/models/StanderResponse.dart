// To parse this JSON data, do
//
//     final standerResponse = standerResponseFromJson(jsonString);

import 'dart:convert';

StanderResponse standerResponseFromJson(String str) =>
    StanderResponse.fromJson(json.decode(str));



class StanderResponse {
  StanderResponse({
    this.status,
    this.isDriveToRent,
    this.data,
    this.contract,
    this.message,
  });

  bool? status;
  bool? isDriveToRent;
  dynamic data;
  dynamic contract;
  String? message;

  factory StanderResponse.fromJson(Map<String, dynamic> json) =>
      StanderResponse(
        status: json["status"],
        data: json["data"],
        contract: json["contract"],
        isDriveToRent: json["is_drive_to_rent"],
        message: json["message"],
      );

}

class Data {
  Data({
    this.hasContract,
  });

  bool? hasContract;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        hasContract: json["has_contract"],
      );


}
