import 'package:thrivve/features/myProductDetails/domain/entities/product_details.dart';
import '../../domain/entities/car_details.dart';
import '../../domain/entities/car_document.dart';
import '../../domain/entities/installment.dart';

class ContractDetailsModel extends ProductDetails {
  @override
  String? name;
  @override
  String? type;
  @override
  double? totalPrice;
  @override
  String? imageUrl;
  @override
  String? remainingDate;
  @override
  double? paidAmount;
  @override
  String? nextPaymentDate;
  @override
  double? nextPaymentAmount;
  @override
  double? paidPercentage;
  @override
  List<Installment>? listOfInstallments;
  ProductType productTypeValue;
  @override
  int? id;

  VehicleDetails? vehicleDetails;

  List<Attachment>? attachmentsList;
  List<Attachment>? promissoryNotesAttachments;
  List<Attachment>? deliveryNotesAttachments;

  ContractDetailsModel({
    this.id,
    this.name,
    this.type,
    this.totalPrice,
    this.imageUrl,
    this.remainingDate,
    this.paidAmount,
    this.nextPaymentDate,
    this.nextPaymentAmount,
    this.paidPercentage,
    this.listOfInstallments,
    required this.productTypeValue,
    this.vehicleDetails,
    this.attachmentsList,
    this.promissoryNotesAttachments,
    this.deliveryNotesAttachments,
  }) : super(
          id: id,
          name: name,
          type: type,
          totalPrice: totalPrice,
          imageUrl: imageUrl,
          remainingDate: remainingDate,
          paidAmount: paidAmount,
          nextPaymentDate: nextPaymentDate,
          nextPaymentAmount: nextPaymentAmount,
          paidPercentage: paidPercentage,
          listOfInstallments: listOfInstallments,
          productType: productTypeValue,
          carDetail: CarDetail.fromServer3(vehicleDetails),
        );

  @override
  List<CarDocument> get documentsList {
    List<CarDocument> documents = [];
    if (attachmentsList != null) {
      documents.addAll(attachmentsList!.map((e) => CarDocument.fromServer2(e)));
    }
    if (promissoryNotesAttachments != null) {
      documents.addAll(
          promissoryNotesAttachments!.map((e) => CarDocument.fromServer2(e)));
    }
    if (deliveryNotesAttachments != null) {
      documents.addAll(
          deliveryNotesAttachments!.map((e) => CarDocument.fromServer2(e)));
    }
    return documents;
  }

  factory ContractDetailsModel.fromJson(Map<String, dynamic> json) {
    // Create Party object from the JSON
    Party? party = json['party'] != null ? Party.fromJson(json['party']) : null;

    return ContractDetailsModel(
      productTypeValue: json['product_type'] == 'vehicle'
          ? ProductType.vehicle
          : ProductType.device,
      id: json['id'] as int?,
      name: party != null
          ? (json['product_type'] == 'vehicle'
              ? party.manufacturerModel?.manufacturer?.manufacturerNameEn ?? ''
              : party.title ?? '')
          : '',
      type: party != null
          ? (json['product_type'] == 'vehicle'
              ? party.manufacturerModel?.manufacturerModelNameEn ?? ''
              : party.code ?? '')
          : '',
      totalPrice: (json['selling_price'] as num?)?.toDouble(),
      imageUrl: null,
      // Assuming imageUrl is not returned from the server
      remainingDate: json['remaining_date'] as String?,
      paidAmount: (json['total_paid_amount'] as num?)?.toDouble(),
      nextPaymentDate: json['next_installment_date'] as String?,
      nextPaymentAmount: (json['next_installment_amount'] as num?)?.toDouble(),
      paidPercentage: (json['paid_percentage'] as num?)?.toDouble(),
      attachmentsList: json['attachments'] != null
          ? (json['attachments'] as List<dynamic>?)
              ?.map((i) => Attachment.fromJson(i as Map<String, dynamic>))
              .toList()
          : null,
      promissoryNotesAttachments: json['promissory_notes_attachments'] != null
          ? (json['promissory_notes_attachments'] as List<dynamic>?)
              ?.map((i) => Attachment.fromJson(i as Map<String, dynamic>))
              .toList()
          : null,
      deliveryNotesAttachments: json['delivery_notes_attachments'] != null
          ? (json['delivery_notes_attachments'] as List<dynamic>?)
              ?.map((i) => Attachment.fromJson(i as Map<String, dynamic>))
              .toList()
          : null,

      vehicleDetails: json['product_type'] == 'vehicle'
          ? json['party'] != null
              ? VehicleDetails.fromJson(json['party'])
              : null
          : null,
      listOfInstallments: json['installments'] != null
          ? (json['installments'] as List<dynamic>?)
              ?.map((i) => Installment.fromServerListModel(
                  InstallmentModel.fromJson(i as Map<String, dynamic>)))
              .toList()
          : null,
    );
  }
}

class Attachment {
  final DateTime? date;
  final int? id;
  final String? link;
  final bool? isDeleted;
  final String? description;
  final String? user;
  final int? contractId;

  Attachment.fromJson(Map<String, dynamic> json)
      : date = json['date'] != null ? DateTime.tryParse(json['date']) : null,
        id = json['id'] as int?,
        link = json['link'] as String?,
        isDeleted = json['is_deleted'] as bool?,
        description = json['description'] as String?,
        user = json['user'] as String?,
        contractId = json['contract_id'] as int?;
}

class VehicleDetails {
  final ManufacturerModel? manufacturerModel;
  final int? manufactureYear;
  final String? descriptionEn;
  final String? fuelTypeValue;

  final String? color;
  final String? engineVolume;
  final DateTime? customIssueDate;
  final String? transmission;

  VehicleDetails({
    this.manufacturerModel,
    this.manufactureYear,
    this.descriptionEn,
    this.fuelTypeValue,
    this.color,
    this.engineVolume,
    this.customIssueDate,
    this.transmission,
  });

  factory VehicleDetails.fromJson(Map<String, dynamic> json) {
    return VehicleDetails(
      manufacturerModel: json['manufacturer_model'] != null
          ? ManufacturerModel.fromJson(json['manufacturer_model'])
          : null,
      manufactureYear: json['manufacture_year'] as int?,
      fuelTypeValue: json['fuel_type_obj']?['Value'] as String?,
      color: json['color']?['Value'] as String?,
      customIssueDate: json["custom_issue_date"] != null
          ? DateTime.tryParse(json["custom_issue_date"])
          : null,
      engineVolume: json['engine_size'] as String?,
      transmission: json['transmission'] as String?,
      descriptionEn: json['description_en'] as String?,
    );
  }
}

class ManufacturerModel {
  final Manufacturer? manufacturer;
  final String? manufacturerModelNameEn;

  ManufacturerModel({
    this.manufacturer,
    this.manufacturerModelNameEn,
  });

  factory ManufacturerModel.fromJson(Map<String, dynamic> json) {
    return ManufacturerModel(
      manufacturer: json['manufacturer'] != null
          ? Manufacturer.fromJson(json['manufacturer'])
          : null,
      manufacturerModelNameEn: json['manufacturer_model_name_en'] as String?,
    );
  }
}

class Manufacturer {
  final String? manufacturerNameEn;

  Manufacturer({this.manufacturerNameEn});

  factory Manufacturer.fromJson(Map<String, dynamic> json) {
    return Manufacturer(
      manufacturerNameEn: json['manufacturer_name_en'] as String?,
    );
  }
}

class Party {
  final String? title;
  final String? code;
  final ManufacturerModel? manufacturerModel;

  Party({
    this.title,
    this.manufacturerModel,
    this.code,
  });

  factory Party.fromJson(Map<String, dynamic> json) {
    return Party(
      title: json['title'] as String?,
      code: json['code'] as String?,
      manufacturerModel: json['manufacturer_model'] != null
          ? ManufacturerModel.fromJson(json['manufacturer_model'])
          : null,
    );
  }
}

class InstallmentModel {
  final String? dueDate;
  final double? total;
  final int? id;
  final String? status;

  InstallmentModel.fromJson(Map<String, dynamic> json)
      : status = json['status'] as String?,
        total = (json['total'] as num?)?.toDouble(),
        id = json['id'] as int?,
        dueDate = json['due_date'] as String?;
}
