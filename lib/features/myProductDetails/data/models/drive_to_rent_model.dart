class Contract {
  Contract({
    this.contractPdfTitle,
    this.price,
    this.contractPdf,
    this.vehicle,
    this.vehicleImages,
    this.totalInstallmentsAmount,
    this.contractsCount,
    this.remainingDaysFromToday,
    this.totalRentDays,
    this.percentagePassedDays,
    this.nextInstallments,
  });

  String? contractPdfTitle;
  dynamic price;
  String? contractPdf;
  Vehicle? vehicle;
  List<Image>? vehicleImages;
  dynamic totalInstallmentsAmount;
  dynamic contractsCount;
  dynamic remainingDaysFromToday;
  dynamic totalRentDays;
  double? percentagePassedDays;
  List<TInstallment>? nextInstallments;

  factory Contract.fromJson(Map<String, dynamic> json) => Contract(
        contractPdfTitle: json["contract_pdf_title"],
        price: json["price"],
        contractPdf: json["contract_pdf"],
        vehicle: Vehicle.fromJson(json["vehicle"]),
        vehicleImages: List<Image>.from(
            json["vehicle_images"].map((x) => Image.fromJson(x))),
        totalInstallmentsAmount: json["total_installments_amount"],
        contractsCount: json["contracts_count"],
        remainingDaysFromToday: json["remaining_days_from_today"],
        totalRentDays: json["total_rent_days"],
        percentagePassedDays: json["percentage_passed_days"].toDouble(),
        nextInstallments: List<TInstallment>.from(
            json["next_installments"].map((x) => TInstallment.fromJson(x))),
      );
}

class TInstallment {
  TInstallment({
    this.total,
    this.value,
    this.dueDate,
  });

  dynamic total;
  dynamic value;
  DateTime? dueDate;

  factory TInstallment.fromJson(Map<String, dynamic> json) => TInstallment(
        value: json["value"],
        total: json["total"],
        dueDate:
            json["due_date"] != null ? DateTime.parse(json["due_date"]) : null,
      );
}

class StatusContract {
  StatusContract({
    this.id,
    this.value,
  });

  int? id;
  String? value;

  factory StatusContract.fromJson(Map<String, dynamic> json) => StatusContract(
        id: json["id"],
        value: json["Value"],
      );
}

class Image {
  Image({
    this.url,
  });

  String? url;

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        url: json["url"],
      );
}

class Vehicle {
  Vehicle({
    this.descriptionEn,
    this.color,
    this.engineVolume,
    this.manufacturerModel,
    this.assetCategory,
    this.customIssueDate,
    this.manufactureYear,
    this.fuelTypeObj,
  });

  dynamic descriptionEn;
  StatusContract? color;
  String? engineVolume;
  ManufacturerModel? manufacturerModel;
  StatusContract? assetCategory;
  DateTime? customIssueDate;
  int? manufactureYear;
  StatusContract? fuelTypeObj;

  factory Vehicle.fromJson(Map<String, dynamic> json) => Vehicle(
        descriptionEn: json["description_en"],
        color: StatusContract.fromJson(json["color"]),
        engineVolume: json["engine_volume"],
        manufacturerModel:
            ManufacturerModel.fromJson(json["manufacturer_model"]),
        assetCategory: StatusContract.fromJson(json["asset_category"]),
        customIssueDate: json["custom_issue_date"] != null
            ? DateTime.parse(json["custom_issue_date"])
            : null,
        manufactureYear: json["manufacture_year"],
        fuelTypeObj: json["fuel_type_obj"] != null ? StatusContract.fromJson(json["fuel_type_obj"]) : null,
      );
}

class ManufacturerModel {
  ManufacturerModel({
    this.manufacturerModelNameAr,
    this.id,
    this.vehicleManufacturerId,
    this.manufacturer,
    this.manufacturerModelNameEn,
    this.creation,
  });

  String? manufacturerModelNameAr;
  int? id;
  int? vehicleManufacturerId;
  Manufacturer? manufacturer;
  String? manufacturerModelNameEn;
  DateTime? creation;

  factory ManufacturerModel.fromJson(Map<String, dynamic> json) =>
      ManufacturerModel(
        manufacturerModelNameAr: json["manufacturer_model_name_ar"],
        id: json["id"],
        vehicleManufacturerId: json["vehicle_manufacturer_id"],
        manufacturer: Manufacturer.fromJson(json["manufacturer"]),
        manufacturerModelNameEn: json["manufacturer_model_name_en"],
        creation: DateTime.parse(json["creation"]),
      );
}

class Manufacturer {
  Manufacturer({
    this.manufacturerNameEn,
    this.id,
    this.manufacturerNameAr,
    this.creation,
  });

  String? manufacturerNameEn;
  int? id;
  String? manufacturerNameAr;
  DateTime? creation;

  factory Manufacturer.fromJson(Map<String, dynamic> json) => Manufacturer(
        manufacturerNameEn: json["manufacturer_name_en"],
        id: json["id"],
        manufacturerNameAr: json["manufacturer_name_ar"],
        creation: DateTime.parse(json["creation"]),
      );
}
