import 'package:thrivve/features/under_processing/data/mappers/entry_type_extention.dart';
import 'package:thrivve/features/under_processing/data/models/under_processing_model.dart';
import 'package:thrivve/features/under_processing/domain/entities/under_processing_entity.dart';

extension UnderProcessingModelExtention on UnderProcessingModel {
  UnderProcessingEntity toEntity() {
    return UnderProcessingEntity(
      icon: icon,
      referenceId: referenceId,
      amount: amount,
      subTitle: subTitle,
      title: title,
      amountAbs: amountAbs,
      isTransaction: isTransaction,
      entryType: entryType.toEntry(),
    );
  }
}
