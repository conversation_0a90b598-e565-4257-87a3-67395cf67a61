import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/under_processing/domain/entities/input_under_processing_entity.dart';
import 'package:thrivve/features/under_processing/domain/entities/under_processing_entity.dart';

abstract class UnderProcessingRepository {
  Future<Either<Failure, List<UnderProcessingEntity>?>?> getAllUnderProcessing(
      InputUnderProcessingEntity? input);
}
