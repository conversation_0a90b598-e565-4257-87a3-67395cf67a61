import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/under_processing/data/data_sources/under_processing_data_source.dart';
import 'package:thrivve/features/under_processing/data/mappers/under_processing_model_extention.dart';
import 'package:thrivve/features/under_processing/domain/entities/input_under_processing_entity.dart';
import 'package:thrivve/features/under_processing/domain/entities/under_processing_entity.dart';
import 'package:thrivve/features/under_processing/domain/repositories/under_processing_repository.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/network/network_info.dart';

class UnderProcessingRepositoryImpl implements UnderProcessingRepository {
  final UnderProcessingDataSource underProcessingDataSource;
  final NetworkInfo? networkInfo;

  UnderProcessingRepositoryImpl({
    required this.underProcessingDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<UnderProcessingEntity>?>?> getAllUnderProcessing(
      InputUnderProcessingEntity? input) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result =
            await underProcessingDataSource.getAllUnderProcessing(input);
        return Right((result ?? []).map((e) => e.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
