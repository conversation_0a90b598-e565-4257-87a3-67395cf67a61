import 'package:equatable/equatable.dart';
import 'package:thrivve/features/dashboard/domain/enum/application_type_enum.dart';

class ThrivveUser extends Equatable {
  final String? accessToken;
  final String? refreshToken;
  final String? deviceFingerprint;
  final int? customerId;
  final int? deviceId;
  final String? fullName;
  final String? nickName;
  final String? countryCode;
  final String? language;
  final String? currency;
  final String? image;
  final String? mobile;
  final bool? isBiometricEnabled;
  final String? biometricType;
  final AppVersion? appVersion;
  final SecuritySettings? securitySettings;
  final ApplicationTypeEnum? applicationTypeEnum;

  const ThrivveUser({
    required this.mobile,
    required this.accessToken,
    required this.refreshToken,
    required this.deviceFingerprint,
    required this.customerId,
    required this.deviceId,
    required this.fullName,
    required this.nickName,
    required this.language,
    required this.currency,
    required this.countryCode,
    required this.image,
    required this.isBiometricEnabled,
    required this.biometricType,
    required this.appVersion,
    required this.securitySettings,
    this.applicationTypeEnum,
  });

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'device_fingerprint': deviceFingerprint,
      'customer_id': customerId,
      'device_id': deviceId,
      'full_name': fullName,
      'nick_name': nickName,
      'language': language,
      'currency': currency,
      'country_code': countryCode,
      'image': image,
      'is_biometric_enabled': isBiometricEnabled,
      'biometric_type': biometricType,
      'app_version_obj': appVersion?.toJson(),
      'security_settings': securitySettings?.toJson(),
      'mobile': mobile,
    };
  }

  Map<String, dynamic> toSentry() {
    return {
      'device_fingerprint': deviceFingerprint,
      'customer_id': customerId,
      'device_id': deviceId,
      'full_name': fullName,
      'nick_name': nickName,
      'language': language,
      'currency': currency,
      'country_code': countryCode,
      'image': image,
      'is_biometric_enabled': isBiometricEnabled,
      'biometric_type': biometricType,
      'app_version_obj': appVersion?.toJson(),
      'security_settings': securitySettings?.toJson(),
      'mobile': mobile,
    };
  }

  factory ThrivveUser.fromJson(Map<String, dynamic> json) {
    return ThrivveUser(
      mobile: json['mobile'],
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      deviceFingerprint: json['device_fingerprint'],
      customerId: json['customer_id'],
      deviceId: json['device_id'],
      fullName: json['full_name'],
      nickName: json['nick_name'],
      language: json['language'],
      currency: json['currency'],
      countryCode: json['country_code'],
      image: json['image'],
      isBiometricEnabled: json['is_biometric_enabled'],
      biometricType: json['biometric_type'],
      appVersion: json['app_version_obj'] != null
          ? AppVersion.fromJson(json['app_version_obj'])
          : null,
      securitySettings: json['security_settings'] != null
          ? SecuritySettings.fromJson(json['security_settings'])
          : null,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        deviceFingerprint,
        customerId,
        deviceId,
        fullName,
        nickName,
        language,
        currency,
        countryCode,
        image,
        isBiometricEnabled,
        biometricType,
        appVersion,
        securitySettings,
        applicationTypeEnum,
        mobile,
      ];
}

class AppVersion extends Equatable {
  final String? ios;
  final String? android;
  final String? desc;
  final String? buttonText;
  final String? title;

  const AppVersion({
    required this.ios,
    required this.android,
    required this.desc,
    required this.buttonText,
    required this.title,
  });

  Map<String, dynamic> toJson() {
    return {
      'ios': ios,
      'android': android,
      'desc': desc,
      'button_text': buttonText,
      'title': title,
    };
  }

  factory AppVersion.fromJson(Map<String, dynamic> json) {
    return AppVersion(
      ios: json['ios'],
      android: json['android'],
      desc: json['desc'],
      buttonText: json['button_text'],
      title: json['title'],
    );
  }

  @override
  List<Object?> get props => [
        ios,
        android,
        desc,
        buttonText,
        title,
      ];
}

class SecuritySettings extends Equatable {
  final bool? hasPinCode;
  final bool? isBiometricEnabled;
  final String? biometricType;

  const SecuritySettings({
    required this.hasPinCode,
    required this.isBiometricEnabled,
    required this.biometricType,
  });

  Map<String, dynamic> toJson() {
    return {
      'has_pin_code': hasPinCode,
      'is_biometric_enabled': isBiometricEnabled,
      'biometric_type': biometricType,
    };
  }

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      hasPinCode: json['has_pin_code'],
      isBiometricEnabled: json['is_biometric_enabled'],
      biometricType: json['biometric_type'],
    );
  }

  @override
  List<Object?> get props => [
        hasPinCode,
        isBiometricEnabled,
        biometricType,
      ];
}
