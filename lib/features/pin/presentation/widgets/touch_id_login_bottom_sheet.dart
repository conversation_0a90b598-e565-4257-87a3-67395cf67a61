import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class TouchIDLoginBottomSheet extends StatefulWidget {
  const TouchIDLoginBottomSheet({
    super.key,
  });

  @override
  State<TouchIDLoginBottomSheet> createState() =>
      _TouchIDLoginBottomSheetState();
}

class _TouchIDLoginBottomSheetState extends State<TouchIDLoginBottomSheet> {
  String _authorized = 'Not Authorized';

  Future<void> _authenticateWithBiometrics() async {
    bool authenticated = false;
    final LocalAuthentication auth = LocalAuthentication();
    try {
      setState(() {
        _authorized = 'Authenticating';
      });
      authenticated = await auth.authenticate(
        localizedReason:
            'Scan your fingerprint (or face or whatever) to authenticate',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
      setState(() {
        _authorized = 'Authenticating';
      });
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print(e);
      }
      setState(() {
        _authorized = 'Error - ${e.message}';
      });
      return;
    }
    if (!mounted) {
      return;
    }

    final String message = authenticated ? 'Authorized' : 'Not Authorized';
    setState(() {
      _authorized = message;
    });
  }

  @override
  void initState() {
    _authenticateWithBiometrics();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.only(
            top: 16,
            bottom: MediaQuery.of(context).viewInsets.bottom + 32,
          ),
          decoration: BoxDecoration(
              color: context.bottomsheetColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.0),
                  topRight: Radius.circular(16.0))),
          child: Column(
            children: [
              AppBar(
                leadingWidth: 48,
                leading: Container(
                  margin: const EdgeInsets.only(left: 8),
                  child: Image.asset(
                    Assets.thrivvePhotosThrivveBlackLogo,
                    width: 40,
                    height: 40,
                  ),
                ),
                title: Text(
                  "login_with_fingerprint".tr,
                  style: TextStyle(
                      color: context.black,
                      fontSize: 14,
                      fontWeight: FontWeight.w500),
                ),
              ),
              Divider(
                height: 1,
                thickness: 1,
                color: context.cardHomeContainer,
              ),
              const SizedBox(
                height: 70,
              ),
              Image.asset(
                Assets.thrivvePhotosFingerprint,
                width: 56,
                height: 56,
              ),
              const SizedBox(
                height: 16,
              ),
              Text(
                "touch_sensor_msj".tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: context.statusBackground,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(
                height: 56,
              ),
              InkWell(
                onTap: () {},
                child: Text(
                  _authorized,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: context.appPrimaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(
                height: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
