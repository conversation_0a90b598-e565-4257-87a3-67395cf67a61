import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class DotPin extends StatelessWidget {
  final bool fillStatus;
  final bool hasError;

  const DotPin({super.key, required this.fillStatus, this.hasError = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 16.w,
      height: 16.h,
      decoration: BoxDecoration(
        border: Border.all(
          color: fillStatus
              ? context.outlineButtonColor
              : hasError
                  ? Colors.red
                  : context.black!.withOpacity(0.4),
          width: 1.w,
        ),
        shape: BoxShape.circle,
        color: fillStatus ? context.outlineButtonColor : context.whiteColor,
      ),
    );
  }
}
