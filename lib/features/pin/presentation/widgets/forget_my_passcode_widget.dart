import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/top_bar_widget.dart';
import 'package:thrivve/features/pin/presentation/manager/pin_bloc.dart';

import '../../../../core/widget/border_button.dart';
import '../../../../core/widget/button.dart';
import '../../../../generated/assets.dart';
import '../../../onboarding/presentation/widgets/circle_avatar_widget.dart';

class ForgetMyPasscodeWidget extends StatelessWidget {
  final Function() onPageChange;
  final VoidCallback onClickHelp;

  const ForgetMyPasscodeWidget({
    required this.onPageChange,
    required this.onClickHelp,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const TopBar(),
        SizedBox(height: 16.h),
        const CircleAvatarWidget(image: Assets.thrivvePhotosForgetMyPasscode),
        SizedBox(height: 28.h),
        Text(
          "forgot_my_passcode_title".tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: context.black,
            fontWeight: FontWeight.w600,
            fontSize: 15.sp,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          "forgot_my_passcode_msj".tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: context.lightBlack,
            fontWeight: FontWeight.w400,
            fontSize: 11.sp,
          ),
        ),
        SizedBox(height: 32.h),
        BlocBuilder<PinBloc, PinState>(builder: (context, state) {
          return Button(
            widget: state.sendOtpStatus == AppStatus.loading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ))
                : null,
            text: "forgot_my_passcode_ok_btn".tr,
            onTab: () {
              onPageChange();
            },
            enable: true,
            height: 36.h,
          );
        }),
        SizedBox(height: 16.h),
        BorderButton(
          text: "forgot_my_passcode_no_btn".tr,
          fontWeight: FontWeight.w600,
          fontSize: 11.sp,
          onTab: () {
            Get.back();
          },
        ),
        SizedBox(height: 16.h),
        InkWell(
          onTap: onClickHelp,
          child: Center(
            child: DecoratedBox(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: context.appPrimaryColor,
                    width: 1.0,
                  ),
                ),
              ),
              child: Text(
                "need_help".tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12.sp,
                  color: context.outlineButtonColor,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }
}
