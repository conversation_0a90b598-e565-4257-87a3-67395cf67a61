import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/button.dart';

import '../../../onboarding/presentation/widgets/top_bar_widget.dart';

class QuestionMarkBottomSheetWidget extends StatelessWidget {
  final String? title;

  final String? description;

  final String? textButton;

  const QuestionMarkBottomSheetWidget({
    super.key,
    this.title,
    this.description,
    this.textButton,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.0.r),
            topRight: Radius.circular(16.0.r),
          ),
        ),
        child: Column(
          children: [
            const TopBar(),
            SizedBox(
              height: 20.h,
            ),
            Text(
              description ?? "",
              style: TextStyle(
                color: context.lightBlack,
                fontSize: 11.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: 28.h,
            ),
            Visibility(
              visible: textButton != null,
              child: Button(
                  height: 36,
                  text: textButton ?? "",
                  onTab: () {
                    Get.back();
                  }),
            ),
            SizedBox(
              height: 34.h,
            ),
          ],
        ),
      ),
    );
  }
}
