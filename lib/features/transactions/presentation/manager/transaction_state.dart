part of 'transaction_bloc.dart';

class TransactionState extends Equatable {
  const TransactionState({
    this.getListOfTransactionStatuses = AppStatus.initial,
    this.errorMessage,
    this.page = 1,
    this.hasMoreItems,
    this.listOfTransactions,
    this.selectedTransactionsFilterType,
    this.getListOfTransactionFiltersStatuses = AppStatus.initial,
    this.startDate,
    this.endDate,
    this.filterDateRange,
    this.isFilteredByDate,
    this.listOfTransactionsFilters,
    this.searchText,
    this.isFilteredByType,
    this.listOfFilters,
  });

  final List<Filter>? listOfFilters;
  final List<TransactionsFilter?>? listOfTransactionsFilters;

  final List<Transaction>? listOfTransactions;
  final AppStatus getListOfTransactionStatuses;
  final AppStatus getListOfTransactionFiltersStatuses;
  final TransactionsFilter? selectedTransactionsFilterType;

  final bool? isFilteredByDate;

  final bool? isFilteredByType;

  final DateRange? filterDateRange;

  final String? startDate;
  final String? endDate;

  final String? searchText;
  final String? errorMessage;
  final int? page;
  final bool? hasMoreItems;

  TransactionState copyWith({
    List<Transaction>? Function()? listOfTransactions,
    List<Filter>? Function()? listOfFilters,
    List<TransactionsFilter?>? Function()? listOfTransactionsFilters,
    AppStatus Function()? getListOfTransactionStatuses,
    AppStatus Function()? getListOfTransactionFiltersStatuses,
    int? Function()? page,
    bool? Function()? hasMoreItems,
    String? Function()? searchText,
    String? Function()? errorMessage,
    String? Function()? startDate,
    String? Function()? endDate,
    TransactionsFilter? Function()? selectedTransactionsFilterType,
    DateRange? Function()? filterDateRange,
    bool? Function()? isFilteredByDate,
    bool? Function()? isFilteredByType,
  }) {
    return TransactionState(
      page: page != null ? page() : this.page,
      listOfTransactionsFilters: listOfTransactionsFilters != null
          ? listOfTransactionsFilters()
          : this.listOfTransactionsFilters,
      getListOfTransactionFiltersStatuses:
          getListOfTransactionFiltersStatuses != null
              ? getListOfTransactionFiltersStatuses()
              : this.getListOfTransactionFiltersStatuses,
      listOfFilters:
          listOfFilters != null ? listOfFilters() : this.listOfFilters,
      searchText: searchText != null ? searchText() : this.searchText,
      isFilteredByDate:
          isFilteredByDate != null ? isFilteredByDate() : this.isFilteredByDate,
      isFilteredByType:
          isFilteredByType != null ? isFilteredByType() : this.isFilteredByType,
      getListOfTransactionStatuses: getListOfTransactionStatuses != null
          ? getListOfTransactionStatuses()
          : this.getListOfTransactionStatuses,
      selectedTransactionsFilterType: selectedTransactionsFilterType != null
          ? selectedTransactionsFilterType()
          : this.selectedTransactionsFilterType,
      filterDateRange:
          filterDateRange != null ? filterDateRange() : this.filterDateRange,
      listOfTransactions: listOfTransactions != null
          ? listOfTransactions()
          : this.listOfTransactions,
      hasMoreItems: hasMoreItems != null ? hasMoreItems() : this.hasMoreItems,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
      startDate: startDate != null ? startDate() : this.startDate,
      endDate: endDate != null ? endDate() : this.endDate,
    );
  }

  @override
  List<Object?> get props => [
        getListOfTransactionFiltersStatuses,
        page,
        listOfFilters,
        listOfTransactionsFilters,
        searchText,
        isFilteredByDate,
        isFilteredByType,
        hasMoreItems,
        selectedTransactionsFilterType,
        filterDateRange,
        listOfTransactions,
        errorMessage,
        startDate,
        endDate,
        getListOfTransactionStatuses,
      ];
}
