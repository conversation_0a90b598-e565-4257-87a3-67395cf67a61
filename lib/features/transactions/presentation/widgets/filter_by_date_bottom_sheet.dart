import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/date_util.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/transactions/presentation/widgets/date_filter_tag.dart';

class FilterByDateBottomSheet extends StatefulWidget {
  Function(String startDate, String endDate, DateRange dateRange)
      onDateSelected;

  String? startDate;
  String? endDate;
  DateRange dateRangeValue = DateRange.custom;

  FilterByDateBottomSheet({
    super.key,
    required this.onDateSelected,
    this.startDate,
    this.endDate,
    this.dateRangeValue = DateRange.custom,
  });

  @override
  State<FilterByDateBottomSheet> createState() =>
      _FilterByDateBottomSheetState();
}

class _FilterByDateBottomSheetState extends State<FilterByDateBottomSheet> {
  DateTime? selectedStartDate;
  DateTime? selectedEndDate;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 40.h),
      width: MediaQuery.sizeOf(context).width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
        color: context.bottomsheetColor,
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: MediaQuery.sizeOf(context).width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // x icon with circle background
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Icon(
                        Icons.close,
                        size: 20.w,
                        color: context.lightBlack,
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        setState(() {
                          widget.startDate = null;
                          widget.endDate = null;
                          selectedEndDate = null;
                          selectedStartDate = null;
                          widget.dateRangeValue = DateRange.custom;
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            color: context.appPrimaryColor),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20.w, vertical: 8.h),
                          child: CustomTextWidget(
                            title: "clear".tr,
                            color: Colors.white,
                            size: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
              CustomTextWidget(
                title: "select_a_data_range".tr,
                color: context.black,
                size: 14,
                fontWeight: FontWeight.w500,
              ),
              SizedBox(height: 16.h),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    DateFilterTag(
                      text: 'last_month'.tr,
                      isSelected: widget.dateRangeValue == DateRange.lastMonth,
                      onSelect: (value) {
                        if (value) {
                          setState(() {
                            widget.dateRangeValue = DateRange.lastMonth;
                          });

                          widget.onDateSelected(
                            DateUtil.getLastMonthDate()[0],
                            DateUtil.getLastMonthDate()[1],
                            DateRange.lastMonth,
                          );
                          Get.back();
                        } else {
                          setState(() {
                            widget.startDate = null;
                            widget.endDate = null;
                            widget.dateRangeValue = DateRange.custom;
                          });
                        }
                      },
                    ),
                    SizedBox(width: 8.w),
                    DateFilterTag(
                      text: "last_quarter".tr,
                      isSelected:
                          widget.dateRangeValue == DateRange.lastQuarter,
                      onSelect: (value) {
                        if (value) {
                          setState(() {
                            widget.dateRangeValue = DateRange.lastQuarter;
                          });
                          widget.onDateSelected(
                            DateUtil.getLastQuarterDate()[0],
                            DateUtil.getLastQuarterDate()[1],
                            DateRange.lastQuarter,
                          );
                          Get.back();
                        } else {
                          setState(() {
                            widget.startDate = null;
                            widget.endDate = null;
                            widget.dateRangeValue = DateRange.custom;
                          });
                        }
                      },
                    ),
                    SizedBox(width: 8.w),
                    DateFilterTag(
                      text: 'last_year'.tr,
                      isSelected: widget.dateRangeValue == DateRange.lastYear,
                      onSelect: (value) {
                        if (value) {
                          setState(() {
                            widget.dateRangeValue = DateRange.lastYear;
                          });
                          widget.onDateSelected(
                            DateUtil.getLastYearDate()[0],
                            DateUtil.getLastYearDate()[1],
                            DateRange.lastYear,
                          );
                          Get.back();
                        } else {
                          setState(() {
                            widget.startDate = null;
                            widget.endDate = null;
                            widget.dateRangeValue = DateRange.custom;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
              InkWell(
                onTap: () {
                  showDatePickerBottomSheet(context, (date, dateTime) {
                    setState(() {
                      widget.startDate = date;
                      selectedStartDate = dateTime;
                    });
                  }, endDate: selectedEndDate);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextWidget(
                          title: "start".tr,
                          color: context.black,
                          size: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        SizedBox(height: 8.h),
                        CustomTextWidget(
                          title: widget.startDate ?? "select_a_date".tr,
                          color: context.hintTextColor2,
                          size: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ],
                    ),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: context.black,
                      size: 14.sp,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
              InkWell(
                onTap: () {
                  showDatePickerBottomSheet(context, (date, dateTime) {
                    setState(() {
                      widget.endDate = date;
                      selectedEndDate = dateTime;
                    });
                  }, startDate: selectedStartDate);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextWidget(
                          title: "end".tr,
                          color: context.black,
                          size: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        SizedBox(height: 8.h),
                        CustomTextWidget(
                          title: widget.endDate ?? "select_a_date".tr,
                          color: context.hintTextColor2,
                          size: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ],
                    ),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: context.black,
                      size: 14.sp,
                    ),
                  ],
                ),
              ),
            ],
          ),
          InkWell(
            onTap: () {
              if (widget.startDate == null || widget.endDate == null) {
                Get.snackbar(
                  "err".tr,
                  "please_select_a_date_range".tr,
                  backgroundColor: context.whiteColor.withOpacity(0.8),
                  colorText: context.black,
                  snackPosition: SnackPosition.TOP,
                  margin: EdgeInsets.all(16.w),
                  borderRadius: 8.r,
                  duration: const Duration(seconds: 3),
                );
                return;
              } else if (DateUtil.isBefore(
                  widget.endDate!, widget.startDate!)) {
                Get.snackbar(
                  "err".tr,
                  "end_date_cannot_be_before_start_date".tr,
                  backgroundColor: context.whiteColor!.withOpacity(0.8),
                  colorText: context.black,
                  snackPosition: SnackPosition.TOP,
                  margin: EdgeInsets.all(16.w),
                  borderRadius: 8.r,
                  duration: const Duration(seconds: 3),
                );
                return;
              }
              widget.onDateSelected(
                widget.startDate!,
                widget.endDate!,
                DateRange.custom,
              );
              Get.back();
            },
            child: Container(
              alignment: Alignment.center,
              width: MediaQuery.sizeOf(context).width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: context.appPrimaryColor),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 15.h),
                child: CustomTextWidget(
                  title: "show_activities".tr,
                  color: Colors.white,
                  size: 14,
                  fontWeight: FontWeight.w500,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void showDatePickerBottomSheet(
      BuildContext context, Function(String, DateTime) onDateSelected,
      {DateTime? startDate, DateTime? endDate}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) {
          late int year = DateTime.now().year;
          return SingleChildScrollView(
            child: Container(
              margin: EdgeInsets.only(top: 40.h),
              width: MediaQuery.sizeOf(context).width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
                color: context.bottomsheetColor,
              ),
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomTextWidget(
                        title: "choose_a_start_date".tr,
                        color: context.black,
                        size: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      // close icon with circle background
                      InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          height: 24.h,
                          width: 24.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(90.r),
                            color: context.hintTextColor2,
                          ),
                          child: Icon(
                            Icons.close,
                            size: 14.sp,
                            color: context.whiteColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: CalendarDatePicker(
                      initialDate: null,
                      firstDate: startDate ?? DateTime(2020),
                      lastDate: endDate ?? DateTime(2050),
                      onDateChanged: (date) {
                        print(date);
                        // check if the selected is year don't do action
                        if (date.year != year) {
                          year = date.year;
                          return;
                        }

                        onDateSelected(
                            DateUtil.getExpiryDateFormatDate(date) ?? "", date);
                        Get.back();
                      },
                      selectableDayPredicate: (date) {
                        if (date.isAfter(DateTime.now())) {
                          return false;
                        }
                        return true;
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }
}

// date range enum
enum DateRange {
  lastMonth,
  lastQuarter,
  lastYear,
  custom,
}
