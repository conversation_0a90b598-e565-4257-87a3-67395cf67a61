import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

class TransactionFilterTags extends StatelessWidget {
  final String text;
  final Color? color;
  final Color? textColor;
  final bool isSelected;
  final Function() onSelect;
  final Function() onClean;

  const TransactionFilterTags({
    super.key,
    required this.text,
    this.color,
    this.textColor,
    this.isSelected = false,
    required this.onSelect,
    required this.onClean,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onSelect(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: isSelected
              ? context.appPrimaryColor
              : color ?? context.containerColor,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color:
                isSelected ? context.appPrimaryColor : context.iconBoarderColor,
          ),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomTextWidget(
                title: text,
                color: isSelected ? Colors.white : (textColor ?? context.black),
                size: 10,
                fontWeight: FontWeight.w500,
              ),
              isSelected
                  ? Row(
                      children: [
                        SizedBox(width: 4.w),
                        InkWell(
                          onTap: () => onClean(),
                          child: Container(
                            height: 14.h,
                            width: 14.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(90.r),
                              border: Border.all(color: Colors.white),
                            ),
                            child: Icon(Icons.close,
                                size: 10.sp, color: Colors.white),
                          ),
                        ),
                      ],
                    )
                  : SizedBox(width: 0.w),
            ],
          ),
        ),
      ),
    );
  }
}
