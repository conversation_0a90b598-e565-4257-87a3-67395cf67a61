import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/helper.dart';
import '../../../../generated/assets.dart';
import '../../../mainHome/presentation/manager/main_home_bloc.dart';
import '../../../onboarding/presentation/widgets/phone_number_input.dart';
import '../manager/updateMobile/update_mobile_bloc.dart';
import '../widgets/sign_up_bottom_sheet_widget.dart';

class UpdateMobileScreen extends StatelessWidget {
  UpdateMobileScreen({super.key});

  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<UpdateMobileBloc>()..add(GetCountriesEvent()),
      child: MultiBlocListener(
        listeners: [
          BlocListener<UpdateMobileBloc, UpdateMobileState>(
            listenWhen: (previous, current) {
              return current.status != previous.status;
            },
            listener: (context, state) {
              switch (state.status) {
                case AppStatus.loading:
                  showLoaderDialog(context);
                  break;
                case AppStatus.success:
                  dismissLoaderDialog(context);
                  verifyYourMobileNumber(
                      context: context, mobile: state.mobileNum);
                  break;
                case AppStatus.failure:
                  dismissLoaderDialog(context);
                  showGeneralBottomSheet(
                    context: context,
                    title: "err".tr,
                    description: state.errorMessage ?? '',
                    icon: Assets.thrivvePhotosX,
                    iconColor: context.whiteColor,
                    iconBackgroundColor: context.logoutBtnBackground,
                    firstBtnText: 'got_it'.tr,
                    firstBtnOnClick: () {
                      Get.back();
                    },
                  );
                  break;
                default:
                  break;
              }
            },
          ),
          BlocListener<UpdateMobileBloc, UpdateMobileState>(
            listenWhen: (previous, current) {
              return current.verifyStatus != previous.verifyStatus;
            },
            listener: (context, state) {
              switch (state.verifyStatus) {
                case AppStatus.loading:
                  showLoaderDialog(context);
                  break;
                case AppStatus.success:
                  getIt<IAnalyticsLogger>()
                      .logEvent(AnalyticsActions.phoneNumberChanged);
                  dismissLoaderDialog(context);
                  Get.back();
                  showGeneralBottomSheet(
                      context: context,
                      title: state.verifyOtp?.title ??
                          'mobile_number_updated_successfully'.tr,
                      description: state.verifyOtp?.message ??
                          'mobile_number_updated_successfully_desc'.tr,
                      icon: Assets.thrivvePhotosSuccess,
                      iconColor: context.whiteColor,
                      iconBackgroundColor: context.greenColor,
                      firstBtnText: 'got_it'.tr,
                      firstBtnOnClick: () {
                        Get.back();
                        Get.back(result: true);
                      },
                      onCloseClick: () {
                        Get.back();
                        Get.back(result: true);
                      });
                  break;
                case AppStatus.failure:
                  dismissLoaderDialog(context);
                  showGeneralBottomSheet(
                    context: context,
                    title: "err".tr,
                    description: state.errorMessage ?? '',
                    icon: Assets.thrivvePhotosX,
                    iconColor: context.whiteColor,
                    iconBackgroundColor: Colors.red,
                    firstBtnText: 'got_it'.tr,
                    firstBtnOnClick: () {
                      Get.back();
                    },
                  );
                  break;
                default:
                  break;
              }
            },
          ),
        ],
        child: BlocBuilder<UpdateMobileBloc, UpdateMobileState>(
          builder: (context, state) {
            return Scaffold(
              appBar: AppBar(
                leadingWidth: 66.w,
                leading: Center(
                  child: Container(
                    height: 40.h,
                    width: 40.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: context.appBackgroundColor,
                    ),
                    child: IconButton(
                        onPressed: () {
                          // refresh the previous screen when the user goes back after editing the mobile number.
                          if (state.verifyStatus == AppStatus.success) {
                            Get.back(result: true);
                          } else {
                            Get.back();
                          }
                        },
                        icon: Icon(
                          Icons.arrow_back_rounded,
                          color: context?.black,
                          size: 20.w,
                        )),
                  ),
                ),
              ),
              body: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 12.h),
                        Text(
                          'update_your_mobile_number'.tr,
                          style: TextStyle(
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600,
                            color: context.black,
                          ),
                        ),
                        SizedBox(height: 36.h),
                        Text(
                          'update_your_mobile_number_desc'.tr,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            color: context.lightBlack,
                          ),
                        ),
                        SizedBox(height: 20.h),
                        PhoneNumberInput(
                          autoFocus: false,
                          focusNode: _focusNode,
                          getCountriesLoading:
                              state.getCountriesStatus == AppStatus.loading,
                          listOfCounties: state.listOfCountries,
                          readyToSubmit: (readyToSubmit, value) {
                            context
                                .read<UpdateMobileBloc>()
                                .add(UpdateMobileReadyToSubmitEvent(
                                  readyToSubmit: readyToSubmit,
                                  mobileNum: value,
                                ));
                          },
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Divider(
                        color: context.borderLeaseColor,
                        thickness: 0.4.h,
                        height: 0,
                      ),
                      SizedBox(height: 16.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: CustomButton(
                          isLoading: state.status == AppStatus.loading,
                          enabled: state.readyToSubmit ?? false,
                          text: "save_changes_btn".tr,
                          onPressed: () {
                            // hide the keyboard
                            // _hideKeyboard();
                            _hideKeyboard();

                            showGeneralBottomSheet(
                              context: context,
                              showCloseBtn: false,
                              title: 'is_your_mobile_correct'.tr,
                              description:
                                  formatPhoneNumber(state.mobileNum ?? ''),
                              icon: null,
                              descTextDirection: TextDirection.ltr,
                              firstBtnText: 'yes_its_right'.tr,
                              secondBtnText: 'no_i_want_to_edit_it'.tr,
                              firstBtnOnClick: () {
                                // context.read<UpdateMobileBloc>().add(
                                //     SendOtpEvent(mobileNum: state.mobileNum));
                                Get.back();
                                context
                                    .read<UpdateMobileBloc>()
                                    .add(SendOtpEvent(
                                      mobileNum: state.mobileNum,
                                    ));
                              },
                              secondBtnOnClick: () {
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        ),
                      ),
                      SizedBox(height: 26.h),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _hideKeyboard() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus(); // Explicitly unfocus
    }
  }

  void verifyYourMobileNumber({
    required BuildContext context,
    String? mobile,
  }) {
    final updateMobileBloc = BlocProvider.of<UpdateMobileBloc>(context);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.containerColor,
      builder: (context) {
        return MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: updateMobileBloc,
            ),
            BlocProvider.value(
              value: getIt<MainHomeBloc>(),
            ),
          ],
          child: MultiBlocListener(
            listeners: [
              BlocListener<MainHomeBloc, MainHomeState>(
                listenWhen: (previous, current) {
                  return current.supportUrl != previous.supportUrl;
                },
                listener: (context, state) {
                  if (state.supportUrl != null) {
                    openWhatsApp(url: state.supportUrl ?? '');
                  }
                },
              ),
            ],
            child: BlocBuilder<UpdateMobileBloc, UpdateMobileState>(
              builder: (context, state) {
                return SignUpBottomSheetWidget(
                  mobileNum: state.mobileNum ?? '',
                  otpCode: state.otpCode ?? '',
                  onVerificationSubmit: (mobileNumber, otpCode) {
                    updateMobileBloc.add(VerifyOtpEvent(
                      mobileNum: mobileNumber ?? '',
                      otpNum: otpCode ?? '',
                    ));
                  },
                  onResendOTP: () {
                    updateMobileBloc.add(ResentVerificationCodeEvent(
                      mobileNum: state.mobileNum ?? '',
                    ));
                  },
                  onBackClick: () {
                    Navigator.of(context).pop();
                  },
                  needHelpOnClick: () {
                    context.read<MainHomeBloc>().add(GetSupportUrlEvent());
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}
