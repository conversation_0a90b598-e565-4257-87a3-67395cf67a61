import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/util/const.dart';
import 'package:thrivve/core/utils/biometric_auth_utility.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';

import '../../../../core/api/api_settings.dart';
import '../../../../core/app_routes.dart';
import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../../generated/assets.dart';
import '../widgets/profile_menu_item.dart';
import '../widgets/section_title.dart';

class PrivacyAndSecurityScreen extends StatelessWidget {
  const PrivacyAndSecurityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
        value: getIt<MainHomeBloc>()
          ..add(CheckIsDeviceSupportAndTypeBioMetricEvent()),
        child: MultiBlocListener(
            listeners: [
              BlocListener<MainHomeBloc, MainHomeState>(
                listenWhen: (previous, current) =>
                    previous.isDeviceSupportBioMetric !=
                        current.isDeviceSupportBioMetric ||
                    previous.isBioMetricEnabledForThisDevice !=
                        current.isBioMetricEnabledForThisDevice ||
                    previous.isPinEnabledForThisDevice !=
                        current.isPinEnabledForThisDevice,
                listener: (context, state) {},
              ),
            ],
            child: BlocBuilder<MainHomeBloc, MainHomeState>(
              builder: (context, state) {
                return Scaffold(
                  appBar: AppBar(
                    leadingWidth: 66.w,
                    leading: Center(
                      child: Container(
                        height: 40.h,
                        width: 40.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: context.appBackgroundColor,
                        ),
                        child: IconButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            icon: Icon(
                              Icons.arrow_back_rounded,
                              color: context.black,
                              size: 20.w,
                            )),
                      ),
                    ),
                  ),
                  body: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 12.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'privacy_and_security'.tr,
                              style: TextStyle(
                                fontSize: 22.sp,
                                fontWeight: FontWeight.w600,
                                color: context?.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 40.h),
                            SectionTitle(
                              title: 'security'.tr,
                            ),
                            SizedBox(height: 8.h),
                          ],
                        ),
                      ),
                      _biometric(context, state),
                      ProfileMenuItem(
                        enabled:
                            (state.isBioMetricEnabledForThisDevice ?? false)
                                .inverted,
                        icon: Assets.thrivvePhotosPasscode,
                        title: "change_pin".tr,
                        subtitle: state.isPinEnabledForThisDevice == true
                            ? "change_pin_description".tr
                            : "set_pin_description".tr,
                        onTap: () async {
                          final isPinEnabled =
                              await getIt<UserSecureDataSource>()
                                  .isPinEnabled();
                          if (isPinEnabled) {
                            await Get.toNamed(Routes.changePinScreen,
                                arguments: {"from": "changeCode"});
                          } else {
                            await Get.toNamed(Routes.changePinScreen);
                          }

                          getIt<MainHomeBloc>()
                              .add(CheckIsDeviceSupportAndTypeBioMetricEvent());
                        },
                      ),
                      SizedBox(height: 18.h),
                      ProfileMenuItem(
                        icon: Assets.assetsThrivvePhotosMobile,
                        title: "change_your_phone_number".tr,
                        subtitle:
                            formatPhoneNumber(state.personalInfo?.mobile ?? ''),
                        textAlign: state.language == valueArLanguage
                            ? TextAlign.end
                            : TextAlign.start,
                        textDirection: TextDirection.ltr,
                        onTap: () {
                          getIt<IAnalyticsLogger>()
                              .logEvent(AnalyticsActions.changePhoneNumber);
                          Get.toNamed(AppRoutes.updateMobileScreen)
                              ?.then((value) {
                            if (value != null && value == true) {
                              context
                                  .read<MainHomeBloc>()
                                  .add(GetPersonalInfoEvent());
                            }
                          });
                        },
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 40.h),
                              SectionTitle(
                                title: 'privacy'.tr,
                              ),
                              SizedBox(height: 8.h)
                            ]),
                      ),
                      ProfileMenuItem(
                        icon: Assets.thrivvePhotosPrivacy,
                        title: "privacy_msg".tr,
                        onTap: () {
                          getIt<IAnalyticsLogger>()
                              .logEvent(AnalyticsActions.viewPrivacyPolicy);
                          openTheUriInWebPage(
                              url: ApiSettings.privacyPolicyUrl,
                              context: context);
                        },
                      ),
                      SizedBox(height: 18.h),
                    ],
                  ),
                );
              },
            )));
  }

  bool get isDeviceSupport =>
      getIt<MainHomeBloc>().state.isDeviceSupportBioMetric ?? false;

  bool get isFaceId => getIt<MainHomeBloc>().state.isFaceId ?? false;

  Widget _biometric(BuildContext context, MainHomeState state) {
    if (isDeviceSupport.inverted) {
      return SizedBox.shrink();
    }

    return BlocBuilder<MainHomeBloc, MainHomeState>(
        buildWhen: (previous, current) =>
            ((previous.isBioMetricEnabledForThisDevice !=
                    current.isBioMetricEnabledForThisDevice) ||
                (state.isPinEnabledForThisDevice !=
                    current.isPinEnabledForThisDevice)),
        builder: (context, state) {
          return Visibility(
            visible: (state.isPinEnabledForThisDevice == true),
            child: ListTile(
              contentPadding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
              ),
              // minVerticalPadding: 0.0, // Custom vertical padding
              leading: Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: context.appBackgroundColor,
                  borderRadius: BorderRadius.circular(90.r),
                ),
                child: Padding(
                  padding: EdgeInsets.all(6.sp),
                  child: SvgPicture.asset(
                    width: 24.w,
                    height: 24.h,
                    isFaceId
                        ? Assets.thrivvePhotosFaceIdSvgIcon
                        : Assets.thrivvePhotosFingerImage,
                    colorFilter: ColorFilter.mode(
                      context.black,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              title: Text(
                isFaceId ? 'face_id'.tr : 'touch_id'.tr,
                textAlign: TextAlign.start,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w700,
                  color: context.black,
                ),
              ),
              subtitle: Text(
                isFaceId ? 'face_id_desc'.tr : 'touch_id_desc'.tr,
                textAlign: TextAlign.start,
                style: TextStyle(
                  fontSize: 11.sp,
                  fontWeight: FontWeight.w400,
                  color: context.lightBlack,
                ),
              ),
              trailing: CupertinoSwitch(
                value: state.isBioMetricEnabledForThisDevice ?? false,
                // Thumb color when active
                activeTrackColor: context
                    .appPrimaryColor, // Track color when active (dark blue)
                inactiveThumbColor: Colors.white, // Thumb color when inactive
                inactiveTrackColor: Colors.grey.shade300,
                onChanged: (value) =>
                    _onChangedSwitch(value, context, isFaceId),
              ),
            ),
          );
        });
  }

  void _onChangedSwitch(bool value, BuildContext context, bool isFaceId) {
    final biometricAuth = BiometricAuthUtility(
      userSecureDataSource: getIt<UserSecureDataSource>(),
      mainHomeBloc: context.read<MainHomeBloc>(),
    );

    if (value) {
      biometricAuth.handleBiometricAuth(isFaceId: isFaceId);
    } else {
      biometricAuth.handleDisableBiometric(isFaceId: isFaceId);
    }
  }
}
