import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/core/widget/container_general_bottom_sheet_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/payment/binding/payment_binding.dart';
import 'package:thrivve/features/payment/presentation/pages/saved_cards_page.dart';
import 'package:thrivve/features/settings/presentation/widgets/appearance_bottomsheet.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../generated/assets.dart';
import '../widgets/profile_menu_item.dart';
import '../widgets/section_title.dart';

class SettingScreen extends StatelessWidget {
  const SettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<MainHomeBloc>(),
      child: BlocBuilder<MainHomeBloc, MainHomeState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              leadingWidth: 66.w,
              leading: Center(
                child: Container(
                  height: 40.h,
                  width: 40.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: context.appBackgroundColor,
                  ),
                  child: IconButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      icon: Icon(
                        Icons.arrow_back_rounded,
                        color: context.black,
                        size: 20.w,
                      )),
                ),
              ),
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'settings'.tr,
                        style: TextStyle(
                          fontSize: 22.sp,
                          fontWeight: FontWeight.w600,
                          color: context.black,
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 40.h),
                      SectionTitle(
                        title: 'general_setting'.tr,
                      ),
                      SizedBox(height: 8.h),
                    ],
                  ),
                ),
                GetBuilder<ThemeController>(builder: (controller) {
                  return Obx(() {
                    return ProfileMenuItem(
                      colorizedIcon: false,
                      icon: controller.themeModeTitle.value.icon,
                      title: 'appearance'.tr,
                      subtitle: controller.themeModeTitle.value.title.tr,
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: context.containerColor,
                          builder: (builderContext) {
                            return ThemeBottomsheet();
                          },
                        );
                      },
                    );
                  });
                }),
                SizedBox(height: 18.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 40.h),
                      SectionTitle(
                        title: 'account_setting'.tr,
                      ),
                      SizedBox(height: 8.h),
                    ],
                  ),
                ),
                ProfileMenuItem(
                  icon: Assets.thrivvePhotosProfileBankAccounts,
                  title: "bank_accounts".tr,
                  subtitle: "manage_your_bank_accounts".tr,
                  onTap: () {
                    getIt<IAnalyticsLogger>()
                        .logEvent(AnalyticsActions.viewBankAccounts);
                    Get.toNamed(AppRoutes.bankAccountListPage);
                  },
                ),
                SizedBox(height: 18.h),
                ProfileMenuItem(
                  icon: Assets.thrivvePhotosCards,
                  title: "saved_cards".tr,
                  subtitle: "manage_saved_cards".tr,
                  onTap: () {
                    getIt<IAnalyticsLogger>()
                        .logEvent(AnalyticsActions.viewSavedCards);
                    PaymentBinding().dependencies();
                    Get.bottomSheet(SavedCardsPage());
                  },
                ),
                SizedBox(height: 18.h),
                ProfileMenuItem(
                  icon: Assets.thrivvePhotosProfileSettingDeleteAccount,
                  title: "delete_account".tr,
                  subtitle: "delete_account_subtitle".tr,
                  onTap: () {
                    getIt<IAnalyticsLogger>()
                        .logEvent(AnalyticsActions.deleteMyAccount);

                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: context.containerColor,
                      builder: (builderContext) {
                        return BlocProvider.value(
                          value: BlocProvider.of<MainHomeBloc>(context),
                          child: BlocBuilder<MainHomeBloc, MainHomeState>(
                              builder: (context, state) {
                            return ContainerGeneralBottomSheetWidget(
                              isLoading:
                                  state.deleteUserStatus == AppStatus.loading,
                              title: "are_you_sure".tr,
                              description: "delete_account_message".tr,
                              icon: Assets.thrivvePhotosDeleteBank,
                              buttonColor: context?.logoutBtnBackground,
                              firstBtnText: "delete_account_btn".tr,
                              secondBtnText: "cancel".tr,
                              firstBtnOnClick: () {
                                context
                                    .read<MainHomeBloc>()
                                    .add(const DeleteUserAccountEvent());
                              },
                              secondBtnOnClick: () {
                                Get.back();
                              },
                              imageBackgroundWidth: 60.w,
                              imageBackgroundHeight: 60.h,
                            );
                          }),
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
