part of 'update_mobile_bloc.dart';

@immutable
sealed class UpdateMobileEvent extends Equatable {}

class SendOtpEvent extends UpdateMobileEvent {
  final String? mobileNum;

  SendOtpEvent({
    required this.mobileNum,
  });

  @override
  List<Object?> get props => [
        mobileNum,
      ];
}

class EnterPinNumberEvent extends UpdateMobileEvent {
  EnterPinNumberEvent();

  @override
  List<Object?> get props => [];
}

class UpdateMobileReadyToSubmitEvent extends UpdateMobileEvent {
  final bool readyToSubmit;
  final String? mobileNum;

  UpdateMobileReadyToSubmitEvent({required this.readyToSubmit, this.mobileNum});

  @override
  List<Object?> get props => [readyToSubmit, mobileNum];
}

class ResentVerificationCodeEvent extends UpdateMobileEvent {
  final String mobileNum;

  ResentVerificationCodeEvent({required this.mobileNum});

  @override
  List<Object?> get props => [mobileNum];
}

class VerifyOtpEvent extends UpdateMobileEvent {
  final String mobileNum;
  final String otpNum;

  VerifyOtpEvent({required this.mobileNum, required this.otpNum});

  @override
  List<Object?> get props => [mobileNum, otpNum];
}

class GetCountriesEvent extends UpdateMobileEvent {
  GetCountriesEvent();

  @override
  List<Object?> get props => [];
}

class SelectedCountryEvent extends UpdateMobileEvent {
  final Country? country;

  SelectedCountryEvent({this.country});

  @override
  List<Object?> get props => [country];
}
