import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class ProfileMenuItem extends StatelessWidget {
  final String icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;
  final bool hasWarning;
  final bool isDone;
  final bool enabled;
  final bool colorizedIcon;
  final TextDirection? textDirection;
  final TextAlign textAlign;

  const ProfileMenuItem({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.enabled = true,
    required this.onTap,
    this.hasWarning = false,
    this.isDone = false,
    this.colorizedIcon = true,
    this.textDirection,
    this.textAlign = TextAlign.start,
  });

  @override
  Widget build(BuildContext context) {
    // Define disabled colors - using more muted/greyed out versions
    final Color disabledTextColor =
        Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade500 // Lighter grey for dark mode
            : Colors.grey; // Standard grey for light mode

    final Color disabledIconColor =
        Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade600 // Lighter grey for dark mode
            : Colors.grey.shade400;
    return Opacity(
      opacity: enabled ? 1.0 : 0.7,
      child: SizedBox(
        height: 52.h,
        child: ListTile(
          enabled: enabled,
          contentPadding: EdgeInsets.only(
            left: 16.w,
            right: 16.w,
          ),
          leading: Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: context.appBackgroundColor,
                borderRadius: BorderRadius.circular(90.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(8.0.w),
                child: Center(
                    child: icon.split(".").last.contains("svg")
                        ? SvgPicture.asset(
                            icon,
                            width: 24.w,
                            height: 24.h,
                            color: enabled
                                ? (colorizedIcon
                                    ? context.blackIconColor
                                    : null)
                                : disabledIconColor,
                          )
                        : Image.asset(
                            icon,
                            width: 24.w,
                            height: 24.h,
                            color: enabled
                                ? (colorizedIcon
                                    ? context.blackIconColor
                                    : null)
                                : disabledIconColor,
                          )),
              )),
          title: Text(
            title,
            style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: enabled ? context.black : disabledTextColor),
          ),
          subtitle: subtitle != null
              ? Text(subtitle ?? "",
                  textDirection: textDirection,
                  textAlign: textAlign,
                  style: TextStyle(
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w400,
                      color: enabled ? context.lightBlack : disabledTextColor))
              : null,
          trailing: isDone
              ? Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 9.h),
                  decoration: BoxDecoration(
                    color: enabled ? context.greenColor : Colors.grey,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    'done'.tr,
                    style: TextStyle(
                      height: 0.5,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (hasWarning && enabled) ...[
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.redAccent,
                        size: 12.w,
                      ),
                      SizedBox(width: 10.w),
                    ],
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: enabled ? context.black : disabledIconColor,
                      size: 16.w,
                    ),
                  ],
                ),
          onTap: enabled ? onTap : null,
        ),
      ),
    );
  }
}
