
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/theme/theme_widget_data.dart';
import 'package:thrivve/features/settings/presentation/widgets/theme_custom_radiobutton.dart';

class AppearanceWidget extends StatelessWidget {
  final ThemeWidgetData themeWidgetData;
  final bool isSelected;

  const AppearanceWidget(this.themeWidgetData, this.isSelected, {super.key});

  @override
  Widget build(BuildContext context) {
    final backgroundColor = context.appBackgroundColor;
    final titleColor = context.black;
    final subtitleColor = context.lightBlack;

    return ListTile(
      onTap: () {
        Get.find<ThemeController>().changeThemeing(themeWidgetData.themeMode);
      },
      leading: Container(
        width: 40.w,
        height: 40.h,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(90.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(8.0.w),
          child: Center(
            child: SvgPicture.asset(
              themeWidgetData.icon,
              width: 24.w,
              height: 24.h,
            ),
          ),
        ),
      ),
      title: Text(
        themeWidgetData.title.tr,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w700,
          color: titleColor,
        ),
      ),
      subtitle: Text(
        themeWidgetData.subtitle.tr,
        style: TextStyle(
          fontSize: 11.sp,
          fontWeight: FontWeight.w400,
          color: subtitleColor,
        ),
      ),
      trailing: CustomRadioButton(isSelected),
    );
  }
}
