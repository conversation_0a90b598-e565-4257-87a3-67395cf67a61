import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class LanguageSelector extends StatelessWidget {
  final String selectedLanguage;

  const LanguageSelector({super.key, required this.selectedLanguage});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Language',
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold)),
        Row(
          children: [
            _languageButton(context, 'العربية', selectedLanguage == 'العربية'),
            SizedBox(width: 10.w),
            _languageButton(context, 'English', selectedLanguage == 'English'),
          ],
        ),
      ],
    );
  }

  Widget _languageButton(
      BuildContext context, String language, bool isSelected) {
    return GestureDetector(
      onTap: () {
        // BlocProvider.of<ProfileBloc>(context).add(ChangeLanguage(language));
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: isSelected ? Colors.purple : Colors.transparent,
          border: Border.all(color: Colors.purple),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(language,
            style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? context.whiteColor : Colors.purple)),
      ),
    );
  }
}
