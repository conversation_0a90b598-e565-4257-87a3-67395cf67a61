import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../core/util/const.dart';

class ProfileMenuLanguageItem extends StatelessWidget {
  final String icon;
  final String title;
  final Function(String? lang) onTap;
  final String? currentAppLanguage;

  const ProfileMenuLanguageItem({
    super.key,
    required this.icon,
    required this.title,
    this.currentAppLanguage,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 52.h,
      child: ListTile(
        contentPadding: EdgeInsets.only(
          left: 16.w,
          right: 16.w,
        ),
        // minVerticalPadding: 0.0, // Custom vertical padding
        leading: Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: context.appBackgroundColor,
              borderRadius: BorderRadius.circular(90.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(8.0.w),
              child: Center(
                  child: SvgPicture.asset(icon,
                      width: 24.w,
                      height: 24.h,
                      color: context.blackIconColor)),
            )),
        title: Text(
          title,
          style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w700,
              color: context.black),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                onTap(valueArLanguage);
              },
              child: Container(
                width: 74.w,
                height: 30.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border:
                      Border.all(width: 0.5.w, color: context.appPrimaryColor!),
                  color: currentAppLanguage == valueArLanguage
                      ? context.appPrimaryColor
                      : context.containerColor,
                  borderRadius: BorderRadius.circular(20.0.r),
                ),
                child: Text(
                  "العربية",
                  style: TextStyle(
                      color: currentAppLanguage == valueArLanguage
                          ? Colors.white
                          : context.black,
                      fontWeight: FontWeight.w400,
                      fontFamily: "NotoSansArabic",
                      fontSize: 12.sp),
                ),
              ),
            ),
            SizedBox(
              width: 8.w,
            ),
            InkWell(
              onTap: () {
                onTap(valueEnLanguage);
              },
              child: Container(
                width: 74.w,
                height: 30.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border:
                      Border.all(width: 0.5.w, color: context.appPrimaryColor!),
                  color: currentAppLanguage == valueEnLanguage
                      ? context.appPrimaryColor
                      : context.containerColor,
                  borderRadius: BorderRadius.circular(20.0.r),
                ),
                child: Text(
                  "English",
                  style: TextStyle(
                    color: currentAppLanguage == valueEnLanguage
                        ? Colors.white
                        : context.black,
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
        onTap: () {
          onTap(currentAppLanguage == valueEnLanguage
              ? valueArLanguage
              : valueEnLanguage);
        },
      ),
    );
  }
}
