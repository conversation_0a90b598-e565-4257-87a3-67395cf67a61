import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../core/util/const.dart';

class LanguageRow extends StatelessWidget {
  final String? icon;
  final String? title;
  final String? currentAppLanguage;
  final Function(String? lang) onTab;

  const LanguageRow({
    super.key,
    required this.icon,
    required this.title,
    required this.onTab,
    required this.currentAppLanguage,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: context.appBackgroundColor,
                  borderRadius: BorderRadius.circular(90.r),
                ),
                child: Padding(
                  padding: EdgeInsets.all(8.0.w),
                  child: Center(
                      child: Image.asset(
                    icon ?? "",
                    width: 24.w,
                    height: 24.h,
                  )),
                )),
            SizedBox(
              width: 8.w,
            ),
            Center(
              child: Text(
                title ?? "",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: context.textColor,
                ),
              ),
            )
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                onTab(valueEnLanguage);
              },
              child: Container(
                width: 58.w,
                height: 25.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border:
                      Border.all(width: 0.5.w, color: context.appPrimaryColor!),
                  color: currentAppLanguage == valueEnLanguage
                      ? context.appPrimaryColor
                      : context.containerColor,
                  borderRadius: BorderRadius.circular(16.0.r),
                ),
                child: Text(
                  "English",
                  style: TextStyle(
                    color: currentAppLanguage == valueEnLanguage
                        ? context.whiteColor
                        : context.black,
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 8.w,
            ),
            InkWell(
              onTap: () {
                onTab(valueArLanguage);
              },
              child: Container(
                width: 58.w,
                height: 25.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border:
                      Border.all(width: 0.5.w, color: context.appPrimaryColor!),
                  color: currentAppLanguage == valueArLanguage
                      ? context.appPrimaryColor
                      : context.containerColor,
                  borderRadius: BorderRadius.circular(16.0.r),
                ),
                child: Text(
                  "العربية",
                  style: TextStyle(
                      color: currentAppLanguage == valueArLanguage
                          ? context?.whiteColor
                          : context.black,
                      fontWeight: FontWeight.w500,
                      fontSize: 10.sp),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
