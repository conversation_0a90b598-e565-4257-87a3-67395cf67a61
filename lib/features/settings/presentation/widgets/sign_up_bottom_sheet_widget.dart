import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/settings/presentation/widgets/mobile_verification_widget.dart';

class SignUpBottomSheetWidget extends StatelessWidget {
  final String mobileNum;
  final String otpCode;
  final Function(String?, String?) onVerificationSubmit;
  final Function() onResendOTP;
  final Function() onBackClick;
  final Function() needHelpOnClick;

  const SignUpBottomSheetWidget({
    super.key,
    required this.mobileNum,
    required this.otpCode,
    required this.onVerificationSubmit,
    required this.onResendOTP,
    required this.onBackClick,
    required this.needHelpOnClick,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context)
              .bottom, // Adjust for the keyboard
        ),
        child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0.r),
              topRight: Radius.circular(20.0.r),
            ),
          ),
          child: MobileVerificationWidget(
            otpCode: otpCode,
            mobileNumber: mobileNum,
            onVerificationSubmit: onVerificationSubmit,
            onBackClick: onBackClick,
            resendOTP: onResendOTP,
            needHelpOnClick: needHelpOnClick,
          ),
        ),
      ),
    );
  }
}
