import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/biometric_settings.dart';
import '../entities/biometric_settings_input.dart';
import '../repositories/profile_repository.dart';

class UpdateBiometricSettingsUseCase {
  final ProfileRepository repository;

  UpdateBiometricSettingsUseCase(this.repository);

  Future<Either<Failure, BiometricSettings?>> call(BiometricSettingsInput input) {
    return repository.updateBiometricSettings(input: input);
  }
} 