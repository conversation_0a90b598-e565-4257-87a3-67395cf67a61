import 'dart:async';
import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/static_var.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/localDataSource/user_secure_data_source.dart';
import '../../../../core/util/const.dart';
import '../../domain/use_cases/check_ token_expiration_use_case.dart';

part 'splash_event.dart';
part 'splash_state.dart';

class SplashBloc extends Bloc<SplashEvent, SplashState> {
  final UserSecureDataSource userSecureDataSource;
  final CheckTokenExpirationUseCase checkTokenExpirationUseCase;

  SplashBloc({
    required this.userSecureDataSource,
    required this.checkTokenExpirationUseCase,
  }) : super(const SplashState()) {
    on<InitSplashEvent>(_initSplashEventOnCall);
    on<NavigateToNextScreen>(_navigateToNextScreenOnCall);
  }

  Future<void> _navigateToNextScreenOnCall(
    NavigateToNextScreen event,
    Emitter<SplashState> emit,
  ) async {
    final isUserLoggedIn = await userSecureDataSource.isLogin();
    var isForceUpdate = await versionCheck();

    if (isForceUpdate) {
      emit(state.copyWith(isForceUpdateAvailable: () => true));
    } else {
      if (isUserLoggedIn == true) {
        final isPinEnabled = await getIt<UserSecureDataSource>().isPinEnabled();
        final isFaceIdEnabled =
            await getIt<UserSecureDataSource>().isBioMetricEnabled();

        if (isFaceIdEnabled || isPinEnabled) {
          Get.offAllNamed(AppRoutes.pinScreen);
        } else {
          reinitializeBloc();
          Get.offAllNamed(AppRoutes.homePage);
        }
      } else {
        reinitializeBloc();
        Get.offAllNamed(AppRoutes.homePage);
      }
    }
  }

  Future<void> _initSplashEventOnCall(
    InitSplashEvent event,
    Emitter<SplashState> emit,
  ) async {
    await Future.delayed(Duration(milliseconds: 300));
    final isUserLoggedIn = await userSecureDataSource.isLogin();
    getIt<MainHomeBloc>()
      ..add(CheckLogin())
      ..add(GetFlagsEvent());

    var isFirstTimeInstalled =
        await userSecureDataSource.isFirstTimeInstallApp();
    print("isFirstTimeInstalled $isFirstTimeInstalled");
    emit(state.copyWith(isFirstTimeInstalled: () => isFirstTimeInstalled));
    try {
      userSecureDataSource.setFirstTimeInstallApp();
    } catch (e) {
      print("Could not set first time install flag: $e");
      // Continue execution even if this fails
    }
    var newCurrentSavedLanguage = await userSecureDataSource.getLanguage();
    emit(state.copyWith(language: () => newCurrentSavedLanguage ?? ""));
    StaticVar.isUserLoggedIn = isUserLoggedIn == true;
    if (isUserLoggedIn == true) {
      final result = await checkTokenExpirationUseCase(NoParams());
      result?.fold((error) {
        log('error $error');
      }, (data) {
        final mobile = data?.mobile;
        if (mobile != null) {
          userSecureDataSource.setMobileNumber(mobile);
        }
      });
    }
  }

  Future<bool> versionCheck() async {
    final PackageInfo info = await PackageInfo.fromPlatform();
    int currentVersion = int.parse(info.buildNumber);
    try {
      final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
      await remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 60),
        minimumFetchInterval: const Duration(seconds: 1),
      ));
      await remoteConfig.fetchAndActivate();
      int newVersion = remoteConfig.getInt(forceUpdateKey);
      if (kDebugMode) {
        print(
            "newVersion $newVersion currentVersion $currentVersion ${newVersion > currentVersion}");
      }
      return newVersion > currentVersion;
    } catch (exception) {
      if (kDebugMode) {
        print("exception ${exception.toString()}");
      }
      return false;
    }
  }
}
