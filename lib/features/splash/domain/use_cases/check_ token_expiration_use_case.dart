import 'package:dartz/dartz.dart';
import 'package:thrivve/features/onboarding/domain/entities/check_token_response_entity.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../onboarding/domain/repositories/auth_repository.dart';

class CheckTokenExpirationUseCase implements UseCase<dynamic, NoParams> {
  AuthRepository? authRepository;

  CheckTokenExpirationUseCase({this.authRepository});

  @override
  Future<Either<Failure, CheckTokenResponseEntity?>?> call(
      NoParams params) async {
    return await authRepository?.checkTokenStatus();
  }
}
