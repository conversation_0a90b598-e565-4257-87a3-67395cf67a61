part of 'my_products_bloc.dart';

enum MyProductsStatuses { initial, loading, success, failure }

class MyProductsState extends Equatable {
  const MyProductsState({
    this.getListOfProductsStatuses = MyProductsStatuses.initial,
    this.errorMessage,
    this.language,
    this.currency,
    this.listOfActiveProducts,
    this.listOfInActiveProducts,
    this.hasActiveProducts,
    this.hasInActiveProducts,
  });

  final List<MyProduct>? listOfActiveProducts;
  final List<MyProduct>? listOfInActiveProducts;
  final bool? hasInActiveProducts;
  final bool? hasActiveProducts;
  final MyProductsStatuses getListOfProductsStatuses;
  final String? errorMessage;
  final String? language;
  final String? currency;

  MyProductsState copyWith(
      {MyProductsStatuses Function()? getListOfProductsStatuses,
      List<MyProduct>? Function()? listOfActiveProducts,
      List<MyProduct>? Function()? listOfInActiveProducts,
      String? Function()? errorMessage,
      String? Function()? language,
      bool? Function()? hasInActiveProducts,
      bool? Function()? hasActiveProducts,
      String? Function()? currency}) {
    return MyProductsState(
      getListOfProductsStatuses: getListOfProductsStatuses != null
          ? getListOfProductsStatuses()
          : this.getListOfProductsStatuses,
      hasInActiveProducts: hasInActiveProducts != null
          ? hasInActiveProducts()
          : this.hasInActiveProducts,
      hasActiveProducts: hasActiveProducts != null
          ? hasActiveProducts()
          : this.hasActiveProducts,
      listOfInActiveProducts: listOfInActiveProducts != null
          ? listOfInActiveProducts()
          : this.listOfInActiveProducts,
      language: language != null ? language() : this.language,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
      currency: currency != null ? currency() : this.currency,
      listOfActiveProducts: listOfActiveProducts != null
          ? listOfActiveProducts()
          : this.listOfActiveProducts,
    );
  }

  @override
  List<Object?> get props => [
        currency,
        language,
        listOfActiveProducts,
        errorMessage,
        getListOfProductsStatuses,
        listOfInActiveProducts,
        hasInActiveProducts
      ];
}
