import 'package:equatable/equatable.dart';

class Subscription extends Equatable {
  const Subscription({
    required this.id,
    required this.title,
    required this.startDate,
    required this.value,
    required this.documentUrl,
    required this.icon,
  });

  final int id;
  final String title;
  final String startDate;
  final String value;
  final String documentUrl;
  final String icon;

  @override
  List<Object?> get props => [
        id,
        title,
        startDate,
        value,
        documentUrl,
        icon,
      ];
}
