import 'package:flutter/material.dart';

class StoryProgressBar extends StatelessWidget {
  final int currentIndex;
  final int totalStories;
  final double height;

  const StoryProgressBar({
    super.key,
    required this.currentIndex,
    required this.totalStories,
    this.height = 3.0,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(
        totalStories,
        (index) => Expanded(
          child: Container(
            height: height,
            margin: EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              color: index <= currentIndex
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(height),
            ),
          ),
        ),
      ),
    );
  }
}
