import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/features/stories_bar/presentation/manager/stories_controller.dart';

import 'story_circle.dart';

class StoriesBar extends StatelessWidget {
  final StoriesController controller = Get.find<StoriesController>();

  StoriesBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SizedBox(
        height: 100.h,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: controller.allUserStories.length,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          itemBuilder: (context, index) {
            final userStories = controller.allUserStories[index];
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: StoryCircle(
                userStories: userStories,
                onTap: () => controller.openStory(index),
              ),
            );
          },
        ),
      );
    });
  }
}
