// File: lib/screens/story_viewer.dart
import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/features/stories_bar/data/models/story_model.dart';
import 'package:thrivve/features/stories_bar/presentation/manager/stories_controller.dart';
import 'package:video_player/video_player.dart';

class StoryViewer extends StatefulWidget {
  const StoryViewer({Key? key}) : super(key: key);

  @override
  State<StoryViewer> createState() => _StoryViewerState();
}

class _StoryViewerState extends State<StoryViewer>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  final StoriesController controller = Get.find<StoriesController>();
  Timer? _timer;
  final int _defaultImageDuration = 5; // seconds for image stories
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  bool _isPaused = false;
  bool _isMuted = false;
  DateTime? _pauseTime;
  Duration _remainingTime = Duration.zero;

  // Animation controller for smoother transitions
  late AnimationController _animationController;

  // For double tap reactions
  Timer? _doubleTapTimer;
  int _doubleTapCount = 0;
  Offset? _lastTapPosition;

  // Flag to block tap-through events
  bool _isHandlingGesture = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Enter immersive mode for better viewing experience
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // Lock to portrait orientation for stories
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Load with a slight delay to allow for smooth transitions
    Future.delayed(const Duration(milliseconds: 100), () {
      _loadStoryContent();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // Reset orientation
    SystemChrome.setPreferredOrientations([]);

    // Cleanup all resources
    _cleanupResources();
    _doubleTapTimer?.cancel();
    _animationController.dispose();

    controller.closeStory();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Pause story when app goes to background
    if (state == AppLifecycleState.paused) {
      _pauseStory();
    } else if (state == AppLifecycleState.resumed) {
      _resumeStory();
    }
  }

  void _cleanupResources() {
    _timer?.cancel();
    _videoController?.removeListener(_onVideoProgress);
    _videoController?.dispose();
    _videoController = null;
  }

  Future<void> _loadStoryContent() async {
    if (controller.currentUserStories == null) return;

    // Cleanup previous resources
    _cleanupResources();

    final currentStory = _getCurrentStory();
    if (currentStory == null) return;

    if (currentStory.contentType == StoryContentType.video) {
      await _initializeVideoController(currentStory.contentUrl);
    } else {
      // Prefetch next image if possible
      _prefetchNextImage();
      _startImageTimer();
    }
  }

  StoryModel? _getCurrentStory() {
    if (controller.currentUserStories == null ||
        controller.currentStoryIndex >=
            (controller.currentUserStories?.stories.length ?? 0)) {
      return null;
    }
    return controller.currentUserStories?.stories[controller.currentStoryIndex];
  }

  void _prefetchNextImage() {
    if (controller.currentUserStories == null) return;

    // Prefetch next image in the current user's stories
    final nextIndex = controller.currentStoryIndex + 1;
    if (nextIndex < (controller.currentUserStories?.stories.length ?? 0)) {
      final nextStory = controller.currentUserStories?.stories[nextIndex];
      if (nextStory?.contentType == StoryContentType.image) {
        precacheImage(
            CachedNetworkImageProvider((nextStory?.contentUrl ?? '')), context);
      }
    }

    // Also try to prefetch first image of next user's stories if available
    final nextUserIndex = controller.currentUserIndex + 1;
    if (controller.allUserStories.length > nextUserIndex) {
      final nextUserStories = controller.allUserStories[nextUserIndex];
      if (nextUserStories.stories.isNotEmpty &&
          nextUserStories.stories[0].contentType == StoryContentType.image) {
        precacheImage(
            CachedNetworkImageProvider(nextUserStories.stories[0].contentUrl),
            context);
      }
    }
  }

  Future<void> _initializeVideoController(String videoUrl) async {
    _isVideoInitialized = false;
    setState(() {});

    try {
      // Use VideoPlayerValue.networkUrl constructor for better caching
      final httpHeaders = <String, String>{
        'Cache-Control': 'max-age=86400', // Cache for 24 hours
      };

      _videoController = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl),
        httpHeaders: httpHeaders,
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: false),
      );

      // Set playback speed for smoother video
      _videoController?.setPlaybackSpeed(1.0);

      await _videoController?.initialize();
      _videoController?.addListener(_onVideoProgress);

      // Apply mute setting
      _videoController?.setVolume(_isMuted ? 0.0 : 1.0);

      // Set video to loop if it's short
      if ((_videoController?.value.duration.inSeconds ?? 0) < 15) {
        _videoController?.setLooping(true);
      }

      _videoController?.play();

      if (mounted) {
        setState(() {
          _isVideoInitialized = true;
        });
      }
    } catch (e) {
      print('Error initializing video: $e');
      // Fallback to image timer if video fails
      _startImageTimer();
    }
  }

  void _onVideoProgress() {
    if (_videoController == null || !_isVideoInitialized || !mounted) return;

    // Check if video reached the end
    if ((_videoController?.value.position ?? Duration.zero) >=
        (_videoController?.value.duration ?? Duration.zero)) {
      _moveToNextStory();
      return;
    }

    // Update state for smooth progress bar
    setState(() {});
  }

  void _startImageTimer() {
    _timer?.cancel();

    // Set the initial start time
    final startTime = DateTime.now();
    final totalDuration = Duration(seconds: _defaultImageDuration);

    // Reset remaining time for new image
    _remainingTime = totalDuration;

    setState(() {}); // Update UI to show progress from the beginning

    // Create a periodic timer with very small increments for smooth animation
    _timer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (mounted && !_isPaused) {
        final elapsedTime = DateTime.now().difference(startTime);

        // Calculate remaining time
        if (elapsedTime < totalDuration) {
          setState(() {
            _remainingTime = totalDuration - elapsedTime;
          });
        } else {
          timer.cancel();
          _moveToNextStory();
        }
      }
    });
  }

  void _pauseStory() {
    if (_isPaused) return;

    _isPaused = true;
    _pauseTime = DateTime.now();

    if (_videoController != null && _isVideoInitialized) {
      _videoController?.pause();
    } else {
      _timer?.cancel(); // Pause the timer

      // No need to calculate remaining time as we're now tracking it continuously
    }

    if (mounted) setState(() {});
  }

  void _resumeStory() {
    if (!_isPaused) return;

    _isPaused = false;

    if (_videoController != null && _isVideoInitialized) {
      _videoController?.play();
    } else {
      // For image stories, restart timer with remaining time
      final resumeTime = DateTime.now();
      final remainingDuration = _remainingTime;

      _timer?.cancel();
      _timer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
        if (mounted && !_isPaused) {
          final elapsedSinceResume = DateTime.now().difference(resumeTime);

          // Calculate new remaining time
          if (elapsedSinceResume < remainingDuration) {
            setState(() {
              _remainingTime = remainingDuration - elapsedSinceResume;
            });
          } else {
            timer.cancel();
            _moveToNextStory();
          }
        }
      });
    }

    if (mounted) setState(() {});
  }

  void _moveToNextStory() {
    controller.nextStory();

    if (controller.isViewingStory) {
      _loadStoryContent();
    } else {
      Get.back(); // Return to previous screen if no more stories
    }
  }

  void _moveToPreviousStory() {
    controller.previousStory();

    if (controller.isViewingStory) {
      _loadStoryContent();
    } else {
      Get.back(); // Return to previous screen if no more stories
    }
  }

  void _onTapDown(TapDownDetails details) {
    // First check if tap is within the header area where the mute and close buttons are
    final topHeaderHeight =
        MediaQuery.of(context).padding.top + 100; // Approximate header height

    // If tap is in the header area, ignore it for navigation purposes
    if (details.globalPosition.dy < topHeaderHeight) {
      return; // Don't process navigation for taps in the header area
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final tapPosition = details.globalPosition.dx;
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    // Handle double-tap to like (Instagram style) in the center region
    if (tapPosition > screenWidth / 3 && tapPosition < (screenWidth * 2 / 3)) {
      _handleDoubleTap(details.globalPosition);
      return;
    }

    // Determine which side was tapped, considering RTL support
    final isTapOnStart = tapPosition < screenWidth / 3;
    final isTapOnEnd = tapPosition > (screenWidth * 2 / 3);

    // For RTL (Arabic), we need to reverse the logic
    if (isRTL) {
      if (isTapOnStart) {
        // In RTL, start (right) side - go to next story
        _moveToNextStory();
      } else if (isTapOnEnd) {
        // In RTL, end (left) side - go to previous story
        _moveToPreviousStory();
      }
    } else {
      // For LTR (English)
      if (isTapOnStart) {
        // In LTR, start (left) side - go to previous story
        _moveToPreviousStory();
      } else if (isTapOnEnd) {
        // In LTR, end (right) side - go to next story
        _moveToNextStory();
      }
    }
  }

  void _handleDoubleTap(Offset position) {
    if (_doubleTapCount == 0) {
      _doubleTapCount = 1;
      _lastTapPosition = position;
      _doubleTapTimer = Timer(const Duration(milliseconds: 300), () {
        // Single tap in the middle - toggle pause
        if (_isPaused) {
          _resumeStory();
        } else {
          _pauseStory();
        }
        _doubleTapCount = 0;
        _lastTapPosition = null;
      });
    } else {
      // This is a double tap
      _doubleTapTimer?.cancel();
      _doubleTapCount = 0;
      _showLikeAnimation(position);
    }
  }

  void _showLikeAnimation(Offset position) {
    // Here you would trigger a heart animation at the tap position
    // This is just a placeholder for the like animation functionality
    print('Double tapped to like at position $position');
  }

  void _onLongPressStart(LongPressStartDetails details) {
    _pauseStory();
  }

  void _onLongPressEnd(LongPressEndDetails details) {
    _resumeStory();
  }

  Widget _buildProgressBar() {
    if (controller.currentUserStories == null) return const SizedBox.shrink();

    final currentUserStories = controller.currentUserStories!;
    final totalStories = currentUserStories.stories.length;
    final currentIndex = controller.currentStoryIndex;
    final currentStory = _getCurrentStory();

    if (currentStory == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Row(
        children: List.generate(
          totalStories,
          (index) {
            // Progress for the current story
            double progress = 0.0;
            if (index < currentIndex) {
              // Past stories are fully completed
              progress = 1.0;
            } else if (index == currentIndex) {
              if (currentStory.contentType == StoryContentType.video &&
                  _isVideoInitialized &&
                  _videoController != null) {
                // Video progress
                final duration =
                    _videoController?.value.duration.inMilliseconds;
                if ((duration ?? 0) > 0) {
                  progress =
                      (_videoController?.value.position.inMilliseconds ?? 0) /
                          (duration ?? 0);
                }
                // Don't update progress when paused
                if (_isPaused) {
                  progress = progress.clamp(
                      0.0, 0.999); // Never show as complete while paused
                }
              } else {
                // For images, calculate based on remaining time
                final totalMilliseconds = _defaultImageDuration * 1000;
                final elapsed =
                    totalMilliseconds - _remainingTime.inMilliseconds;
                progress = (elapsed / totalMilliseconds)
                    .clamp(0.0, _isPaused ? 0.999 : 1.0);
              }
            }

            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(2.5),
                  child: Stack(
                    children: [
                      // Background (unfilled part)
                      Container(
                        height: 2.5,
                        width: double.infinity,
                        color: Colors.white.withValues(alpha: 0.35),
                      ),

                      // Filled part
                      LayoutBuilder(
                        builder: (context, constraints) {
                          final maxWidth = constraints.maxWidth;
                          final width =
                              (progress * maxWidth).clamp(0.0, maxWidth);

                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 100),
                            curve: Curves.linear,
                            height: 2.5,
                            width: width,
                            color: Colors.white,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _toggleMute() {
    if (_videoController == null || !_isVideoInitialized) return;

    setState(() {
      _isMuted = !_isMuted;
      _videoController?.setVolume(_isMuted ? 0.0 : 1.0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (!didPop) {
          controller.closeStory();
          Navigator.of(context).pop();
        }
      },
      child: Obx(() {
        if (!controller.isViewingStory ||
            controller.currentUserStories == null) {
          // We're in an invalid state, go back
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.back();
          });
          return const Scaffold(backgroundColor: Colors.black);
        }

        final currentUserStories = controller.currentUserStories!;
        final currentStory = _getCurrentStory();

        if (currentStory == null) {
          // Invalid state, go back
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.back();
          });
          return const Scaffold(backgroundColor: Colors.black);
        }

        return Scaffold(
          extendBodyBehindAppBar: true,
          extendBody: true,
          backgroundColor: Colors.black,
          body: GestureDetector(
            onTapDown: _onTapDown,
            onLongPressStart: _onLongPressStart,
            onLongPressEnd: _onLongPressEnd,
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Story content (image or video)
                _buildStoryContent(currentStory),

                // Left and right tap indicators (only visible when debugging)
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                  ],
                ),

                // Pause indicator
                if (_isPaused)
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.black38,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.pause,
                        color: Colors.white,
                        size: 50,
                      ),
                    ),
                  ),

                // Top bar with progress indicators and user info
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.7),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: SafeArea(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding:
                                const EdgeInsets.only(top: 8.0, bottom: 4.0),
                            child: _buildProgressBar(),
                          ),

                          // User info row
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                              vertical: 8.0,
                            ),
                            child: Row(
                              children: [
                                Hero(
                                  tag: 'avatar-${currentUserStories.userId}',
                                  child: CircleAvatar(
                                    radius: 20,
                                    backgroundImage: CachedNetworkImageProvider(
                                      currentUserStories.userAvatar,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        currentUserStories.userName,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        _getTimeAgo(currentStory.createdAt),
                                        style: const TextStyle(
                                          color: Colors.white70,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Control buttons
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Video volume controls
                                    if (currentStory.contentType ==
                                            StoryContentType.video &&
                                        _isVideoInitialized)
                                      Padding(
                                        padding: EdgeInsetsDirectional.only(
                                          end: 8.w,
                                        ),
                                        child: Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap: () {
                                              _toggleMute();
                                            },
                                            // Handle event properly
                                            splashColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                            borderRadius:
                                                BorderRadius.circular(20),
                                            child: Container(
                                              margin: const EdgeInsets.only(
                                                  right: 8),
                                              padding: const EdgeInsets.all(8),
                                              width: 40,
                                              height: 40,
                                              decoration: const BoxDecoration(
                                                color: Colors.black26,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                _isMuted
                                                    ? Icons.volume_off
                                                    : Icons.volume_up,
                                                color: Colors.white,
                                                size: 20,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),

                                    // Close button
                                    Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: () {
                                          controller.closeStory();
                                          Get.back();
                                        },
                                        splashColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                        borderRadius: BorderRadius.circular(20),
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          decoration: const BoxDecoration(
                                            color: Colors.black26,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(Icons.close,
                                              color: Colors.white, size: 20),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildStoryContent(StoryModel story) {
    if (story.contentType == StoryContentType.video &&
        _isVideoInitialized &&
        _videoController != null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Center(
          child: AspectRatio(
            aspectRatio: MediaQuery.of(context).size.width /
                MediaQuery.of(context).size.height,
            child: VideoPlayer(_videoController!),
          ),
        ),
      );
    } else {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: CachedNetworkImage(
          imageUrl: story.contentUrl,
          fit: BoxFit.cover,
          memCacheWidth: MediaQuery.of(context).size.width.toInt(),
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(
              color: Colors.white70,
            ),
          ),
          errorWidget: (context, url, error) => const Center(
            child: CircularProgressIndicator(
              color: Colors.white70,
            ),
          ),
        ),
      );
    }
  }
}
