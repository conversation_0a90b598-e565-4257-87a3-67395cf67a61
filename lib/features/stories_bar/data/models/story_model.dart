// File: lib/models/story_model.dart
enum StoryContentType { image, video }

class StoryModel {
  final String id;
  final String contentUrl; // Can be image or video URL
  final StoryContentType contentType;
  final String title;
  final DateTime createdAt;
  bool isViewed;
  final Duration? videoDuration; // Only used for video content

  StoryModel({
    required this.id,
    required this.contentUrl,
    this.contentType = StoryContentType.image,
    required this.title,
    required this.createdAt,
    this.isViewed = false,
    this.videoDuration,
  });
}

class UserStories {
  final String userId;
  final String userName;
  final String userAvatar;
  final List<StoryModel> stories;

  UserStories({
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.stories,
  });

  bool get hasUnviewedStories => stories.any((story) => !story.isViewed);
}
