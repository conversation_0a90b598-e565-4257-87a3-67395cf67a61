part of 'transaction_details_bloc.dart';

@immutable
sealed class TransactionDetailsEvent {}

class GetTransactionDetailsEvent extends TransactionDetailsEvent {
  final String? transactionId;

  GetTransactionDetailsEvent(this.transactionId);
}

//CancelTransactionEvent
class CancelTransactionEvent extends TransactionDetailsEvent {
  final String? transactionId;

  CancelTransactionEvent(this.transactionId);
}
