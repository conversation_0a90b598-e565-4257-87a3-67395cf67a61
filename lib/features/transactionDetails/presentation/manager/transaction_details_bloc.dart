import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:get/get.dart';
import 'package:meta/meta.dart';

import '../../../../core/util/app_status.dart';
import '../../../../core/util/general_helper.dart';
import '../../../withdraw/domain/entities/withdraw_confirmation.dart';
import '../../domain/use_cases/cancel_transaction_use_case.dart';
import '../../domain/use_cases/get_transaction_details_use_case.dart';

part 'transaction_details_event.dart';

part 'transaction_details_state.dart';

class TransactionDetailsBloc
    extends Bloc<TransactionDetailsEvent, TransactionDetailsState> {
  final GetTransactionDetailsUseCase getTransactionDetailsUseCase;
  final CancelTransactionUseCase cancelTransactionUseCase;

  TransactionDetailsBloc({
    required this.getTransactionDetailsUseCase,
    required this.cancelTransactionUseCase,
  }) : super(TransactionDetailsState()) {
    on<GetTransactionDetailsEvent>(_onGetTransactionDetailsEvent);
    on<CancelTransactionEvent>(_onCancelTransactionEvent);
  }

  Future<void> _onCancelTransactionEvent(
    CancelTransactionEvent event,
    Emitter<TransactionDetailsState> emit,
  ) async {
    emit(state.copyWith(deleteStatus: () => AppStatus.loading));
    if (event.transactionId == null) {
      emit(state.copyWith(
        deleteStatus: () => AppStatus.failure,
        errorMessage: () => 'transaction_id_is_required'.tr,
      ));
      return;
    }

    var cancelTransactionUseCaseData = await cancelTransactionUseCase(
        CancelTransactionParams(transactionId: event.transactionId!));
    cancelTransactionUseCaseData.fold(
      (failure) {
        emit(state.copyWith(
          deleteStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (message) {
        emit(
          state.copyWith(
            deleteStatus: () => AppStatus.success,
            deleteMessage: () => message,
          ),
        );
      },
    );
  }

  Future<void> _onGetTransactionDetailsEvent(
    GetTransactionDetailsEvent event,
    Emitter<TransactionDetailsState> emit,
  ) async {
    if (event.transactionId == null) {
      emit(state.copyWith(
        status: () => AppStatus.failure,
        errorMessage: () => 'transaction_id_is_required'.tr,
      ));
      return;
    }

    emit(state.copyWith(
      status: () => AppStatus.loading,
      isTopUpTransaction: () => event.transactionId!.contains("THTP"),
    ));
    var getTransactionDetailsUseCaseData = await getTransactionDetailsUseCase(
        TransactionDetailsParams(transactionId: event.transactionId!));
    getTransactionDetailsUseCaseData.fold(
      (failure) {
        emit(state.copyWith(
          status: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (withdrawConfirmation) {
        emit(
          state.copyWith(
            status: () => AppStatus.success,
            withdrawConfirmation: () => withdrawConfirmation,
          ),
        );
      },
    );
  }
}
