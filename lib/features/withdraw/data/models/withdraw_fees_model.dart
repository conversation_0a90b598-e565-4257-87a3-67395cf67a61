import '../../domain/entities/withdraw_fees.dart';

class WithdrawFeesModel extends WithdrawFees {
  @override
  final double? fees;
  @override
  final double? minimumAmount;
  @override
  final String? message;

  WithdrawFeesModel({
    required this.fees,
    required this.minimumAmount,
    required this.message,
  }) : super(
    fees: fees ?? 0.0,
    minimumAmount: minimumAmount ?? 0.0,
    message: message ?? '',
  );

  factory WithdrawFeesModel.fromJson(Map<String, dynamic> json) {
    return WithdrawFeesModel(
      fees: json['fees']?.toDouble(),
      minimumAmount: json['minimum_amount']?.toDouble(),
      message: json['message'],
    );
  }
}
