import 'package:dartz/dartz.dart';
import 'package:thrivve/features/withdraw/domain/entities/onboarding_item.dart';
import 'package:thrivve/features/withdraw/domain/entities/payment_method_creation_setting.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../../topUp/domain/entities/top_up_sccessfully.dart';
import '../../domain/entities/withdraw.dart';
import '../../domain/entities/withdraw_confirmation.dart';
import '../../domain/repositories/withdraw_repository.dart';
import '../data_sources/withdraw_remote_data_source.dart';

class WithdrawRepositoryImpl implements WithdrawRepository {
  final WithdrawRemoteDataSource remoteDataSource;
  final NetworkInfo? networkInfo;

  WithdrawRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, WithdrawConfirmation?>> checkWithdrawFees(
      {required double amount, required int paymentMethodId}) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final feesResponse = await remoteDataSource.enterAmountAndCheckFees(
            amount: amount, paymentMethodId: paymentMethodId);
        return Right(feesResponse);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on ServerExceptionWithListOfMessage catch (e) {
        return Left(ServerFailureWithListOfMessage(
            listOfErrorMessages: e.listOfErrorMessages));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, TopUpSuccessfully?>> makeWithdrawRequest(
      {required double amount, required int paymentMethodId}) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final message = await remoteDataSource.makeWithdrawRequest(
            amount: amount, paymentMethodId: paymentMethodId);
        return Right(message);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Withdraw?>> getWithdrawRequestCreationDetails() async {
    if (await networkInfo?.isConnected == true) {
      try {
        final details =
            await remoteDataSource.getWithdrawRequestCreationDetails();
        return Right(details);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentMethodCreationSetting?>>
      getPaymentMethodCreationSetting() async {
    if (await networkInfo?.isConnected == true) {
      try {
        final setting =
            await remoteDataSource.getPaymentMethodCreationSetting();
        return Right(setting);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, WithdrawConfirmation?>> getTransactionDetails(
      {required String transactionId}) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final details = await remoteDataSource.getTransactionDetails(
            transactionId: transactionId);
        return Right(details);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String?>> cancelTransaction(
      {required String transactionId}) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final message = await remoteDataSource.cancelTransaction(
            transactionId: transactionId);
        return Right(message);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<OnboardingItem>?>> getOnboardingSetting(
      {required String key}) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final onboardingItems =
            await remoteDataSource.getOnboardingSetting(key: key);
        return Right(onboardingItems);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
