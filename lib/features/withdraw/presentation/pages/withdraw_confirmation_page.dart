import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/helper.dart';
import '../../../../core/widget/button.dart';
import '../../domain/entities/withdraw_confirmation.dart';
import '../manager/withdraw_bloc.dart';
import '../widgets/icon_text_row_widget.dart';
import '../widgets/transaction_detail_row.dart';

// need refactoring
class WithdrawConfirmationScreen extends StatelessWidget {
  const WithdrawConfirmationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.containerColor,
      child: SafeArea(
        top: false,
        bottom: false,
        minimum: EdgeInsets.only(bottom: 18.h),
        child: Scaffold(
          resizeToAvoidBottomInset: true, // Ensure this is enabled
          appBar: AppBar(
            leadingWidth: 66.w,
            leading: Center(
              child: Container(
                height: 40.h,
                width: 40.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: context.appBackgroundColor,
                ),
                child: IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: Icon(
                      Icons.arrow_back_rounded,
                      color: context.black,
                      size: 20.w,
                    )),
              ),
            ),
          ),
          body: BlocProvider.value(
            value: getIt<WithdrawBloc>(),
            child: BlocListener<WithdrawBloc, WithdrawState>(
              listenWhen: (previous, current) {
                return current.createRequestStatus !=
                    previous.createRequestStatus;
              },
              listener: (context, state) {
                switch (state.createRequestStatus) {
                  case AppStatus.success:
                    Get.toNamed(
                      AppRoutes.paymentRequestDonePage,
                      parameters: {
                        "message": state.confirmationMessage?.message ?? "",
                        "title": state.confirmationMessage?.title ??
                            "request_send_successfully".tr
                      },
                    )?.then((value) {
                      Get.back(result: value);
                    });

                    break;
                  case AppStatus.failure:
                    errorSnackBar(
                      context: context,
                      message: state.errorMessage,
                    );
                    break;
                  default:
                    break;
                }
              },
              child: BlocBuilder<WithdrawBloc, WithdrawState>(
                builder: (context, state) {
                  if (state.withdrawConfirmation == null) {
                    Get.back();
                  }
                  final withdrawConfirmation = state.withdrawConfirmation!;

                  return Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(height: 8.h),
                              SizedBox(
                                height: 30.h,
                                child: CustomTextWidget(
                                  title: "withdraw_confirmation".tr,
                                  size: 22,
                                  fontWeight: FontWeight.w600,
                                  color: context.black,
                                ),
                              ),
                              SizedBox(height: 40.h),
                              CustomTextWidget(
                                title: withdrawConfirmation.message ?? "",
                                size: 12,
                                color: context.lightBlack,
                                fontWeight: FontWeight.w600,
                              ),
                              SizedBox(height: 20.h),
                              _transactionContainer(withdrawConfirmation),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            Divider(
                              color: Colors.grey,
                              thickness: 0.4.h,
                              height: 0,
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                left: 16.0.w,
                                right: 16.0.w,
                                bottom: 6.0.h,
                                top: 12.0.h,
                              ),
                              child: Button(
                                widget: state.createRequestStatus ==
                                        AppStatus.loading
                                    ? SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ))
                                    : null,
                                enable: true,
                                key: Key('confirm_button'),
                                text: "confirm".tr,
                                onTab: () {
                                  log("message");
                                  getIt<WithdrawBloc>().add(MakeWithdrawRequest(
                                      amount: state.paymentAmount,
                                      paymentMethodId:
                                          state.selectedPaymentMethod?.id));
                                },
                                height: 36.h,
                                fontWeight: FontWeight.w600,
                              ),
                            )
                          ],
                        ),
                      )
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _transactionContainer(WithdrawConfirmation withdrawConfirmation) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        TransactionDetailRow(
          label: withdrawConfirmation.sentMessage ?? "",
          countryCode: withdrawConfirmation.countryCode ?? "",
          currency: withdrawConfirmation.currency ?? "",
          amount: withdrawConfirmation.sentAmount ?? "",
        ),
        ...?withdrawConfirmation.steps?.map((step) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 24.h),
            child: IconTextRowWidget(
              isFirst: step == withdrawConfirmation.steps?.first,
              isLast: step == withdrawConfirmation.steps?.last,
              icon: step.icon,
              text: step.message,
            ),
          );
        }),
        TransactionDetailRow(
          label: withdrawConfirmation.receivedMessage ?? "",
          countryCode: withdrawConfirmation.countryCode ?? "",
          currency: withdrawConfirmation.currency ?? "",
          amount: withdrawConfirmation.receivedAmount ?? "",
        ),
      ],
    );
  }
}
