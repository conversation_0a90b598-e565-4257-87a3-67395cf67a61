import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/button.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

import '../../../../core/widget/border_button.dart';
import '../../domain/entities/onboarding_item.dart';

class OnboardingListDialog extends StatefulWidget {
  final List<OnboardingItem> items;

  const OnboardingListDialog({super.key, required this.items});

  @override
  _OnboardingListDialogState createState() => _OnboardingListDialogState();
}

class _OnboardingListDialogState extends State<OnboardingListDialog> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  void _onNextPressed() {
    if (_currentPage < widget.items.length - 1) {
      setState(() {
        _currentPage++;
      });
    } else {
      Navigator.of(context).pop(); // Close dialog on the last page
    }
  }

  void _onBackPressed() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
        // Show the dialog after moving to the previous position
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print("Current page: $_currentPage");
    final currentItem = widget.items[_currentPage];

    return Stack(
      children: [
        AnimatedPositioned(
          duration: Duration(milliseconds: 300),
          top: currentItem.yPosition?.h,
          left: 0,
          right: 0,
          child: Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 8.w),
            backgroundColor: Colors.transparent,
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.topCenter,
                  child: CustomPaint(
                    painter: ArrowPainter(),
                    child: SizedBox(width: 20.w, height: 20.h),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 20.h),
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: context.containerColor,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ExpandablePageView.builder(
                        controller: _pageController,
                        itemCount: widget.items.length,
                        onPageChanged: (index) {
                          setState(() {
                            _currentPage = index;
                          });
                        },
                        itemBuilder: (context, index) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CustomTextWidget(
                                title: currentItem.title,
                                size: 15,
                                fontWeight: FontWeight.w600,
                                color: context.lightBlack,
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 20.h),
                              CustomTextWidget(
                                title: currentItem.description,
                                size: 12,
                                fontWeight: FontWeight.w400,
                                color: context.lightBlack,
                                textAlign: TextAlign.start,
                              ),
                            ],
                          );
                        },
                      ),
                      SizedBox(height: 32.h),
                      _buildIndicator(),
                      SizedBox(height: 32.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (_currentPage > 0)
                            BorderButton(
                              padding: EdgeInsets.symmetric(horizontal: 18.w),
                              text: "back".tr,
                              fontWeight: FontWeight.w700,
                              fontSize: 12.sp,
                              onTab: _onBackPressed,
                            ),
                          Spacer(),
                          Button(
                              fontWeight: FontWeight.w700,
                              padding: EdgeInsets.symmetric(horizontal: 18.w),
                              fontSize: 12.sp,
                              height: 36,
                              text: _currentPage == widget.items.length - 1
                                  ? "done".tr
                                  : "next".tr,
                              onTab: _onNextPressed),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.items.length,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: 8.w,
          height: 8.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentPage == index
                ? context.appPrimaryColor
                : Colors.grey[300],
          ),
        ),
      ),
    );
  }
}

// Custom painter for the arrow pointing to the dialog
class ArrowPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = Get.context!.whiteColor!;
    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
