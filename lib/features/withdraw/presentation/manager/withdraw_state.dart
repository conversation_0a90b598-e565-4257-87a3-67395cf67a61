part of 'withdraw_bloc.dart';

class WithdrawState extends Equatable {
  final AppStatus status;
  final AppStatus validationStatus;
  final AppStatus createRequestStatus;
  final AppStatus getPaymentMethods;
  final AppStatus getPublicBanks;
  final AppStatus addBankStatus;
  final List<double>? suggestedAmounts;
  final WithdrawConfirmation? withdrawConfirmation;
  final List<PaymentMethod>? paymentMethods;
  final List<Bank>? publicBanks;
  final double? availableBalance;
  final double? paymentAmount;
  final String? errorMessage;
  final String? validationMessage;
  final TopUpSuccessfully? confirmationMessage;
  final Withdraw? withdraw;
  final Withdraw? balanceCreationDetails;
  final PaymentMethod? selectedPaymentMethod;
  final List<String?>? paymentMethodErrors;
  final Color? validationColor;
  final PaymentMethodCreationSetting? paymentMethodCreationSetting;
  final AddBankSuccessfully? addBankSuccessfully;
  final bool? showWithdrawOnboardingDialog;
  final bool? showPaymentMethodOnboardingDialog;
  final bool? withdrawEnabled;
  final bool confirmBtnStatus;
  final List<OnboardingItem>? listOfWithdrawOnboardingItems;
  final OnboardingItem? paymentMethodOnboardingItem;

  const WithdrawState({
    this.status = AppStatus.initial,
    this.addBankStatus = AppStatus.initial,
    this.validationStatus = AppStatus.initial,
    this.createRequestStatus = AppStatus.initial,
    this.getPaymentMethods = AppStatus.initial,
    this.getPublicBanks = AppStatus.initial,
    this.suggestedAmounts,
    this.showPaymentMethodOnboardingDialog,
    this.paymentMethodErrors,
    this.showWithdrawOnboardingDialog,
    this.paymentAmount,
    this.addBankSuccessfully,
    this.confirmationMessage,
    this.validationColor,
    this.validationMessage,
    this.paymentMethods,
    this.withdrawConfirmation,
    this.publicBanks,
    this.availableBalance,
    this.errorMessage,
    this.withdraw,
    this.balanceCreationDetails,
    this.selectedPaymentMethod,
    this.paymentMethodCreationSetting,
    this.withdrawEnabled,
    this.confirmBtnStatus = true,
    this.listOfWithdrawOnboardingItems,
    this.paymentMethodOnboardingItem,
  });

  bool get isDataReady =>
      getPublicBanks == AppStatus.success &&
      paymentMethodCreationSetting != null;

  WithdrawState copyWith({
    AppStatus Function()? status,
    AppStatus Function()? addBankStatus,
    AppStatus Function()? validationStatus,
    AppStatus Function()? createRequestStatus,
    AppStatus Function()? getPaymentMethods,
    AppStatus Function()? getPublicBanks,
    Color? Function()? validationColor,
    AddBankSuccessfully? Function()? addBankSuccessfully,
    WithdrawConfirmation? Function()? withdrawConfirmation,
    List<double>? Function()? suggestedAmounts,
    List<PaymentMethod>? Function()? paymentMethods,
    List<Bank>? Function()? publicBanks,
    double? Function()? availableBalance,
    String? Function()? errorMessage,
    String? Function()? validationMessage,
    TopUpSuccessfully? Function()? confirmationMessage,
    double? Function()? paymentAmount,
    bool? Function()? showWithdrawOnboardingDialog,
    bool? Function()? withdrawEnabled,
    bool? Function()? showPaymentMethodOnboardingDialog,
    Withdraw? Function()? withdraw,
    Withdraw? Function()? balanceCreationDetails,
    PaymentMethod? Function()? selectedPaymentMethod,
    PaymentMethodCreationSetting? Function()? paymentMethodCreationSetting,
    bool Function()? confirmBtnStatus,
    List<OnboardingItem>? Function()? listOfWithdrawOnboardingItems,
    OnboardingItem? Function()? paymentMethodOnboardingItem,
    List<String?>? Function()? paymentMethodErrors,
  }) {
    return WithdrawState(
      status: status != null ? status() : this.status,
      paymentMethodErrors: paymentMethodErrors != null
          ? paymentMethodErrors()
          : this.paymentMethodErrors,
      confirmBtnStatus:
          confirmBtnStatus != null ? confirmBtnStatus() : this.confirmBtnStatus,
      withdrawEnabled:
          withdrawEnabled != null ? withdrawEnabled() : this.withdrawEnabled,
      showPaymentMethodOnboardingDialog:
          showPaymentMethodOnboardingDialog != null
              ? showPaymentMethodOnboardingDialog()
              : this.showPaymentMethodOnboardingDialog,
      showWithdrawOnboardingDialog: showWithdrawOnboardingDialog != null
          ? showWithdrawOnboardingDialog()
          : this.showWithdrawOnboardingDialog,
      paymentAmount:
          paymentAmount != null ? paymentAmount() : this.paymentAmount,
      addBankSuccessfully: addBankSuccessfully != null
          ? addBankSuccessfully()
          : this.addBankSuccessfully,
      addBankStatus:
          addBankStatus != null ? addBankStatus() : this.addBankStatus,
      paymentMethodCreationSetting: paymentMethodCreationSetting != null
          ? paymentMethodCreationSetting()
          : this.paymentMethodCreationSetting,
      getPublicBanks:
          getPublicBanks != null ? getPublicBanks() : this.getPublicBanks,
      getPaymentMethods: getPaymentMethods != null
          ? getPaymentMethods()
          : this.getPaymentMethods,
      createRequestStatus: createRequestStatus != null
          ? createRequestStatus()
          : this.createRequestStatus,
      validationColor:
          validationColor != null ? validationColor() : this.validationColor,
      validationStatus:
          validationStatus != null ? validationStatus() : this.validationStatus,
      validationMessage: validationMessage != null
          ? validationMessage()
          : this.validationMessage,
      withdrawConfirmation: withdrawConfirmation != null
          ? withdrawConfirmation()
          : this.withdrawConfirmation,
      selectedPaymentMethod: selectedPaymentMethod != null
          ? selectedPaymentMethod()
          : this.selectedPaymentMethod,
      balanceCreationDetails: balanceCreationDetails != null
          ? balanceCreationDetails()
          : this.balanceCreationDetails,
      withdraw: withdraw != null ? withdraw() : this.withdraw,
      confirmationMessage: confirmationMessage != null
          ? confirmationMessage()
          : this.confirmationMessage,
      suggestedAmounts:
          suggestedAmounts != null ? suggestedAmounts() : this.suggestedAmounts,
      paymentMethods:
          paymentMethods != null ? paymentMethods() : this.paymentMethods,
      publicBanks: publicBanks != null ? publicBanks() : this.publicBanks,
      availableBalance:
          availableBalance != null ? availableBalance() : this.availableBalance,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
      listOfWithdrawOnboardingItems: listOfWithdrawOnboardingItems != null
          ? listOfWithdrawOnboardingItems()
          : this.listOfWithdrawOnboardingItems,
      paymentMethodOnboardingItem: paymentMethodOnboardingItem != null
          ? paymentMethodOnboardingItem()
          : this.paymentMethodOnboardingItem,
    );
  }

  @override
  List<Object?> get props => [
        paymentMethodErrors,
        status,
        withdrawEnabled,
        confirmBtnStatus,
        showPaymentMethodOnboardingDialog,
        showWithdrawOnboardingDialog,
        paymentAmount,
        addBankStatus,
        paymentMethodCreationSetting,
        getPublicBanks,
        getPaymentMethods,
        confirmationMessage,
        createRequestStatus,
        validationColor,
        validationStatus,
        balanceCreationDetails,
        suggestedAmounts,
        withdrawConfirmation,
        validationMessage,
        paymentMethods,
        publicBanks,
        withdraw,
        availableBalance,
        errorMessage,
        selectedPaymentMethod,
        addBankSuccessfully,
        listOfWithdrawOnboardingItems,
        paymentMethodOnboardingItem,
      ];
}
