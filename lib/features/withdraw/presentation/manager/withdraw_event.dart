part of 'withdraw_bloc.dart';

@immutable
sealed class WithdrawEvent {}

class CheckWithdraw<PERSON>ees extends WithdrawEvent {
  final String? amount;

  CheckWithdrawFees({required this.amount});
}

class CheckWithdrawFeesImpl extends WithdrawEvent {
  final String? amount;

  CheckWithdrawFeesImpl({required this.amount});
}

class ValidateWithDrawEvent extends WithdrawEvent {}

class GetPaymentMethods extends WithdrawEvent {}

class GetWithdrawEvent extends WithdrawEvent {}

class GetPaymentMethodCreationSettingEvent extends WithdrawEvent {}

class AddBankEvent extends WithdrawEvent {
  final String? beneficiaryIban;
  final String? beneficiaryName;
  final String? bankId;

  AddBankEvent({
    this.beneficiaryIban,
    this.beneficiaryName,
    this.bankId,
  });
}

class MakeWithdrawRequest extends WithdrawEvent {
  final double? amount;
  final int? paymentMethodId;

  MakeWithdrawRequest({
    required this.amount,
    required this.paymentMethodId,
  });
}

class GetPublicBanks extends WithdrawEvent {}

class GetAddNewBankDataEvent extends WithdrawEvent {}

//SelectPaymentMethodEvent
class SelectPaymentMethodEvent extends WithdrawEvent {
  final PaymentMethod? paymentMethod;

  SelectPaymentMethodEvent({required this.paymentMethod});
}

class OnboardingDialogEvent extends WithdrawEvent {
  OnboardingDialogEvent();
}
