import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/withdraw_confirmation.dart';
import '../repositories/withdraw_repository.dart';

class CheckWithdrawFeesUseCase
    extends UseCase<WithdrawConfirmation?, WithdrawFeesParams> {
  final WithdrawRepository repository;

  CheckWithdrawFeesUseCase({required this.repository});

  @override
  Future<Either<Failure, WithdrawConfirmation?>> call(
      WithdrawFeesParams params) async {
    return repository.checkWithdrawFees(
        amount: params.amount, paymentMethodId: params.paymentMethodId);
  }
}

// param amount and payment method
class WithdrawFeesParams {
  final double amount;
  final int paymentMethodId;

  WithdrawFeesParams({required this.amount, required this.paymentMethodId});
}
