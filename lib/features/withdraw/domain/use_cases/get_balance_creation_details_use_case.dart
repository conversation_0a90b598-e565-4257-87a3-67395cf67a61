import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/withdraw.dart';
import '../repositories/withdraw_repository.dart';

class GetBalanceCreationDetailsUseCase extends UseCase<Withdraw?, NoParams> {
  final WithdrawRepository repository;

  GetBalanceCreationDetailsUseCase({required this.repository});

  @override
  Future<Either<Failure, Withdraw?>> call(NoParams params) async {
    return await repository.getWithdrawRequestCreationDetails();
  }
}
