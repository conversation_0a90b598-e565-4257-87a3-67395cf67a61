import 'package:equatable/equatable.dart';

class WithdrawConfirmation extends Equatable {
  final String? title;
  final String? message;
  final String? sentMessage;
  final String? sentAmount;
  final String? receivedMessage;
  final String? receivedAmount;
  final String? feesMessage;
  final String? fees;
  final List<WithdrawStep>? steps;
  final String? countryCode;
  final String? currency;
  final String? status;
  final String? statusKey;
  final String? creationDate;
  final String? approvedDate;
  final String? canceledDate;
  final String? rejectedDate;
  final Map<dynamic, dynamic>? data;
  final String? messageNotes;

  const WithdrawConfirmation({
    required this.title,
    required this.statusKey,
    required this.message,
    required this.sentMessage,
    required this.sentAmount,
    required this.receivedMessage,
    required this.receivedAmount,
    required this.feesMessage,
    required this.fees,
    required this.steps,
    required this.countryCode,
    required this.currency,
    this.status,
    this.creationDate,
    this.approvedDate,
    this.canceledDate,
    this.rejectedDate,
    required this.data,
    this.messageNotes,
  });

  @override
  List<Object?> get props => [
        message,
        sentMessage,
        sentAmount,
        receivedMessage,
        statusKey,
        receivedAmount,
        feesMessage,
        status,
        fees,
        messageNotes,
        steps,
        countryCode,
        currency,
        creationDate,
        approvedDate,
        canceledDate,
        rejectedDate,
        data,
        title,
      ];
}

// Class to handle each step in the steps list
class WithdrawStep extends Equatable {
  final String? icon;
  final String? message;
  final String? url;
  final bool? isClickable;

  const WithdrawStep({
    required this.icon,
    required this.message,
    this.url,
    this.isClickable,
  });

  @override
  List<Object?> get props => [icon, message, url, isClickable];
}
