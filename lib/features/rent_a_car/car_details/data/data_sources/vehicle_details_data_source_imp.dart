import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/data_sources/i_vehicle_details_data_source.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/apply_rent_car_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/car_details_additional_atributes_response_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/car_details_response_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/my_subscription_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/price_breakdown_response_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_get_car_price.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_review_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_vehicle_additional_atts.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/kyc_response_model.dart';

class VehicleDetailsDataSourceImplementation extends VehicleDetailsDataSource {
  final ApiClient apiClient;
  VehicleDetailsDataSourceImplementation(this.apiClient);

  @override
  Future<CarDetailsResponseModel?> getVehicleDetails(int? id) async {
    final response = await apiClient.request<CarDetailsResponseModel>(
        endpoint: ApiSettings.getLeaseVihecleDetails,
        queryParameters: {"vehicle_id": id},
        fromJson: (json) => CarDetailsResponseModel.fromJson(json));
    return response.data;
  }

  @override
  Future<RentCarPriceBreakdownResponseModel?> getRentCarPrice(
      GetRentCarPriceParams getRentCarPriceParams) async {
    Map<String, dynamic> queryParameters = getRentCarPriceParams.toJson();

    queryParameters.removeWhere((key, value) => value == null);
    log(queryParameters.toString());
    final response =
        await apiClient.request<RentCarPriceBreakdownResponseModel>(
            endpoint: ApiSettings.getLeaseVihecleDetailsPrice,
            queryParameters: queryParameters,
            fromJson: (json) =>
                RentCarPriceBreakdownResponseModel.fromJson(json));
    return response.data;
  }

  @override
  Future<CarRentAdditionalAttributesResponseModel?> getVehicleAdditionalDetails(
      VehiclesAdditionalDetailsParams vehiclesAdditionalDetailsParams) async {
    final response =
        await apiClient.request<CarRentAdditionalAttributesResponseModel>(
            endpoint: ApiSettings.getLeaseVihecleAdditionalDetailsPrice,
            queryParameters: vehiclesAdditionalDetailsParams.toJson(),
            fromJson: (json) =>
                CarRentAdditionalAttributesResponseModel.fromJson(json));
    return response.data;
  }

  @override
  Future<ApplyRentCarModel?> reviewAndCheckout(
      ReviewAndCheckoutParams reviewAndCheckoutParams) async {
    final response = await apiClient.request<ApplyRentCarModel>(
        endpoint: ApiSettings.reviewAndCheckoutRentCar,
        method: RequestType.post,
        data: FormData.fromMap(reviewAndCheckoutParams.toJson()),
        fromJson: (json) => ApplyRentCarModel.fromJson(json));
    return response.data;
  }

  @override
  Future<MySubscriptionModel?> mySubscription() async {
    final response = await apiClient.request<MySubscriptionModel>(
        endpoint: ApiSettings.mySubscription,
        fromJson: (json) => MySubscriptionModel.fromJson(json));
    return response.data;
  }

  @override
  Future<KYCResponseModel?> submitUpdateVehicle(
    Map<String, dynamic> kycData,
  ) async {
    final endpoint = '${ApiSettings.rentCarKYC}/ll';
    final response = await apiClient.request<KYCResponseModel>(
      endpoint: endpoint,
      method: RequestType.post,
      data: kycData,
      fromJson: (json) => KYCResponseModel.fromJson(json),
    );
    return response.data;
  }
}
