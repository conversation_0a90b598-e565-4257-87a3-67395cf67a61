import 'package:thrivve/features/rent_a_car/car_details/data/mappers/add_on_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/insurance_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/mileage_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/package_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/vehicle_details_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/vehicle_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/car_details_additional_atributes_response_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/car_details_response_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_additional_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_details_entity.dart';

extension CarDetailsExtension on CarDetailsResponseModel {
  CarDetailsEntity toEntity() {
    return CarDetailsEntity(
        package: package?.map((e) => e.toEntity()).toList(),
        includedItems: includedItems,
        vehicle: vehicle?.toEntity(),
        vehicleDetails: vehicleDetails?.map((e) => e.toEntity()).toList());
  }
}

extension CarAdditionalDetailsExtension
    on CarRentAdditionalAttributesResponseModel {
  CarAdditionalDetailsEntity toEntity() {
    return CarAdditionalDetailsEntity(
      addOns: addOns?.map((e) => e.toEntity()).toList(),
      mileage: mileage?.map((e) => e.toEntity()).toList(),
      insurance: insurance?.map((e) => e.toEntity()).toList(),
    );
  }
}
