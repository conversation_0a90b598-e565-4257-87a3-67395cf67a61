import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_entity.dart';

import '../models/price_model.dart';

extension PriceModelMapper on RentCarPriceModel {
  RentCarPriceEntity toEntity() {
    return RentCarPriceEntity(
        discountPrice: discountPrice,
        price: price,
        currency: currency,
        totalPrice: totalPrice,
        description: description,
        period: period);
  }
}
