import 'package:thrivve/features/rent_a_car/car_details/data/models/add_on_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/add_on_entity.dart';

extension AddOnExtension on AddOnModel {
  AddOnEntity toEntity() {
    return AddOnEntity(
        id: id,
        description: description,
        title: title,
        subTitle: subTitle,
        extraNote: extraNote,
        isPopular: isPopular,
        note: note,
        isMostCommon: isMostCommon,
        icon: icon,
        isDefault: isDefault,
        currency: currency,
        period: period,
        price: price,
        baseDuration: baseDuration);
  }
}
