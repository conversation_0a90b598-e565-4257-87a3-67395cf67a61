import 'dart:convert';

import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/kyc_type_enum.dart';

class EditVehicleDetailsModel {
  final List<int>? addOnsIds;
  final int? milageId;
  final int? insuranceId;
  final int? commitmentMonths;
  final int? vehiclePricingId;
  final int? vehicleId;
  final bool updateAddsOn;

  const EditVehicleDetailsModel(
      {this.addOnsIds,
      this.commitmentMonths,
      this.insuranceId,
      this.milageId,
      this.vehicleId,
      this.vehiclePricingId,
      this.updateAddsOn = false});

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {
      'add_ons_ids': addOnsIds,
      'milage_id': milageId,
      'insurance_id': insuranceId,
      'commitment_months': commitmentMonths,
      'vehicle_pricing_id': vehiclePricingId,
      'vehicle_id': vehicleId,
      'update_add_ons': updateAddsOn
    };
    map.removeWhere((key, value) => value == null);
    return map;
  }
}
