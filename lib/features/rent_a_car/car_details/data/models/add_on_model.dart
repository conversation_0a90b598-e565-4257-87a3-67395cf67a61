class AddOnModel {
  final int? _id;
  final String? _description;
  final String? _title;
  final String? _subTitle;
  final String? _extraNote;
  final bool? _isPopular;
  final String? _note;
  final String? _icon;
  final bool? _isMostCommon;
  final bool? _isDefault;
  final String? _currency;
  final String? _price;
  final String? _period;
  final String? _baseDuration;

  AddOnModel({
    int? id,
    String? description,
    String? title,
    String? subTitle,
    String? extraNote,
    bool? isPopular,
    String? note,
    String? icon,
    bool? isMostCommon,
    bool? isDefault,
    String? currency,
    String? price,
    String? period,
    String? baseDuration,
  })  : _id = id,
        _description = description,
        _title = title,
        _subTitle = subTitle,
        _extraNote = extraNote,
        _isPopular = isPopular,
        _note = note,
        _isMostCommon = isMostCommon,
        _icon = icon,
        _isDefault = isDefault,
        _currency = currency,
        _price = price,
        _period = period,
        _baseDuration = baseDuration;

  int? get id => _id;
  String? get description => _description;
  String? get title => _title;
  String? get subTitle => _subTitle;
  String? get extraNote => _extraNote;
  bool? get isPopular => _isPopular;
  String? get note => _note;
  String? get icon => _icon;
  bool? get isMostCommon => _isMostCommon;
  bool? get isDefault => _isDefault;
  String? get currency => _currency;
  String? get price => _price;
  String? get period => _period;
  String? get baseDuration => _baseDuration;

  factory AddOnModel.fromJson(Map<String, dynamic> json) {
    return AddOnModel(
      id: json['id'] as int?,
      description: json['description'] as String?,
      title: json['title'] as String?,
      subTitle: json['sub_title'] as String?,
      extraNote: json['extra_note'] as String?,
      isPopular: json['is_popular'] as bool?,
      note: json['note'] as String?,
      isMostCommon: json['is_most_common'] as bool?,
      isDefault: json['is_default'] as bool?,
      icon: json['icon'] as String?,
      currency: json['currency'] as String?,
      price: json['price'] as String?,
      period: json['period'] as String?,
      baseDuration: json['base_duration'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': _id,
      'description': _description,
      'title': _title,
      'sub_title': _subTitle,
      'extra_note': _extraNote,
      'is_popular': _isPopular,
      'note': _note,
      'is_most_common': _isMostCommon,
      'is_default': _isDefault,
      'icon': _icon,
      'currency': _currency,
      'price': _price,
      'period': _period,
      'base_duration': _baseDuration,
    };
  }

  AddOnModel copyWith({
    int? id,
    String? description,
    String? title,
    String? subTitle,
    String? extraNote,
    bool? isPopular,
    String? note,
    String? icon,
    bool? isMostCommon,
    bool? isDefault,
    String? currency,
    String? price,
    String? period,
    String? baseDuration,
  }) {
    return AddOnModel(
      id: id ?? _id,
      description: description ?? _description,
      title: title ?? _title,
      subTitle: subTitle ?? _subTitle,
      extraNote: extraNote ?? _extraNote,
      isPopular: isPopular ?? _isPopular,
      note: note ?? _note,
      isMostCommon: isMostCommon ?? _isMostCommon,
      isDefault: isDefault ?? _isDefault,
      icon: icon ?? _icon,
      currency: currency ?? _currency,
      price: price ?? _price,
      period: period ?? _period,
      baseDuration: baseDuration ?? _baseDuration,
    );
  }
}
