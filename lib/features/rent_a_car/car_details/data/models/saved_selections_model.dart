class SavedSelectionsModel {
  final List<int>? addOnsIds;
  final int? insuranceId;
  final int? milageId;
  final int? vehicleId;
  final int? vehiclePricingId;
  final int? commitmentMonths;

  SavedSelectionsModel({
    this.addOnsIds,
    this.insuranceId,
    this.milageId,
    this.vehicleId,
    this.vehiclePricingId,
    this.commitmentMonths,
  });

  factory SavedSelectionsModel.fromJson(Map<String, dynamic> json) {
    return SavedSelectionsModel(
      addOnsIds: (json['add_ons_ids'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      insuranceId: json['insurance_id'] as int?,
      milageId: json['milage_id'] as int?,
      vehicleId: json['vehicle_id'] as int?,
      vehiclePricingId: json['vehicle_pricing_id'] as int?,
      commitmentMonths: json['commitment_months'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'add_ons_ids': addOnsIds,
      'insurance_id': insuranceId,
      'milage_id': milageId,
      'vehicle_id': vehicleId,
      'vehicle_pricing_id': vehiclePricingId,
      'commitment_months': commitmentMonths,
    };
  }
}
