class PackageModel {
  final int? _commitmentMonths;
  final int? _id;
  final String? _description;
  final String? _title;
  final String? _subTitle;
  final String? _extraNote;
  final String? _note;
  final bool? _isMostCommon;
  final bool? _isDefault;
  final String? _currency;
  final String? _price;
  final String? _period;

  PackageModel({
    int? commitmentMonths,
    int? id,
    String? description,
    String? title,
    String? subTitle,
    String? extraNote,
    String? note,
    bool? isMostCommon,
    bool? isDefault,
    String? currency,
    String? price,
    String? period,
  })  : _commitmentMonths = commitmentMonths,
        _id = id,
        _description = description,
        _title = title,
        _subTitle = subTitle,
        _extraNote = extraNote,
        _note = note,
        _isMostCommon = isMostCommon,
        _isDefault = isDefault,
        _currency = currency,
        _price = price,
        _period = period;

  int? get commitmentMonths => _commitmentMonths;
  int? get id => _id;
  String? get description => _description;
  String? get title => _title;
  String? get subTitle => _subTitle;
  String? get extraNote => _extraNote;
  String? get note => _note;
  bool? get isMostCommon => _isMostCommon;
  bool? get isDefault => _isDefault;
  String? get currency => _currency;
  String? get price => _price;
  String? get period => _period;

  factory PackageModel.fromJson(Map<String, dynamic> json) {
    return PackageModel(
      commitmentMonths: json['commitment_months'] as int?,
      id: json['id'] as int?,
      description: json['description'] as String?,
      title: json['title'] as String?,
      subTitle: json['sub_title'] as String?,
      extraNote: json['extra_note'] as String?,
      note: json['note'] as String?,
      isMostCommon: json['is_most_common'] as bool?,
      isDefault: json['is_default'] as bool?,
      currency: json['currency'] as String?,
      price: json['price'] as String?,
      period: json['period'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'commitment_months': _commitmentMonths,
      'id': _id,
      'description': _description,
      'title': _title,
      'sub_title': _subTitle,
      'extra_note': _extraNote,
      'note': _note,
      'is_most_common': _isMostCommon,
      'is_default': _isDefault,
      'currency': _currency,
      'price': _price,
      'period': _period,
    };
  }

  PackageModel copyWith({
    int? commitmentMonths,
    int? id,
    String? description,
    String? title,
    String? subTitle,
    String? extraNote,
    String? note,
    bool? isMostCommon,
    bool? isDefault,
    String? currency,
    String? price,
    String? period,
  }) {
    return PackageModel(
      commitmentMonths: commitmentMonths ?? _commitmentMonths,
      id: id ?? _id,
      description: description ?? _description,
      title: title ?? _title,
      subTitle: subTitle ?? _subTitle,
      extraNote: extraNote ?? _extraNote,
      note: note ?? _note,
      isMostCommon: isMostCommon ?? _isMostCommon,
      isDefault: isDefault ?? _isDefault,
      currency: currency ?? _currency,
      price: price ?? _price,
      period: period ?? _period,
    );
  }
}
