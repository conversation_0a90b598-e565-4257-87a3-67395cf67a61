import 'price_model.dart';
import 'price_breakdown_model.dart';

class PriceExtraNoteModel {
  final String? _description;
  final String? _note;

  PriceExtraNoteModel({
    String? description,
    String? note,
  })  : _description = description,
        _note = note;

  String? get description => _description;
  String? get note => _note;

  factory PriceExtraNoteModel.fromJson(Map<String, dynamic> json) {
    return PriceExtraNoteModel(
      description: json['description'] as String?,
      note: json['note'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'description': _description,
      'note': _note,
    };
  }
}

class RentCarPriceBreakdownResponseModel {
  final RentCarPriceBreakdownModel? _priceBreakdown;
  final RentCarPriceModel? _price;
  final RentCarPriceModel? _enrolmentFees;
  final RentCarPriceModel? _monthlyFees;
  final PriceExtraNoteModel? _extraNote;

  RentCarPriceBreakdownResponseModel({
    RentCarPriceBreakdownModel? priceBreakdown,
    RentCarPriceModel? price,
    RentCarPriceModel? enrolmentPrice,
    RentCarPriceModel? monthlyFees,
    PriceExtraNoteModel? extraNote,
  })  : _priceBreakdown = priceBreakdown,
        _price = price,
        _enrolmentFees = enrolmentPrice,
        _monthlyFees = monthlyFees,
        _extraNote = extraNote;

  RentCarPriceBreakdownModel? get priceBreakdown => _priceBreakdown;
  RentCarPriceModel? get price => _price;
  RentCarPriceModel? get enrolmentFees => _enrolmentFees;
  RentCarPriceModel? get monthlyFees => _monthlyFees;
  PriceExtraNoteModel? get extraNote => _extraNote;

  factory RentCarPriceBreakdownResponseModel.fromJson(
      Map<String, dynamic> json) {
    return RentCarPriceBreakdownResponseModel(
      priceBreakdown: json['price_breakdown'] != null
          ? RentCarPriceBreakdownModel.fromJson(
              json['price_breakdown'] as Map<String, dynamic>)
          : null,
      price: json['total'] != null
          ? RentCarPriceModel.fromJson(json['total'] as Map<String, dynamic>)
          : null,
      enrolmentPrice: json['enrolment_fees'] != null
          ? RentCarPriceModel.fromJson(
              json['enrolment_fees'] as Map<String, dynamic>)
          : null,
      monthlyFees: json['monthly_fees'] != null
          ? RentCarPriceModel.fromJson(
              json['monthly_fees'] as Map<String, dynamic>)
          : null,
      extraNote: json['extra_note'] != null
          ? PriceExtraNoteModel.fromJson(
              json['extra_note'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'price_breakdown': _priceBreakdown?.toJson(),
      'price': _price?.toJson(),
      'extra_note': _extraNote?.toJson(),
      'monthly_fees': _monthlyFees?.toJson(),
    };
  }
}
