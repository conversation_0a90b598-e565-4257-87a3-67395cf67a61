class MileageModel {
  final String? _description;
  final int? _id;
  final String? _title;
  final String? _subTitle;
  final bool? _isPopular;
  final String? _note;
  final bool? _isMostCommon;
  final bool? _isDefault;
  final String? _currency;
  final String? _price;
  final String? _period;

  MileageModel({
    String? description,
    int? id,
    String? title,
    String? subTitle,
    bool? isPopular,
    String? note,
    bool? isMostCommon,
    bool? isDefault,
    String? currency,
    String? price,
    String? period,
  })  : _description = description,
        _id = id,
        _title = title,
        _subTitle = subTitle,
        _isPopular = isPopular,
        _note = note,
        _isMostCommon = isMostCommon,
        _isDefault = isDefault,
        _currency = currency,
        _price = price,
        _period = period;

  String? get description => _description;
  int? get id => _id;
  String? get title => _title;
  String? get subTitle => _subTitle;
  bool? get isPopular => _isPopular;
  String? get note => _note;
  bool? get isMostCommon => _isMostCommon;
  bool? get isDefault => _isDefault;
  String? get currency => _currency;
  String? get price => _price;
  String? get period => _period;

  factory MileageModel.fromJson(Map<String, dynamic> json) {
    return MileageModel(
      description: json['description'] as String?,
      id: json['id'] as int?,
      title: json['title'] as String?,
      subTitle: json['sub_title'] as String?,
      isPopular: json['is_popular'] as bool?,
      note: json['note'] as String?,
      isMostCommon: json['is_most_common'] as bool?,
      isDefault: json['is_default'] as bool?,
      currency: json['currency'] as String?,
      price: json['price'] as String?,
      period: json['period'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'description': _description,
      'id': _id,
      'title': _title,
      'sub_title': _subTitle,
      'is_popular': _isPopular,
      'note': _note,
      'is_most_common': _isMostCommon,
      'is_default': _isDefault,
      'currency': _currency,
      'price': _price,
      'period': _period,
    };
  }

  MileageModel copyWith({
    String? description,
    int? id,
    String? title,
    String? subTitle,
    bool? isPopular,
    String? note,
    bool? isMostCommon,
    bool? isDefault,
    String? currency,
    String? price,
    String? period,
  }) {
    return MileageModel(
      description: description ?? _description,
      id: id ?? _id,
      title: title ?? _title,
      subTitle: subTitle ?? _subTitle,
      isPopular: isPopular ?? _isPopular,
      note: note ?? _note,
      isMostCommon: isMostCommon ?? _isMostCommon,
      isDefault: isDefault ?? _isDefault,
      currency: currency ?? _currency,
      price: price ?? _price,
      period: period ?? _period,
    );
  }
}
