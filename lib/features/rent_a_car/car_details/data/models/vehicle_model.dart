class ImageModel {
  final String? _url;

  ImageModel({String? url}) : _url = url;

  String? get url => _url;

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(url: json['url'] as String?);
  }

  Map<String, dynamic> toJson() {
    return {'url': _url};
  }
}

class RentVehicleModel {
  final bool? _isAvailable;
  final String? _vehicleClass;
  final int? _id;
  final String? _contractType;
  final String? _title;
  final String? _subTitle;
  final String? _monthlyPriceTitle;
  final String? _manufactureYear;
  final int? _vehicleId;
  final bool? _fixedFleet;
  final String? _milage;
  final String? _priceNote;
  final String? _period;
  final String? _monthlyPrice;
  final double? _rate;
  final List<String>? _notes;
  final String? _currency;
  final String? _dailyPriceBetween;
  final int? _supplierId;
  final List<ImageModel>? _images;
  final bool? _isMostCommon;

  RentVehicleModel({
    bool? isAvailable,
    String? vehicleClass,
    int? id,
    String? contractType,
    String? title,
    String? subtitle,
    String? monthlyPriceTitle,
    String? manufactureYear,
    int? vehicleId,
    bool? fixedFleet,
    String? milage,
    String? priceNote,
    String? period,
    String? monthlyPrice,
    double? rate,
    List<String>? notes,
    String? currency,
    String? dailyPriceBetween,
    int? supplierId,
    List<ImageModel>? images,
    bool? isMostCommon,
  })  : _isAvailable = isAvailable,
        _vehicleClass = vehicleClass,
        _id = id,
        _contractType = contractType,
        _title = title,
        _subTitle = subtitle,
        _monthlyPriceTitle = monthlyPriceTitle,
        _manufactureYear = manufactureYear,
        _images = images,
        _vehicleId = vehicleId,
        _fixedFleet = fixedFleet,
        _milage = milage,
        _priceNote = priceNote,
        _period = period,
        _monthlyPrice = monthlyPrice,
        _rate = rate,
        _notes = notes,
        _currency = currency,
        _dailyPriceBetween = dailyPriceBetween,
        _supplierId = supplierId,
        _isMostCommon = isMostCommon;

  bool? get isAvailable => _isAvailable;
  String? get vehicleClass => _vehicleClass;
  int? get id => _id;
  String? get contractType => _contractType;
  String? get title => _title;
  String? get subtitle => _subTitle;
  String? get monthlyPriceTitle => _monthlyPriceTitle;
  String? get manufactureYear => _manufactureYear;

  int? get vehicleId => _vehicleId;
  bool? get fixedFleet => _fixedFleet;
  String? get milage => _milage;
  String? get priceNote => _priceNote;
  String? get period => _period;
  String? get monthlyPrice => _monthlyPrice;
  double? get rate => _rate;
  List<String>? get notes => _notes;
  String? get currency => _currency;
  String? get dailyPriceBetween => _dailyPriceBetween;
  int? get supplierId => _supplierId;
  List<ImageModel>? get images => _images;
  bool? get isMostCommon => _isMostCommon;

  factory RentVehicleModel.fromJson(Map<String, dynamic> json) {
    return RentVehicleModel(
      isAvailable: json['is_available'] as bool?,
      vehicleClass: json['vehicle_class'] as String?,
      id: json['id'] as int?,
      contractType: json['contract_type'] as String?,
      title: json['title'] as String?,
      subtitle: json['sub_titile'] as String?,
      monthlyPriceTitle: json['monthly_price_title'] as String?,
      manufactureYear: json['manufacture_year'] as String?,
      vehicleId: json['vehicle_id'] as int?,
      fixedFleet: json['fixed_fleet'] as bool?,
      milage: json['milage'] as String?,
      priceNote: json['price_note'] as String?,
      period: json['period'] as String?,
      monthlyPrice: json['monthly_price'].toString(),
      rate: (json['rate'] as num?)?.toDouble(),
      notes:
          (json['notes'] as List<dynamic>?)?.map((e) => e as String).toList(),
      currency: json['currency'] as String?,
      dailyPriceBetween: json['daily_price_between'] as String?,
      supplierId: json['supplier_id'] as int?,
      images: json['images'] == null
          ? null
          : (json['images'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? ImageModel(url: "")
                  : ImageModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      isMostCommon: json['is_most_common'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_available': _isAvailable,
      'vehicle_class': _vehicleClass,
      'id': _id,
      'contract_type': _contractType,
      'title': _title,
      'sub_titile': _subTitle,
      'monthly_price_title': _monthlyPriceTitle,
      'manufacture_year': _manufactureYear,
      'vehicle_id': _vehicleId,
      'fixed_fleet': _fixedFleet,
      'milage': _milage,
      'price_note': _priceNote,
      'period': _period,
      'monthly_price': _monthlyPrice,
      'rate': _rate,
      'notes': _notes,
      'currency': _currency,
      'daily_price_between': _dailyPriceBetween,
      'supplier_id': _supplierId,
      'images': _images?.map((e) => e.toJson()).toList(),
      'is_most_common': _isMostCommon,
    };
  }

  RentVehicleModel copyWith({
    bool? isAvailable,
    String? vehicleClass,
    int? id,
    String? contractType,
    String? title,
    String? subtitle,
    String? monthlyPriceTitle,
    String? manufactureYear,
    int? vehicleId,
    bool? fixedFleet,
    String? milage,
    String? priceNote,
    String? period,
    String? monthlyPrice,
    double? rate,
    List<String>? notes,
    String? currency,
    String? dailyPriceBetween,
    int? supplierId,
    List<ImageModel>? images,
    bool? isMostCommon,
  }) {
    return RentVehicleModel(
      isAvailable: isAvailable ?? _isAvailable,
      vehicleClass: vehicleClass ?? _vehicleClass,
      id: id ?? _id,
      contractType: contractType ?? _contractType,
      title: title ?? _title,
      subtitle: subtitle ?? _subTitle,
      monthlyPriceTitle: monthlyPriceTitle ?? _monthlyPriceTitle,
      manufactureYear: manufactureYear ?? _manufactureYear,
      vehicleId: vehicleId ?? _vehicleId,
      fixedFleet: fixedFleet ?? _fixedFleet,
      milage: milage ?? _milage,
      priceNote: priceNote ?? _priceNote,
      period: period ?? _period,
      monthlyPrice: monthlyPrice ?? _monthlyPrice,
      rate: rate ?? _rate,
      notes: notes ?? _notes,
      currency: currency ?? _currency,
      dailyPriceBetween: dailyPriceBetween ?? _dailyPriceBetween,
      supplierId: supplierId ?? _supplierId,
      images: images ?? _images,
      isMostCommon: isMostCommon ?? _isMostCommon,
    );
  }
}
