import 'package:equatable/equatable.dart';
import 'price_entity.dart';

class RentCarPriceBreakdownEntity extends Equatable {
  final String? title;
  final String? subTitle;
  final List<RentCarPriceBreakdownItemEntity>? priceBreakdownItems;

  const RentCarPriceBreakdownEntity({
    this.priceBreakdownItems,
    this.subTitle,
    this.title,
  });

  @override
  List<Object?> get props => [
        priceBreakdownItems,
        subTitle,
        title,
      ];
}

class RentCarPriceBreakdownItemEntity extends Equatable {
  final String? title;
  final List<RentCarPriceEntity>? items;
  final RentCarPriceEntity? subItem;
  const RentCarPriceBreakdownItemEntity(
      {required this.title, required this.items, required this.subItem});

  @override
  // TODO: implement props
  List<Object?> get props => [title, items, subItem];
}
