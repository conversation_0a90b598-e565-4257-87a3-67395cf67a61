import 'package:equatable/equatable.dart';

class PackageEntity extends Equatable {
  final int? commitmentMonths;
  final int? id;
  final String? description;
  final String? title;
  final String? subTitle;
  final String? extraNote;
  final String? note;
  final bool? isMostCommon;
  final bool? isDefault;
  final String? currency;
  final String? price;
  final String? period;

  const PackageEntity({
    this.commitmentMonths,
    this.id,
    this.description,
    this.title,
    this.subTitle,
    this.extraNote,
    this.note,
    this.isMostCommon,
    this.isDefault,
    this.currency,
    this.price,
    this.period,
  });

  @override
  List<Object?> get props => [
        commitmentMonths,
        id,
        description,
        title,
        subTitle,
        extraNote,
        note,
        isMostCommon,
        isDefault,
        currency,
        price,
        period,
      ];
}
