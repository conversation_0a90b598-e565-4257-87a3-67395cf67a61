import 'package:equatable/equatable.dart';

class InsuranceEntity extends Equatable {
  final String? description;
  final int? id;
  final String? title;
  final String? subTitle;
  final bool? isPopular;
  final String? note;
  final bool? isMostCommon;
  final bool? isDefault;
  final String? currency;
  final String? price;
  final String? period;

  const InsuranceEntity({
    this.description,
    this.id,
    this.title,
    this.subTitle,
    this.isPopular,
    this.note,
    this.isMostCommon,
    this.isDefault,
    this.currency,
    this.price,
    this.period,
  });

  @override
  List<Object?> get props => [
        description,
        id,
        title,
        subTitle,
        isPopular,
        note,
        isMostCommon,
        isDefault,
        currency,
        price,
        period,
      ];
}
