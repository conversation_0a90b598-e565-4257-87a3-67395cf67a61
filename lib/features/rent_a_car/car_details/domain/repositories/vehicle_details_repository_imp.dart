import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/data_sources/i_vehicle_details_data_source.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/apply_rent_car_mapper.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/car_details_extension.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/price_breakdown_mapper.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/mappers/my_subscription_mapper.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_edit_car_details.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_get_car_price.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_review_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_vehicle_additional_atts.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/apply_rent_car_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_additional_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/my_subscription_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_breakdown_response_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/repositories/i_vehicle_details_repository.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/kyc_response_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_response_entity.dart';

class VehicleDetailsImplementation extends VehicleDetailsRepository {
  final VehicleDetailsDataSource vehicleDetailsDataSource;
  final NetworkInfo? networkInfo;
  VehicleDetailsImplementation(
      {required this.vehicleDetailsDataSource, required this.networkInfo});

  @override
  Future<Either<Failure, CarDetailsEntity?>> getVehicleDetails(int? id) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await vehicleDetailsDataSource.getVehicleDetails(id);
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, RentCarPriceBreakdownResponseEntity?>> getRentCarPrice(
      GetRentCarPriceParams getRentCarPriceParams) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await vehicleDetailsDataSource
            .getRentCarPrice(getRentCarPriceParams);
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, CarAdditionalDetailsEntity?>>
      getVehicleAdditionalDetails(
          VehiclesAdditionalDetailsParams
              vehiclesAdditionalDetailsParams) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await vehicleDetailsDataSource
            .getVehicleAdditionalDetails(vehiclesAdditionalDetailsParams);
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, ApplyRentCarEntity?>> reviewAndCheckout(
      ReviewAndCheckoutParams reviewAndCheckoutParams) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await vehicleDetailsDataSource
            .reviewAndCheckout(reviewAndCheckoutParams);
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, MySubscriptionEntity?>> mySubscription() async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await vehicleDetailsDataSource.mySubscription();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, KYCResponseEntity?>> updateVehicleDetails(
    EditVehicleDetailsModel editVehicleDetailsModel,
  ) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result = await vehicleDetailsDataSource
            .submitUpdateVehicle(editVehicleDetailsModel.toJson());
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
