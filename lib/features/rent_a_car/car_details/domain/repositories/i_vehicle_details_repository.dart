import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_edit_car_details.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_get_car_price.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_review_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_vehicle_additional_atts.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/apply_rent_car_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_additional_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/my_subscription_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_breakdown_response_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_response_entity.dart';

abstract class VehicleDetailsRepository {
  Future<Either<Failure, CarDetailsEntity?>> getVehicleDetails(int id);
  Future<Either<Failure, CarAdditionalDetailsEntity?>>
      getVehicleAdditionalDetails(
          VehiclesAdditionalDetailsParams vehiclesAdditionalDetailsParams);
  Future<Either<Failure, RentCarPriceBreakdownResponseEntity?>> getRentCarPrice(
      GetRentCarPriceParams getRentCarPriceParams);
  Future<Either<Failure, ApplyRentCarEntity?>> reviewAndCheckout(
    ReviewAndCheckoutParams reviewAndCheckoutParams,
  );
  Future<Either<Failure, MySubscriptionEntity?>> mySubscription();
  Future<Either<Failure, KYCResponseEntity?>> updateVehicleDetails(
      EditVehicleDetailsModel editVehicleDetailsModel);
}
