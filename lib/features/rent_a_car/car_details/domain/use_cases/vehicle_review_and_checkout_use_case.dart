import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_review_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/apply_rent_car_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/repositories/i_vehicle_details_repository.dart';

class VehicleReviewAndCheckoutUseCase
    implements UseCase<ApplyRentCarEntity?, ReviewAndCheckoutParams> {
  final VehicleDetailsRepository vehicleDetailsRepository;
  VehicleReviewAndCheckoutUseCase(this.vehicleDetailsRepository);
  @override
  Future<Either<Failure, ApplyRentCarEntity?>> call(
      ReviewAndCheckoutParams param) {
    return vehicleDetailsRepository.reviewAndCheckout(param);
  }
}
