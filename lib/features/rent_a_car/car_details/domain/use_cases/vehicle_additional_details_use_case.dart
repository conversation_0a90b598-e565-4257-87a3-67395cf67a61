import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_vehicle_additional_atts.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_additional_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/repositories/i_vehicle_details_repository.dart';

class GetLeaseVehiclesAdditionalDetailsUseCase
    implements
        UseCase<CarAdditionalDetailsEntity?, VehiclesAdditionalDetailsParams> {
  final VehicleDetailsRepository vehicleDetailsRepository;
  GetLeaseVehiclesAdditionalDetailsUseCase(this.vehicleDetailsRepository);
  @override
  Future<Either<Failure, CarAdditionalDetailsEntity?>> call(
      VehiclesAdditionalDetailsParams param) {
    return vehicleDetailsRepository.getVehicleAdditionalDetails(param);
  }
}
