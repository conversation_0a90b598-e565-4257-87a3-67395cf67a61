import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_breakdown_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_breakdown_response_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/widgets/global_widgets/custom_rent_cat_widgets.dart';

class PriceBreakDownButtomsheet extends StatelessWidget {
  RentCarPriceBreakdownResponseEntity? rentCarPriceBreakdownResponseEntity;
  PriceBreakDownButtomsheet(this.rentCarPriceBreakdownResponseEntity);
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.sp))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: Icon(Icons.close)),
              Center(
                child: Column(
                  children: [
                    _buildTitle(rentCarPriceBreakdownResponseEntity
                            ?.priceBreakdown?.title ??
                        "".tr),
                    _buildSubTitle(rentCarPriceBreakdownResponseEntity
                            ?.priceBreakdown?.subTitle ??
                        "".tr),
                  ],
                ).marginOnly(left: 20.sp, right: 20.sp, top: 20.sp),
              ),
              IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: Icon(
                    Icons.close,
                    color: Colors.transparent,
                  )),
            ],
          ),
          Divider(
            height: 20.sp,
            thickness: 2.sp,
            color: context.borderColor,
          ),
          if (rentCarPriceBreakdownResponseEntity
                  ?.priceBreakdown?.priceBreakdownItems !=
              null)
            _buildRentCarPriceBreakdownItem(rentCarPriceBreakdownResponseEntity!
                .priceBreakdown!.priceBreakdownItems!),
          SizedBox(
            height: 40.sp,
          )
          /* Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitle("one_time_charges_bs".tr),
              SizedBox(
                height: 20.sp,
              ),
              ...rentCarPriceBreakdownResponseEntity
                      ?.priceBreakdown?.oneTimeCharges
                      ?.map((e) => CustomRentCarWidgets.buildPriceWidget(
                          title: e.description ?? "",
                          price: e.totalPrice?.toString() ?? '',
                          discount: e.discountPrice?.toString(),
                          currency: e.currency ?? ""))
                      .toList() ??
                  [],
              Divider(
                height: 20.sp,
                thickness: 2.sp,
                color: context.borderColor,
              ),
              CustomRentCarWidgets.buildMainPriceWidget(
                  title: "total_rent_price".tr,
                  price: rentCarPriceBreakdownResponseEntity
                          ?.priceBreakdown?.oneTimeTotal?.price
                          ?.toString() ??
                      '',
                  currency: rentCarPriceBreakdownResponseEntity
                          ?.priceBreakdown?.oneTimeTotal?.currency
                          ?.toString() ??
                      ''),
            ],
          ).marginOnly(left: 20.sp, right: 20.sp, top: 10.sp),
          Divider(
            height: 20.sp,
            thickness: 4.sp,
            color: context.borderColor,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitle("monthly_charges".tr),
              SizedBox(
                height: 20.sp,
              ),
              ...rentCarPriceBreakdownResponseEntity
                      ?.priceBreakdown?.monthlyCharges
                      ?.map((e) => Column(
                            children: [
                              CustomRentCarWidgets.buildPriceWidget(
                                  title: e.description ?? "",
                                  price: e.price?.toString() ?? '',
                                  currency: e.currency ?? ""),
                              SizedBox(
                                height: 10.sp,
                              ),
                            ],
                          ))
                      .toList() ??
                  [],
              Divider(
                height: 20.sp,
                thickness: 2.sp,
                color: context.borderColor,
              ),
              CustomRentCarWidgets.buildMainPriceWidget(
                  title: "total_rent_price".tr,
                  price: rentCarPriceBreakdownResponseEntity
                          ?.priceBreakdown?.monthlyChargesTotal?.price
                          ?.toString() ??
                      '',
                  currency: rentCarPriceBreakdownResponseEntity
                          ?.priceBreakdown?.monthlyChargesTotal?.currency
                          ?.toString() ??
                      ''),
            ],
          ).marginOnly(left: 20.sp, right: 20.sp, top: 20.sp, bottom: 70.sp)
        */
        ],
      ),
    );
  }

  _buildTitle(String title) {
    return CustomTextWidget(
      title: title,
      size: 15,
      fontWeight: FontWeight.w600,
      color: Get.context!.black,
    );
  }

  _buildSubTitle(String title) {
    return CustomTextWidget(
      title: title,
      size: 11,
      fontWeight: FontWeight.w400,
      color: Get.context!.lightBlack,
    );
  }

  Widget _buildRentCarPriceBreakdownItem(
      List<RentCarPriceBreakdownItemEntity> rentCarPriceBreakdownItemEntities) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: rentCarPriceBreakdownItemEntities.map((e) {
        return Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (e.title != null)
                  Column(
                    children: [
                      _buildTitle(e.title ?? ''),
                      SizedBox(
                        height: 20.sp,
                      ),
                    ],
                  ),
                if (e.items != null) _buildRentCarPriceItem(e.items!),
                if (e.subItem != null) _buildSubItemns(e.subItem!),
              ],
            ).marginOnly(left: 20.sp, right: 20.sp, top: 10.sp),
            if (e.subItem != null && e.items != null)
              Divider(
                height: 20.sp,
                thickness: 4.sp,
                color: Get.context!.borderColor,
              ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildRentCarPriceItem(List<RentCarPriceEntity> rentCarPriceEntities) {
    return Column(
      children: [
        ...rentCarPriceEntities.map((e) {
          return CustomRentCarWidgets.buildPriceWidget(
              title: e.description ?? "",
              price: e.totalPrice?.toString() ?? '',
              discount: e.discountPrice?.toString(),
              currency: e.currency ?? "");
        }),
        Divider(
          height: 20.sp,
          thickness: 2.sp,
          color: Get.context!.borderColor,
        )
      ],
    );
  }

  Widget _buildSubItemns(RentCarPriceEntity subItem) {
    return CustomRentCarWidgets.buildMainPriceWidget(
        title: subItem.description ?? '',
        price: subItem.totalPrice?.toString() ?? '',
        currency: subItem.currency ?? '');
  }
}
