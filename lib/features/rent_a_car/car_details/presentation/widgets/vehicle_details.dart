import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/vehicle_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/vehicle_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/manager/vehicle_details_controller.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/widgets/bottomsheets/vehicle_details_bottomsheet.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/widgets/global_widgets/custom_rent_cat_widgets.dart';

class RentVehicleDetailsWidget extends GetView<VehicleDetailsController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      VehicleEntity? vehicleEntity = controller.vehicle.value;

      List<InfoEntity> mainItems = controller.vehicleDetails.value ?? [];
      List<InfoItemEntity> allItems = mainItems
          .expand((infoEntity) => infoEntity.items ?? [])
          .toList()
          .cast<InfoItemEntity>();
      List<InfoItemEntity> displayItems = allItems.length <= 3
          ? allItems
          : [
              ...allItems.take(3),
              InfoItemEntity(icon: 'more', value: 'more_details'.tr)
            ];

      return controller.isMainLoading.value
          ? _buildShimmer()
          : Container(
              padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 30.sp),
              width: Get.width,
              color: context.appPrimaryColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomTextWidget(
                          title: vehicleEntity?.title ?? '',
                          color: Colors.white,
                          size: 22,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                      if ((vehicleEntity?.vehicleClass ?? '').isNotEmpty)
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.sp, vertical: 7.sp),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20.sp)),
                          child: CustomTextWidget(
                            title: vehicleEntity?.vehicleClass ?? '',
                            color: Colors.black,
                            size: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        )
                    ],
                  ),
                  CustomTextWidget(
                    title: vehicleEntity?.subTitle ?? '',
                    color: Color(0xffD8D8D8),
                    size: 15,
                    fontWeight: FontWeight.w400,
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Visibility(
                      visible: displayItems.isNotEmpty,
                      child: GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisExtent: 18.sp,
                          mainAxisSpacing: 8.h,
                          crossAxisSpacing: 10.w,
                        ),
                        itemCount: displayItems.length,
                        itemBuilder: (context, index) {
                          final item = displayItems[index];
                          return InkWell(
                            onTap: () {
                              if (item.icon == 'more') {
                                Get.bottomSheet(VehicleDetailsBottomsheet(
                                  controller.vehicleDetails.value,
                                ));
                              }
                            },
                            child: CustomRentCarWidgets.buildDetailsItem(
                                item.icon ?? '',
                                item.value ?? item.description ?? ''),
                          );
                        },
                      ))
                ],
              ),
            );
    });
  }

  Widget _buildShimmer() {
    return Container(
      color: Get.context!.appBackgroundColor,
      padding: const EdgeInsets.all(16.0),
      child: Shimmer.fromColors(
        baseColor: Get.context!.borderAddBranch,
        highlightColor: Get.context!.appBackgroundColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 150,
                  height: 20,
                  color: Colors.white,
                ),
                Container(
                  width: 60,
                  height: 20,
                  color: Colors.white,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              width: 200,
              height: 14,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            GridView(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisExtent: 25,
                mainAxisSpacing: 10.h,
                crossAxisSpacing: 10.w,
              ),
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.directions_car, color: Colors.grey),
                    SizedBox(
                      width: 8.w,
                    ),
                    Container(
                      width: 50,
                      height: 14,
                      color: Colors.white,
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.directions_car, color: Colors.grey),
                    SizedBox(
                      width: 8.w,
                    ),
                    Container(
                      width: 50,
                      height: 14,
                      color: Colors.white,
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.directions_car, color: Colors.grey),
                    SizedBox(
                      width: 8.w,
                    ),
                    Container(
                      width: 50,
                      height: 14,
                      color: Colors.white,
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.directions_car, color: Colors.grey),
                    SizedBox(
                      width: 8.w,
                    ),
                    Container(
                      width: 50,
                      height: 14,
                      color: Colors.white,
                    ),
                  ],
                )
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
