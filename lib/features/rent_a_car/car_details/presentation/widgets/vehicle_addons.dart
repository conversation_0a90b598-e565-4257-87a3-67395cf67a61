import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/add_on_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/manager/vehicle_details_controller.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/widgets/global_widgets/expanable_notes.dart';

class AddsOnWidget extends GetView<VehicleDetailsController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final addsOns = controller.addOns.value;
      final selectedAddsOns = controller.selectedAddsOns.value;

      return Visibility(
        visible: addsOns?.isNotEmpty ?? false,
        child: Container(
          margin: EdgeInsets.only(left: 20.w, right: 20.w),
          child: Column(
            key: controller.addOnsKey,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(
                height: 40.sp,
              ),
              CustomTextWidget(
                title: "Add-ons".tr,
                size: 20.sp,
                color: context.black,
                fontWeight: FontWeight.w600,
              ),
              SizedBox(
                height: 8.sp,
              ),
              CustomTextWidget(
                title: "additional_services".tr,
                color: context.black.withOpacity(0.6),
                size: 11,
                fontWeight: FontWeight.w400,
              ),
              SizedBox(
                height: 16.sp,
              ),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                        width: 1.sp, color: context.black.withOpacity(0.2)),
                    borderRadius: BorderRadius.circular(12.sp)),
                child: ListView.separated(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (c, i) {
                      AddOnEntity? addOnEntity = controller.addOns.value?[i];
                      return InkWell(
                          onTap: () {
                            controller.addToAddsOn(addOnEntity);
                          },
                          child: _buildAddOnItem(
                              subTitle: addOnEntity?.subTitle ?? '',
                              title: addOnEntity?.title ?? '',
                              baseDuration: addOnEntity?.baseDuration ?? '',
                              addOnEntity: addOnEntity,
                              icon: addOnEntity?.icon,
                              isSelected:
                                  selectedAddsOns.contains(addOnEntity?.id)));
                    },
                    separatorBuilder: (c, i) {
                      return Divider(
                        color: context.borderColor,
                      );
                    },
                    itemCount: controller.addOns.value?.length ?? 0),
              )
            ],
          ),
        ),
      );
    });
  }

  _buildAddOnItem(
      {required String title,
      required String subTitle,
      required String baseDuration,
      String? icon,
      required AddOnEntity? addOnEntity,
      required bool isSelected}) {
    String iconPath = convertStringToImage(icon ?? '');
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 10.sp),
      decoration: BoxDecoration(
          border: Border.all(color: Get.context!.borderColor),
          borderRadius: BorderRadius.circular(12.sp)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(
            iconPath,
            width: 18.sp,
            fit: BoxFit.scaleDown,
            color: Get.context!.black,
          ),
          SizedBox(
            width: 12.sp,
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextWidget(
                          title: title,
                          fontWeight: FontWeight.w600,
                          size: 14,
                        ),
                        CustomFormattedText(
                          text: subTitle,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: Get.context!.black,
                          ),
                          strongStyle: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w700,
                            color: Get.context!.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                  CupertinoSwitch(
                      value: isSelected,
                      activeColor: Get.context!.appPrimaryColor,
                      onChanged: (x) {
                        controller.addToAddsOn(addOnEntity);
                      }),
                ],
              ),
              SizedBox(height: 8.h),
              InsuranceNoteWidget(
                noteText: addOnEntity?.note ?? '',
                showButtonTitle: "more_addson_details".tr,
              )
            ],
          )),
        ],
      ),
    );
  }
}
