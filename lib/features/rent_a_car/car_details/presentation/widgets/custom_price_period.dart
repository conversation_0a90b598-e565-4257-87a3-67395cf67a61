import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

class CustomPricePeriod extends StatelessWidget {
  final String title;
  final String subTitle;

  final bool? isMostCommon;
  final String? note;
  final String? description;
  CustomPricePeriod(
      {required this.title,
      required this.subTitle,
      this.description,
      this.isMostCommon,
      this.note});
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: CustomTextWidget(
                title: title,
                fontWeight: FontWeight.w600,
                size: 14,
              ),
            ),
            if (isMostCommon == true)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 7.h),
                decoration: BoxDecoration(
                  color: Get.context!.outlineButtonColor,
                  borderRadius: BorderRadius.circular(20.sp),
                ),
                child: CustomTextWidget(
                  title: "most_common".tr,
                  color: Get.context!.whiteColor,
                  fontWeight: FontWeight.w600,
                  size: 10,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CustomFormattedText(
              text: subTitle,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: context.black,
              ),
              strongStyle: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: context.black,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
      ],
    );
  }
}
