import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/extentions/safe_cast.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/static_var.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/login_signup/rent_car_sheet_login_register_sheet.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_edit_car_details.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_get_car_price.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_review_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_vehicle_additional_atts.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/add_on_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_additional_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/insurance_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/mileage_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/package_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/price_breakdown_response_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/vehicle_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/vehicle_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/use_cases/my_subscription_usecase.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/use_cases/update_vehicle_details_use_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/use_cases/vehicle_additional_details_use_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/use_cases/vehicle_details_price_use_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/use_cases/vehicle_details_use_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/use_cases/vehicle_review_and_checkout_use_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/arguments/vehicle_details_arguments.dart';

class VehicleDetailsController extends GetxController {
  bool isApply = true;
  GetLeaseVehiclesDetailsUseCase getLeaseVehiclesDetailsUseCase;
  GetLeaseVehiclePriceDetailsUseCase getLeaseVehiclePriceDetailsUseCase;
  GetLeaseVehiclesAdditionalDetailsUseCase
      getLeaseVehiclesAdditionalDetailsUseCase;
  VehicleReviewAndCheckoutUseCase vehicleReviewAndCheckoutUseCase;
  MySubscriptionUseCase mySubscriptionUseCase;
  UpdateVehicleDetailsUseCase updateVehicleDetailsUseCase;
  final bool? hasAlreadyApplication;
  VehicleDetailsController(
      {required this.getLeaseVehiclesDetailsUseCase,
      required this.getLeaseVehiclePriceDetailsUseCase,
      required this.getLeaseVehiclesAdditionalDetailsUseCase,
      required this.vehicleReviewAndCheckoutUseCase,
      required this.mySubscriptionUseCase,
      required this.updateVehicleDetailsUseCase,
      this.hasAlreadyApplication});

  late int vehicleId;
  ScrollController scrollController = ScrollController();
  TextEditingController mobileController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  final UserSecureDataSource _secureStorage = getIt<UserSecureDataSource>();
  // vehicle details attributes
  final RxDouble appBarOpacity = 0.0.obs;
  RxBool isMainLoading = false.obs;
  RxBool isAdditionalLoading = false.obs;
  RxBool isPriceLoading = false.obs;
  Rxn<List<AddOnEntity>> addOns = Rxn<List<AddOnEntity>>();
  Rxn<List<MileageEntity>> mileage = Rxn<List<MileageEntity>>();
  Rxn<List<InsuranceEntity>> insurance = Rxn<List<InsuranceEntity>>();
  Rxn<List<PackageEntity>> package = Rxn<List<PackageEntity>>();
  Rxn<List<String>> includedItems = Rxn<List<String>>();
  Rxn<List<InfoEntity>> vehicleDetails = Rxn<List<InfoEntity>>();
  Rxn<VehicleEntity> vehicle = Rxn<VehicleEntity>();
  Rxn<List<String>> images = Rxn<List<String>>();

  RxnInt selectedPackageEntityId = RxnInt();
  RxnInt selectedPackageCommitmentMontEntityId = RxnInt();
  RxnInt selectedInsuranceEntityId = RxnInt();
  RxnInt selectedMilegeEntityId = RxnInt();
  RxList<int> selectedAddsOns = RxList<int>([]);
  RxBool isLoginButtonEnabled = false.obs;
  bool addsOnIsUpdated = false;

  // Save current selections to secure storage

  getMySubscription() async {
    _trackUserInteraction('fetching_subscription');
    isMainLoading.value = true;

    final result = await mySubscriptionUseCase(NoParams());
    isMainLoading.value = false;
    result.fold(
      (failure) {
        _captureMessage('Subscription fetch failed',
            level: SentryLevel.error,
            data: {
              'error': failure.toString(),
              'timestamp': DateTime.now().toIso8601String(),
            });
        _handleFailure(failure);
      },
      (subscription) {
        if (subscription != null) {
          _trackUserInteraction('subscription_fetch_success', data: {
            'vehicle_id': subscription.vehicleId,
            'pricing_id': subscription.vehiclePricingId,
            'commitment_months': subscription.commitmentMonths,
            'insurance_id': subscription.insuranceId,
            'mileage_id': subscription.mileageId,
            'addons_ids': subscription.addOnsIds,
          });
          // fill subscription selections
          vehicleId = subscription.vehicleId ?? 0;
          selectedPackageEntityId.value = subscription.vehiclePricingId;
          selectedPackageCommitmentMontEntityId.value =
              subscription.commitmentMonths;
          selectedInsuranceEntityId.value = subscription.insuranceId;
          selectedMilegeEntityId.value = subscription.mileageId;
          selectedAddsOns.value = subscription.addOnsIds ?? [];
          getVehicleDetails();
        }
      },
    );
  }

  RxBool isMySubscription = false.obs;
  getArgument() {
    arguments = safeCast<VehicleDetailsArguments>(Get.arguments);
    vehicleId = arguments?.vehicleId ?? 0;
    isMySubscription.value = false;
    getVehicleDetails();
  }

  fillCarDetailsEntities(CarDetailsEntity carDetailsEntity) {
    package.value = carDetailsEntity.package;
    includedItems.value = carDetailsEntity.includedItems;
    vehicle.value = carDetailsEntity.vehicle;
    vehicleDetails.value = carDetailsEntity.vehicleDetails;
    getImagesFromController(vehicle.value!);
    if (isMySubscription.value == false) {
      if (package.value != null) {
        final defaultPackage = package.value!
            .where((element) => element.isDefault == true)
            .firstOrNull;
        if (defaultPackage != null) {
          selectedPackageEntityId.value = defaultPackage.id;
          selectedPackageCommitmentMontEntityId.value =
              defaultPackage.commitmentMonths;
          getVehicleAdditionalDetailsWithTimer();
        }
      }
    } else {
      getVehicleAdditionalDetailsWithTimer();
    }
  }

  fillCarAdditionalEntities(CarAdditionalDetailsEntity carDetailsEntity) {
    addOns.value = carDetailsEntity.addOns;
    mileage.value = carDetailsEntity.mileage;
    insurance.value = carDetailsEntity.insurance;
    if (isMySubscription.value == false) {
      if (insurance.value != null) {
        final defaultInsurance = insurance.value!
            .where((element) => element.isDefault == true)
            .firstOrNull;
        selectedInsuranceEntityId.value = defaultInsurance?.id;
      }

      if (mileage.value != null) {
        final defaultMileage = mileage.value!
            .where((element) => element.isDefault == true)
            .firstOrNull;
        selectedMilegeEntityId.value = defaultMileage?.id;
      }

      if (addOns.value != null) {
        selectedAddsOns.clear();
        for (var addOn in addOns.value!) {
          if (addOn.isDefault == true && addOn.id != null) {
            selectedAddsOns.add(addOn.id!);
          }
        }
      }
    } else {
      calculateVehiclePrice();
    }
  }

  getImagesFromController(VehicleEntity vehicleEntity) {
    images.value = vehicleEntity.images?.map((e) => e.url ?? '').toList() ?? [];
  }

  getVehicleDetails() async {
    isMainLoading.value = true;
    final results = await getLeaseVehiclesDetailsUseCase.call(vehicleId);
    isMainLoading.value = false;
    results.fold((failure) {
      _captureMessage('Vehicle details fetch failed',
          level: SentryLevel.error,
          data: {
            'error': failure.toString(),
            'vehicle_id': vehicleId,
            'timestamp': DateTime.now().toIso8601String(),
          });
      _handleFailure(failure);
    }, (data) {
      fillCarDetailsEntities(data!);
    });
  }

  Timer? _timer;
  getVehicleAdditionalDetailsWithTimer() {
    _timer?.cancel();
    _timer = Timer(const Duration(milliseconds: 500), () {
      getVehicleAdditionalDetails(selectedPackageEntityId.value!,
          selectedPackageCommitmentMontEntityId.value!);
      calculateVehiclePrice();
    });
  }

  getVehicleAdditionalDetails(int packageId, int commitmentMonthId) async {
    isAdditionalLoading.value = true;
    final results = await getLeaseVehiclesAdditionalDetailsUseCase.call(
        VehiclesAdditionalDetailsParams(
            packageId: packageId, commitmentMonthId: commitmentMonthId));
    isAdditionalLoading.value = false;
    results.fold((failure) {
      _captureMessage('Additional details fetch failed',
          level: SentryLevel.error,
          data: {
            'error': failure.toString(),
            'package_id': packageId,
            'commitment_month_id': commitmentMonthId,
            'timestamp': DateTime.now().toIso8601String(),
          });
      _handleFailure(failure);
    }, (data) {
      fillCarAdditionalEntities(data!);
    });
  }

  Rxn<RentCarPriceBreakdownResponseEntity> rentCarPriceBreakdownEntity =
      Rxn<RentCarPriceBreakdownResponseEntity>();
  calcualtePriceAfterTimer() {
    _timer?.cancel();
    _timer = Timer(const Duration(milliseconds: 500), () {
      calculateVehiclePrice();
    });
  }

  calculateVehiclePrice() async {
    _trackUserInteraction('calculating_price', data: {
      'package_id': selectedPackageEntityId.value,
      'commitment_month_id': selectedPackageCommitmentMontEntityId.value,
      'insurance_id': selectedInsuranceEntityId.value,
      'mileage_id': selectedMilegeEntityId.value,
      'addons_ids': selectedAddsOns,
    });
    isPriceLoading.value = true;
    GetRentCarPriceParams getRentCarPriceParams = GetRentCarPriceParams(
        addsOnsIds: selectedAddsOns,
        commitmentMonthId: selectedPackageCommitmentMontEntityId.value,
        insuranceId: selectedInsuranceEntityId.value,
        mileageId: selectedMilegeEntityId.value,
        vehiclePriceId: selectedPackageEntityId.value);
    log(getRentCarPriceParams.addsOnsIds.toString());
    final result =
        await getLeaseVehiclePriceDetailsUseCase.call(getRentCarPriceParams);
    isPriceLoading.value = false;
    result.fold((failure) {
      _captureMessage('Price calculation failed',
          level: SentryLevel.error,
          data: {
            'error': failure.toString(),
            'package_id': selectedPackageEntityId.value,
            'commitment_month_id': selectedPackageCommitmentMontEntityId.value,
            'timestamp': DateTime.now().toIso8601String(),
          });
      _handleFailure(failure);
    }, (data) {
      rentCarPriceBreakdownEntity.value = data;
    });
  }

  reviewAndCheckout() async {
    final isUserLoggedIn = await _secureStorage.isLogin();
    _trackUserInteraction('review_and_checkout_started', data: {
      'is_logged_in': isUserLoggedIn,
      'vehicle_id': vehicleId,
      'package_id': selectedPackageEntityId.value,
      'commitment_month_id': selectedPackageCommitmentMontEntityId.value,
      'insurance_id': selectedInsuranceEntityId.value,
      'mileage_id': selectedMilegeEntityId.value,
      'addons_ids': selectedAddsOns,
    });

    if (!isUserLoggedIn) {
      // Save current selections before redirecting to login
      StaticVar.reviewAndCheckoutParams = ReviewAndCheckoutParams(
        insuranceId: selectedInsuranceEntityId.value,
        mileageId: selectedMilegeEntityId.value,
        vehicleId: vehicleId,
        vehiclePricingId: selectedPackageEntityId.value!,
        addOnsIds: selectedAddsOns.value,
        commitmentMonthId: selectedPackageCommitmentMontEntityId.value!,
      );
      showRentCarLoginOrRegisterSheet(
        Get.context!,
        fromRent: true,
        dlParams: arguments?.dlParams,
      );
      return;
    }

    if (selectedPackageEntityId.value == null ||
        selectedPackageCommitmentMontEntityId.value == null) {
      _captureMessage('Checkout validation failed',
          level: SentryLevel.error,
          data: {
            'error': 'lack_in_rent_selection',
            'vehicle_id': vehicleId,
            'timestamp': DateTime.now().toIso8601String(),
          });
      errorSnackBar(
        context: Get.context!,
        title: "err".tr,
        message: "lack_in_rent_selection".tr,
      );
      return;
    }

    showLoaderDialog(Get.context!);
    final result = await vehicleReviewAndCheckoutUseCase.call(
      ReviewAndCheckoutParams(
        vehicleId: vehicleId,
        vehiclePricingId: selectedPackageEntityId.value!,
        commitmentMonthId: selectedPackageCommitmentMontEntityId.value!,
        insuranceId: selectedInsuranceEntityId.value,
        mileageId: selectedMilegeEntityId.value,
        addOnsIds: selectedAddsOns,
      ),
    );
    _finishChildSpan('customize_subscription');
    dismissLoaderDialog(Get.context!);

    result.fold(
      (failure) {
        String message = mapFailureToMessage(failure);
        log(message);
        if (message == "Cannot create application with active product") {
          showExistAppWarningSheet(Get.context!);
          return;
        }
        _captureMessage('Checkout process failed',
            level: SentryLevel.error,
            data: {
              'error': failure.toString(),
              'vehicle_id': vehicleId,
              'timestamp': DateTime.now().toIso8601String(),
            });
        _handleFailure(failure);
      },
      (data) {
        StaticVar.reviewAndCheckoutParams = null;
        isApply = false;
        Get.toNamed(AppRoutes.rentCheckoutPage);
      },
    );
  }

  editVehicleDetails() async {
    _trackUserInteraction('edit_vehicle_details_initiated', data: {
      'vehicle_id': vehicleId,
      'package_id': selectedPackageEntityId.value,
      'commitment_months': selectedPackageCommitmentMontEntityId.value,
      'insurance_id': selectedInsuranceEntityId.value,
      'mileage_id': selectedMilegeEntityId.value,
      'addons_ids': selectedAddsOns,
      'is_addons_updated': addsOnIsUpdated,
    });

    showLoaderDialog(Get.context!);
    EditVehicleDetailsModel input = EditVehicleDetailsModel(
        addOnsIds: selectedAddsOns,
        commitmentMonths: selectedPackageCommitmentMontEntityId.value,
        insuranceId: selectedInsuranceEntityId.value,
        milageId: selectedMilegeEntityId.value,
        vehicleId: vehicleId,
        vehiclePricingId: selectedPackageEntityId.value,
        updateAddsOn: true);

    final result = await updateVehicleDetailsUseCase.call(input);
    dismissLoaderDialog(Get.context!);
    result.fold(
      (failure) {
        _captureMessage('Edit vehicle details failed',
            level: SentryLevel.error,
            data: {
              'error': failure.toString(),
              'vehicle_id': vehicleId,
              'timestamp': DateTime.now().toIso8601String(),
            });
        _handleFailure(failure);
      },
      (data) {
        Get.toNamed(AppRoutes.rentCheckoutPage);
      },
    );
  }

  selectPackage(PackageEntity? packageEntity) {
    if (packageEntity?.id != null) {
      _trackUserInteraction('package_selected', data: {
        'package_id': packageEntity?.id,
        'commitment_months': packageEntity?.commitmentMonths,
      });
      selectedPackageEntityId.value = packageEntity?.id!;
      selectedPackageCommitmentMontEntityId.value =
          packageEntity?.commitmentMonths!;
      getVehicleAdditionalDetailsWithTimer();
    }
  }

  selectInsurance(InsuranceEntity? insuranceEntity) {
    if (insuranceEntity?.id != null) {
      _trackUserInteraction('insurance_selected', data: {
        'insurance_id': insuranceEntity?.id,
      });
      selectedInsuranceEntityId.value = insuranceEntity!.id!;
      calcualtePriceAfterTimer();
    }
  }

  selectMilage(MileageEntity? mileageEntity) {
    if (mileageEntity?.id != null) {
      _trackUserInteraction('mileage_selected', data: {
        'mileage_id': mileageEntity?.id,
      });
      selectedMilegeEntityId.value = mileageEntity!.id!;
      calcualtePriceAfterTimer();
    }
  }

  addToAddsOn(AddOnEntity? addOnEntity) {
    if (isMySubscription.value) {
      addsOnIsUpdated = true;
    }
    if (addOnEntity?.id != null) {
      final isAdding = !selectedAddsOns.value.contains(addOnEntity?.id);
      _trackUserInteraction('addon_toggled', data: {
        'addon_id': addOnEntity?.id,
        'action': isAdding ? 'added' : 'removed',
        'is_subscription': isMySubscription.value,
      });

      if (isAdding) {
        selectedAddsOns.add(addOnEntity!.id!);
      } else {
        selectedAddsOns.removeWhere((e) => e == addOnEntity!.id!);
      }

      calcualtePriceAfterTimer();
      selectedAddsOns.refresh();
    }
  }

  _handleFailure(Failure failure) {
    dismissLoaderDialog(Get.context!);
    final error = mapFailureToMessage(failure);
    errorSnackBar(context: Get.context!, title: "err".tr, message: error);
  }

  final _arguments = Rx<VehicleDetailsArguments?>(null);
  VehicleDetailsArguments? get arguments => _arguments.value;
  set arguments(VehicleDetailsArguments? newValue) =>
      _arguments.value = newValue;

  @override
  void onInit() {
    super.onInit();
    _startChildSpan('customize_subscription', 'customize_subscription.start');
    getArgument();
    initializeScrollListener();
  }

  void initializeScrollListener() {
    scrollController.addListener(() {
      final offset = scrollController.offset;
      final maxExtent = Get.width -
          ((2 * kToolbarHeight) + 20.h); // Match with FlexibleSpaceBar height

      // Only show title when FlexibleSpaceBar has completely scrolled out
      if (offset > maxExtent) {
        appBarOpacity.value = 1.0;
      } else {
        appBarOpacity.value = 0.0;
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
  }

  final GlobalKey insuranceKey = GlobalKey();
  final GlobalKey milageKey = GlobalKey();
  final GlobalKey addOnsKey = GlobalKey();

  void _finishChildSpan(String s) {
    SentryService.instance.finishSpan(s);
  }

  void _trackUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'customize_subscription: $action'.toUpperCase(),
      category: 'vehicle_details'.toLowerCase(),
      data: {
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        if (data != null) ...data,
      },
    );
  }

  void _captureMessage(String message,
      {SentryLevel level = SentryLevel.info, Map<String, dynamic>? data}) {
    SentryService.instance.captureMessage(
      message,
      level: level,
      data: data,
    );
  }

  ISentrySpan? _startChildSpan(String name, String operation) {
    return SentryService.instance.startChildSpan(name, operation);
  }
}
