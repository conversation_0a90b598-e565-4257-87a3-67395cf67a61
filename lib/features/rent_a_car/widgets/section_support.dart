import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/util/analytics_actions.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/widgets/map_thrivve_sheet.dart';
import 'package:thrivve/generated/assets.dart';

class SupportSection extends StatelessWidget {
  final bool showAllItems;
  const SupportSection({super.key, this.showAllItems = true});

  void _trackUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'Section Support: $action',
      category: 'rent_support',
      data: {
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        if (data != null) ...data,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: context.colorF4F5F7,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.0),
          topRight: Radius.circular(12.0),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          CustomTextWidget(
            title:
                showAllItems ? 'need_more_information'.tr : "like_visit_us".tr,
            size: 15,
            fontWeight: FontWeight.w600,
            color: context.black,
          ),
          SizedBox(height: 7.h),
          // Subtitle
          if (showAllItems)
            CustomTextWidget(
              title: 'pls_reach_out_to_our_support'.tr,
              size: 11,
              color: context.black.withValues(alpha: 0.7),
            ),
          if (showAllItems) const SizedBox(height: 16.0),
          // Buttons: FAQs and Contact us
          if (showAllItems)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // FAQs Button
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _trackUserInteraction('faqs_clicked');
                      getIt<IAnalyticsLogger>()
                          .logEvent(AnalyticsActions.helpCenter);
                      Get.toNamed(AppRoutes.faqScreen);
                    },
                    icon: SvgPicture.asset(
                      Assets.rentACarFaqsIcon,
                      width: 15.w,
                      height: 15.w,
                      colorFilter: ColorFilter.mode(
                          context.outlineButtonColor, BlendMode.srcIn),
                    ),
                    label: CustomTextWidget(
                      title: 'faqs'.tr,
                      color: context.outlineButtonColor,
                      size: 12,
                      fontWeight: FontWeight.w700,
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: context.outlineButtonColor,
                        width: 1.sp,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12.0),
                    ),
                  ),
                ),
                const SizedBox(width: 16.0),
                // Contact us Button
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _trackUserInteraction('contact_us_clicked');
                      Get.toNamed(AppRoutes.rentNeedHelpPage);
                    },
                    icon: SvgPicture.asset(
                      Assets.rentACarContactUs,
                      width: 15.w,
                      height: 15.w,
                      colorFilter: ColorFilter.mode(
                          context.outlineButtonColor, BlendMode.srcIn),
                    ),
                    label: CustomTextWidget(
                      title: 'contact_us_title'.tr,
                      color: context.outlineButtonColor,
                      size: 12,
                      fontWeight: FontWeight.w700,
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: context.outlineButtonColor,
                        width: 1.sp,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12.0),
                    ),
                  ),
                ),
              ],
            ),
          const SizedBox(height: 16.0),
          // Visit us Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _visitUs(context),
              icon: SvgPicture.asset(
                Assets.rentACarVisitUs,
                width: 15.w,
                height: 15.w,
                colorFilter: ColorFilter.mode(
                    context.outlineButtonColor, BlendMode.srcIn),
              ),
              label: CustomTextWidget(
                title: "visit_us".tr,
                color: context.outlineButtonColor,
                size: 12,
                fontWeight: FontWeight.w700,
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: context.outlineButtonColor,
                  width: 1.sp,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12.0),
              ),
            ),
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  void _visitUs(BuildContext context) {
    // final infoSupplier = getIt<MainHomeBloc>().state.infoSupplier;
    // if ((infoSupplier?.locationUrl ?? '').isEmpty) return;
    //
    // _trackUserInteraction('visit_us_clicked');
    // launchURL(
    //   url: infoSupplier?.locationUrl ??
    //       'https://www.google.com/maps/place/Thrivve+%7C+%D8%AB%D8%B1%D8%A7%D9%8A%D9%81%E2%80%A[…]Pu8ASoASAFQAw%3D%3D&skid=028402d9-8925-4ee9-b319-d786b59ada74',
    //   mode: LaunchMode.externalApplication,
    // );

    showMapThrriveSheet(context: context);
  }
}
