import 'package:get/get.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/data_sources/rent_checkout_remote_data_source.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/data_sources/rent_checkout_remote_data_source_impl.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/repositories/rent_checkout_repository_impl.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/repositories/rent_checkout_repository.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/get_checkout_rent_a_car_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/get_pickup_calendar_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/submit_checkout_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/submit_kyc_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/manager/rent_car_checkout_controller.dart';

class RentCarCheckoutBinding extends Bindings {
  @override
  void dependencies() {
    // Rent Checkout
    Get.lazyPut<RentCheckoutRemoteDataSource>(
      () => RentCheckoutRemoteDataSourceImpl(
        apiClient: getIt(),
      ),
    );
    Get.lazyPut<RentCheckoutRepository>(
      () => RentCheckoutRepositoryImpl(
        remoteDataSource: Get.find<RentCheckoutRemoteDataSource>(),
        networkInfo: getIt(),
      ),
    );
    Get.lazyPut<GetPickupCalendarUseCase>(
      () => GetPickupCalendarUseCase(
          repository: Get.find<RentCheckoutRepository>()),
    );

    Get.lazyPut<GetCheckoutRentACarUseCase>(
      () => GetCheckoutRentACarUseCase(
          repository: Get.find<RentCheckoutRepository>()),
    );

    Get.lazyPut<SubmitKYCUseCase>(
      () => SubmitKYCUseCase(
        repository: Get.find<RentCheckoutRepository>(),
      ),
    );
    Get.lazyPut<SubmitCheckoutUseCase>(
      () => SubmitCheckoutUseCase(Get.find()),
    );
    Get.lazyPut<RentCarCheckoutController>(
      () => RentCarCheckoutController(
        paymentMethodsUseCase: getIt(),
        checkoutPaymentUseCase: getIt(),
        listSavedCardsUseCase: getIt(),
        getPickupCalendarUseCase: Get.find<GetPickupCalendarUseCase>(),
        getCheckoutRentACarUseCase: Get.find<GetCheckoutRentACarUseCase>(),
        submitKYCUseCase: Get.find<SubmitKYCUseCase>(),
        submitCheckoutUseCase: Get.find<SubmitCheckoutUseCase>(),
        iPageLoadingDialog: Get.find<IPageLoadingDialog>(),
      ),
    );
  }
}
