import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/show_bottom_sheet_input.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/widgets/bottomsheets/price_breakdown_bottomsheet.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/manager/rent_car_checkout_controller.dart';

class PriceSection extends StatelessWidget {
  const PriceSection({
    super.key,
    required this.rentCarCheckoutController,
  });
  final RentCarCheckoutController rentCarCheckoutController;
  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final priceBreakdown = rentCarCheckoutController
        .checkoutResponse?.costBreakdown?.priceBreakdown;
    final ontTimeCharges = rentCarCheckoutController
        .checkoutResponse?.costBreakdown?.enrolmentFees;
    final monthlyFees =
        rentCarCheckoutController.checkoutResponse?.costBreakdown?.monthlyFees;
    final price =
        rentCarCheckoutController.checkoutResponse?.costBreakdown?.total;
    return Container(
      padding: EdgeInsetsDirectional.only(
        start: 16.w,
        end: 16.w,
      ),
      decoration: BoxDecoration(
        color: context.backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (ontTimeCharges != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CustomTextWidget(
                      title: ontTimeCharges.description ?? '',
                      size: 11,
                      color: isDarkMode
                          ? Colors.white
                          : context.black.withValues(alpha: 0.6),
                    ),
                    SizedBox(width: 8.w),
                    CustomTextWidget(
                      title:
                          '${ontTimeCharges.currency ?? 'sar'} ${ontTimeCharges.discountPrice ?? '0.0'}',
                      decoration: TextDecoration.lineThrough,
                      size: 11,
                      color: isDarkMode
                          ? Colors.white
                          : context.black.withValues(alpha: 0.6),
                    ),
                  ],
                ),
                CustomTextWidget(
                  title:
                      '${ontTimeCharges.currency ?? 'sar'} ${ontTimeCharges.totalPrice ?? '0.0'}',
                  size: 12,
                  color: isDarkMode
                      ? Colors.white
                      : context.black.withValues(alpha: 0.6),
                ),
              ],
            ),
          SizedBox(height: 10.h),
          if (monthlyFees != null)
            Column(
              children: [
                SizedBox(height: 10.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CustomTextWidget(
                          title: monthlyFees.description ?? '',
                          size: 11,
                          color: isDarkMode
                              ? Colors.white
                              : context.black.withValues(alpha: 0.6),
                        ),
                        SizedBox(width: 8.w),
                        CustomTextWidget(
                          title:
                              '${monthlyFees.currency ?? 'sar'} ${monthlyFees.discountPrice ?? '0.0'}',
                          decoration: TextDecoration.lineThrough,
                          size: 11,
                          color: isDarkMode
                              ? Colors.white
                              : context.black.withValues(alpha: 0.6),
                        ),
                      ],
                    ),
                    CustomTextWidget(
                      title:
                          '${monthlyFees.currency ?? 'sar'} ${monthlyFees.totalPrice ?? '0.0'}',
                      size: 12,
                      color: isDarkMode
                          ? Colors.white
                          : context.black.withValues(alpha: 0.6),
                    ),
                  ],
                ),
              ],
            ),
          SizedBox(height: 10.h),
          Divider(
            thickness: 1.h,
            height: 1.h,
            color: context.black.withValues(alpha: 0.1),
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomTextWidget(
                title: price?.description ?? '',
                size: 15,
                fontWeight: FontWeight.w600,
                color: context.black,
              ),
              CustomFormattedText(
                style: TextStyle(
                  fontSize: 11.sp,
                  fontWeight: FontWeight.w400,
                  color: context.black,
                ),
                strongStyle: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w600,
                  color: context.black,
                ),
                textAlign: WrapAlignment.center,
                text:
                    '**${price?.currency ?? 'sar'} ${price?.totalPrice ?? '0.0'}**${price?.period != null ? ' / ' : ''}${price?.period ?? ''}',
              ),
            ],
          ),
          SizedBox(height: 20.h),
          if ((priceBreakdown?.title ?? '').isNotEmpty)
            GestureDetector(
              onTap: _onClickPriceBreakDown,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    priceBreakdown?.title ?? '',
                    style: TextStyle(
                      decoration: TextDecoration.underline,
                      decorationStyle: TextDecorationStyle.dotted,
                      decorationColor:
                          isDarkMode ? Colors.white : context.appPrimaryColor,
                      decorationThickness: 2.sp,
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w700,
                      color:
                          isDarkMode ? Colors.white : context.appPrimaryColor,
                    ),
                  ),
                  CustomTextWidget(
                    title: priceBreakdown?.subTitle ?? '',
                    size: 11,
                    color: isDarkMode
                        ? Colors.white
                        : context.black.withValues(alpha: 0.6),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _onClickPriceBreakDown() {
    final sheet = PriceBreakDownButtomsheet(
      rentCarCheckoutController.checkoutResponse?.costBreakdown,
    );
    final input = ShowBottomSheetInput(
      sheet,
      isScrollControlled: true,
    );
    Get.find<IShowBottomSheetHelper>().showBottomSheet(input);
  }
}
