import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/step_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/step_rent_car_enum.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/widgets/price_selection_widget.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/widgets/subscription_overview_card.dart';
import 'package:thrivve/features/rent_a_car/widgets/section_support.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/back_icon.dart';
import 'package:thrivve/generated/assets.dart';

import '../manager/rent_car_checkout_controller.dart';
import '../widgets/checkout_step_tile.dart';

class RentCarCheckoutPage extends GetView<RentCarCheckoutController> {
  const RentCarCheckoutPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return _RentCarCheckoutPageStateful();
  }
}

class _RentCarCheckoutPageStateful extends StatefulWidget {
  const _RentCarCheckoutPageStateful({super.key});

  @override
  State<_RentCarCheckoutPageStateful> createState() =>
      _RentCarCheckoutPageStatefulState();
}

class _RentCarCheckoutPageStatefulState
    extends State<_RentCarCheckoutPageStateful> {
  Timer? _abandonmentTimer;
  final RentCarCheckoutController controller =
      Get.find<RentCarCheckoutController>();

  void _trackUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'Checkout: $action',
      category: 'user_interaction',
      data: data,
    );
  }

  @override
  void initState() {
    super.initState();
    _startAbandonmentTimer();
  }

  @override
  void dispose() {
    _abandonmentTimer?.cancel();
    super.dispose();
  }

  void _startAbandonmentTimer() {
    _abandonmentTimer = Timer(const Duration(seconds: 30), () {
      SentryService.instance.captureMessage(
        'Possible checkout abandonment',
        level: SentryLevel.info,
        data: {
          'page': 'rent_car_checkout',
          'abandonment_time': '30s',
          'pickup_status': controller.pickUpDateStatus.value.toString(),
          'payment_status': controller.paymentMethodStatus.value.toString(),
          'person_status': controller.personalDetailsStatus.value.toString(),
        },
      );
    });
  }

  void _onClickBack() {
    _abandonmentTimer?.cancel();
    _trackUserInteraction('back_button_clicked', data: {
      'pickup_status': controller.pickUpDateStatus.value.toString(),
      'payment_status': controller.paymentMethodStatus.value.toString(),
      'person_status': controller.personalDetailsStatus.value.toString(),
      'can_pop': Navigator.of(Get.context!).canPop(),
    });

    if (Navigator.of(Get.context!).canPop()) {
      Get.back();
    } else {
      reinitializeBloc();
      Get.offAllNamed(AppRoutes.homePage);
    }
  }

  void _onClickCheckout(BuildContext context) {
    _abandonmentTimer?.cancel();
    _trackUserInteraction('checkout_button_click');
    controller.doCheckout();
  }

  void _onPopInvoked(didPop, dynamic) async {
    if (!didPop) {
      _abandonmentTimer?.cancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: _onPopInvoked,
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: NestedScrollView(
            headerSliverBuilder: (BuildContext context, bool innerBoxScrolled) {
              return <Widget>[
                createSilverAppBar(context),
              ];
            },
            body: _body(context, controller),
          ),
        ),
      ),
    );
  }

  SliverAppBar createSilverAppBar(BuildContext context) {
    return SliverAppBar(
      leadingWidth: 60.w,
      leading: BackIcon(
        removeSpacing: false,
        onClickBack: _onClickBack,
      ),
      centerTitle: true,
      automaticallyImplyLeading: false,
      expandedHeight: 120.h,
      floating: false,
      forceElevated: true,
      elevation: 0,
      excludeHeaderSemantics: true,
      pinned: true,
      surfaceTintColor: Colors.transparent,
      flexibleSpace: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final bool isCollapsed = constraints.maxHeight <= kToolbarHeight + 10;
          return FlexibleSpaceBar(
            background: Container(
              color: context.backgroundColor,
            ),
            expandedTitleScale: 1,
            titlePadding: isCollapsed
                ? EdgeInsetsDirectional.zero
                : EdgeInsetsDirectional.only(
                    start: 16.w,
                    end: 16.w,
                    top: 40,
                  ),
            centerTitle: isCollapsed,
            title: Align(
              alignment: isCollapsed
                  ? Alignment.center
                  : (Directionality.of(context) == TextDirection.rtl
                      ? Alignment.centerRight
                      : Alignment.centerLeft),
              child: CustomTextWidget(
                paddingStart: 0,
                fontWeight: FontWeight.bold,
                title: 'checkout_and_order'.tr,
                size: 16,
              ),
            ),
            collapseMode: CollapseMode.parallax,
          );
        },
      ),
    );
  }

  Widget _body(BuildContext context, RentCarCheckoutController controller) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            physics: ClampingScrollPhysics(),
            child: Container(
              color: context.backgroundColor,
              child: Obx(() => controller.isLoadingCheckout
                  ? _buildShimmerLoading(context)
                  : Column(
                      children: [
                        SizedBox(height: 15.h),
                        _cardSubscription(context, controller),
                        SizedBox(height: 50.h),
                        _sectionStep(context, controller),
                        SizedBox(height: 56.h),
                        _divider(4.h, context),
                        SizedBox(height: 10.h),
                        PriceSection(
                          rentCarCheckoutController: controller,
                        ),
                        SizedBox(height: 20.h),
                        _checkoutButton(context, controller),
                        SizedBox(height: 40.h),
                        _needHelpWidget(),
                      ],
                    )),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerLoading(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: context.borderAddBranch,
      highlightColor: context.appBackgroundColor,
      child: Column(
        children: [
          SizedBox(height: 20.h),
          // Subscription card shimmer
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.borderAddBranch,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 24.w,
                      height: 24.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Container(
                      width: 120.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                Container(
                  width: double.infinity,
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: context.borderAddBranch,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 60.h),
          // Steps shimmer
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: List.generate(
                3,
                (index) => Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: Row(
                    children: [
                      Container(
                        width: 24.w,
                        height: 24.h,
                        decoration: BoxDecoration(
                          color: context.borderAddBranch,
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 140.w,
                              height: 16.h,
                              decoration: BoxDecoration(
                                color: context.borderAddBranch,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              width: 200.w,
                              height: 14.h,
                              decoration: BoxDecoration(
                                color: context.borderAddBranch,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 24.w,
                        height: 24.h,
                        decoration: BoxDecoration(
                          color: context.borderAddBranch,
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 60.h),
          // Divider
          Container(
            height: 4.h,
            color: context.borderAddBranch,
          ),
          SizedBox(height: 20.h),
          // Price section shimmer
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.borderAddBranch,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 100.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    Container(
                      width: 80.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 120.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    Container(
                      width: 60.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 140.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    Container(
                      width: 70.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: context.borderAddBranch,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          // Checkout button shimmer
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            height: 50.h,
            decoration: BoxDecoration(
              color: context.borderAddBranch,
              borderRadius: BorderRadius.circular(16.r),
            ),
          ),
          SizedBox(height: 40.h),
          // Support section shimmer
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.borderAddBranch,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Container(
                  width: 24.w,
                  height: 24.h,
                  decoration: BoxDecoration(
                    color: context.borderAddBranch,
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 120.w,
                        height: 16.h,
                        decoration: BoxDecoration(
                          color: context.borderAddBranch,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        width: 200.w,
                        height: 14.h,
                        decoration: BoxDecoration(
                          color: context.borderAddBranch,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _cardSubscription(
      BuildContext context, RentCarCheckoutController controller) {
    return Obx(
      () => SubscriptionOverviewCard(
        checkoutResponseEntity: controller.checkoutResponse,
        expanded: controller.isSubscriptionExpanded.value,
        onToggle: controller.toggleSubscriptionExpanded,
        controller: controller,
      ),
    );
  }

  Widget _sectionStep(
      BuildContext context, RentCarCheckoutController controller) {
    return Column(
      children: [
        Obx(
          () => Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CustomTextWidget(
                paddingStart: 16.w,
                title: controller.checkoutResponse?.remainingSteps ?? '',
                color: context.black,
                size: 17,
                fontWeight: FontWeight.w600,
              ),
            ],
          ),
        ),
        SizedBox(height: 20.h),
        Column(
          children:
              (controller.checkoutResponse?.steps ?? []).map((StepEntity item) {
            return Obx(
              () => CheckoutStepTile(
                icon: item.status == CheckoutStepStatus.done
                    ? Assets.rentACarCheckedIcon
                    : item.icon ?? Assets.rentACarPersonalInfo,
                title: item.title ?? '',
                subtitle: item.subTitle ?? '',
                status: item.step == StepRentCarEnum.personal_details
                    ? controller.personalDetailsStatus.value
                    : item.step == StepRentCarEnum.pickup_date
                        ? controller.pickUpDateStatus.value
                        : controller.paymentMethodStatus.value,
                errorMessage: 'fill_out_your_information'.tr,
                onTap: () {
                  _trackUserInteraction('step_tile_click',
                      data: {'step': item.step?.name.toString()});
                  controller.onClickStep(item);
                },
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _divider(double value, BuildContext context) {
    return Divider(
      thickness: value,
      height: value,
      color: context.black.withValues(alpha: 0.08),
    );
  }

  Widget _needHelpWidget() {
    return SupportSection();
  }

  Widget _checkoutButton(
      BuildContext context, RentCarCheckoutController controller) {
    return Container(
      color: context.backgroundColor,
      padding: EdgeInsetsDirectional.only(top: 15.h),
      child: Obx(
        () => CustomButton(
          isLoading: controller.isLoadingCheckoutPaymob,
          margin: EdgeInsetsDirectional.only(
            start: 16.w,
            end: 16.w,
          ),
          key: ValueKey('checkout'),
          enabled: true,
          text: 'pay_now'.tr,
          onPressed: () => _onClickCheckout(context),
          colorText: Colors.white,
        ),
      ),
    );
  }
}
