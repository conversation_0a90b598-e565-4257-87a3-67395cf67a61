import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/upload_image/custom_file_upload_widget.dart';
import 'package:thrivve/core/upload_image/file_upload_controller.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_input_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/kyc_type_enum.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/step_rent_car_enum.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/manager/rent_car_checkout_controller.dart';

class RentPersonalDetailsPage extends GetView<RentCarCheckoutController> {
  const RentPersonalDetailsPage({super.key});

  void _trackUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'User interaction: $action',
      category: 'user_interaction',
      data: data,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: _body(context, controller),
      ),
    );
  }

  Widget _body(BuildContext context, controller) {
    return Column(
      children: [
        Expanded(
          child: Column(
            children: [
              _buildCloseButton(context),
              SizedBox(height: 40.h),
              _title(context),
              SizedBox(height: 40.h),
              _sectionUploadIdentity(context, controller),
              SizedBox(height: 40.h),
              _sectionUploadLicense(context, controller),
            ],
          ),
        ),
        _button(context, controller),
      ],
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(width: 16.w),
        Container(
          height: 40.h,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.appBackgroundColor,
          ),
          child: InkWell(
            onTap: () {
              _trackUserInteraction(
                'closed_button_clicked',
                data: {
                  'personal_status':
                      controller.personalDetailsStatus.value.toString(),
                  'can_pop': Navigator.of(Get.context!).canPop(),
                },
              );
              Get.back();
            },
            child: Icon(
              Icons.close,
              color: context.black,
              size: 20.w,
            ),
          ),
        ),
      ],
    );
  }

  Widget _title(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CustomTextWidget(
          title: 'rent_personal_details'.tr,
          color: context.black,
          size: 22,
          paddingStart: 16.w,
          fontWeight: FontWeight.w600,
        )
      ],
    );
  }

  Widget _sectionUploadIdentity(
      BuildContext context, RentCarCheckoutController controller) {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 16.w, end: 16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomTextWidget(
            title: 'we_need_upload_kyc'.tr,
            color: context.black,
            size: 12,
            fontWeight: FontWeight.w700,
          ),
          SizedBox(height: 12.h),
          Obx(
            () => CustomFileUploadWidget(
              initialFileUrl: controller.identityImage.value,
              height: 140.h,
              controllerId: 'rent_identity_personal_details',
              title: 'press_to_add_id'.tr,
              description: 'rent_formats_file'.tr,
              onFileDeleted: () {
                controller.deleteIdentityPersonal();
              },
              onFileUploaded: (filePath) {
                _trackUserInteraction('identity_document_upload', data: {
                  'file_name': filePath?.split('/').last,
                  'file_size': filePath?.length,
                });
                controller.setIdentityPersonal(filePath);
              },
              onFileSelected: (filePath) {
                Get.back();
                if (filePath?.isNotEmpty == true) {
                  if (filePath != null) {
                    Get.find<FileUploadController>(
                            tag: 'rent_identity_personal_details')
                        .uploadFile(filePath);
                  }
                }
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _sectionUploadLicense(
      BuildContext context, RentCarCheckoutController controller) {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 16.w, end: 16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(
            () => CustomFileUploadWidget(
              initialFileUrl: controller.driverLicenseImage.value,
              height: 140.h,
              controllerId: 'rent_hint_driver_license',
              title: 'press_to_add_lisnce'.tr,
              description: 'rent_formats_file'.tr,
              onFileDeleted: () {
                controller.deleteDriverLicense();
              },
              onFileUploaded: (filePath) {
                _trackUserInteraction('driver_license_upload', data: {
                  'file_name': filePath?.split('/').last,
                  'file_size': filePath?.length,
                });
                controller.setDriverLicense(filePath);
              },
              onFileSelected: (filePath) {
                Get.back();
                if (filePath?.isNotEmpty == true) {
                  if (filePath != null) {
                    Get.find<FileUploadController>(
                            tag: 'rent_hint_driver_license')
                        .uploadFile(filePath);
                  }
                }
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _button(BuildContext context, RentCarCheckoutController controller) {
    return Obx(
      () => Column(
        children: [
          Divider(
            color: context.black.withValues(alpha: 0.08),
            thickness: 4.h,
            height: 4.h,
          ),
          SizedBox(height: 16.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: CustomButton(
              isLoading: controller.isLoadingKYC,
              key: ValueKey('save_id_changes_btn'),
              enabled: (controller.driverLicenseImage.value ?? '').isNotEmpty &&
                  (controller.identityImage.value ?? '').isNotEmpty,
              text: 'confirm'.tr,
              onPressed: () {
                _trackUserInteraction('confirm_personal_details_click');
                final input = KYCInputEntity(
                  type: KYCTypeEnum.kyc,
                  identityDocumentUrl: controller.identityImage.value ?? '',
                  drivingLicenceUrl: controller.driverLicenseImage.value ?? '',
                );
                controller.submitKYC(input, StepRentCarEnum.personal_details);
              },
              colorText: Colors.white,
            ),
          ),
          SizedBox(height: 5.h),
        ],
      ),
    );
  }
}
