import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_input_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_response_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/repositories/rent_checkout_repository.dart';

class SubmitKYCUseCase implements UseCase<KYCResponseEntity?, KYCInputEntity> {
  final RentCheckoutRepository repository;

  SubmitKYCUseCase({required this.repository});

  @override
  Future<Either<Failure, KYCResponseEntity?>> call(KYCInputEntity params) async {
    return await repository.submitKYC(params);
  }
} 