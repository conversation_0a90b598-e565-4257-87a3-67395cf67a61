class VehicleEntity {
  final List<String>? notes;
  final String? period;
  final String? contractType;
  final String? priceNote;
  final String? manufactureYear;
  final double? monthlyPrice;
  final int? id;
  final String? title;
  final String? milage;
  final int? supplierId;
  final String? mainImage;
  final String? currency;
  final String? monthlyPriceTitle;
  final double? rate;
  final int? vehicleId;
  final String? dailyPriceBetween;
  final bool? isAvailable;
  final bool? fixedFleet;

  const VehicleEntity({
    this.notes,
    this.period,
    this.contractType,
    this.priceNote,
    this.manufactureYear,
    this.monthlyPrice,
    this.id,
    this.title,
    this.milage,
    this.supplierId,
    this.mainImage,
    this.currency,
    this.monthlyPriceTitle,
    this.rate,
    this.vehicleId,
    this.dailyPriceBetween,
    this.isAvailable,
    this.fixedFleet,
  });
} 