import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/add_ons_entity.dart';

import 'commitment_months_entity.dart';
import 'insurance_entity.dart';
import 'milage_entity.dart';
import 'package_entity.dart';
import 'vehicle_object_entity.dart';

class SubscriptionEntity {
  final int? applicationId;
  final VehicleObjectEntity? vehicleObject;
  final PackageEntity? package;
  final InsuranceEntity? insurance;
  final MilageEntity? milage;
  final CommitmentMonthsEntity? commitmentMonths;
  final AddOnsEntity? addOns;

  const SubscriptionEntity({
    this.applicationId,
    this.vehicleObject,
    this.package,
    this.insurance,
    this.milage,
    this.commitmentMonths,
    this.addOns,
  });
}
