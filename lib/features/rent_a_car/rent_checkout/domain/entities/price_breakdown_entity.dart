import 'package:equatable/equatable.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/price_item_entity.dart';

class PriceBreakdownEntity extends Equatable {
  final PriceItemEntity? oneTimeTotal;
  final List<PriceItemEntity>? monthlyCharges;
  final PriceItemEntity? monthlyChargesTotal;
  final String? subTitle;
  final List<PriceItemEntity>? oneTimeChanges;
  final String? title;

  const PriceBreakdownEntity({
    this.oneTimeTotal,
    this.monthlyCharges,
    this.monthlyChargesTotal,
    this.subTitle,
    this.oneTimeChanges,
    this.title,
  });

  @override
  List<Object?> get props => [
        oneTimeTotal,
        monthlyCharges,
        monthlyChargesTotal,
        subTitle,
        oneTimeChanges,
        title,
      ];
}
