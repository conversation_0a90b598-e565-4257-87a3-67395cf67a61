import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/calendar_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/checkout_paymob_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/checkout_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/kyc_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/kyc_type_enum.dart';

abstract class RentCheckoutRemoteDataSource {
  Future<CalendarResponseModel?> getPickupCalendar();
  Future<CheckoutResponseModel?> getCheckoutRentACar();
  Future<KYCResponseModel?> submitKYC(
      Map<String, dynamic> kycData, KYCTypeEnum type);
  Future<CheckoutResponsePaymobModel?> submitCheckout();
}
