import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/data_sources/rent_checkout_remote_data_source.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/calendar_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/checkout_paymob_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/checkout_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/kyc_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/kyc_type_enum.dart';

class RentCheckoutRemoteDataSourceImpl implements RentCheckoutRemoteDataSource {
  final ApiClient apiClient;

  RentCheckoutRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<CalendarResponseModel?> getPickupCalendar() async {
    final response = await apiClient.request<CalendarResponseModel>(
      endpoint: ApiSettings.rentCarPickupCalendar,
      method: RequestType.get,
      fromJson: (json) => CalendarResponseModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<CheckoutResponseModel?> getCheckoutRentACar() async {
    final response = await apiClient.request<CheckoutResponseModel>(
      endpoint: ApiSettings.rentCarCheckoutSteps,
      method: RequestType.get,
      fromJson: (json) => CheckoutResponseModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<KYCResponseModel?> submitKYC(
      Map<String, dynamic> kycData, KYCTypeEnum type) async {
    final endpoint = '${ApiSettings.rentCarKYC}/${type.endpoint}';
    final response = await apiClient.request<KYCResponseModel>(
      endpoint: endpoint,
      method: RequestType.post,
      data: kycData,
      fromJson: (json) => KYCResponseModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<CheckoutResponsePaymobModel?> submitCheckout() async {
    final response = await apiClient.request<CheckoutResponsePaymobModel>(
      endpoint:
          '${ApiSettings.thrivveBaseUrl}product/api/v1/rent_car_application/checkout',
      method: RequestType.post,
      fromJson: (json) => CheckoutResponsePaymobModel.fromJson(json),
    );
    return response.data;
  }
}
