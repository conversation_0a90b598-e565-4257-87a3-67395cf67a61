import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/calendar_response_model.dart';

import '../../domain/entities/calendar_month_entity.dart';
import 'calendar_day_mapper.dart';

extension CalendarMonthMapper on CalendarMonthModel {
  CalendarMonthEntity toEntity() {
    return CalendarMonthEntity(
      year: year,
      month: month,
      days: days?.map((day) => day.toEntity()).toList(),
    );
  }
}

extension CalendarMonthEntityMapper on CalendarMonthEntity {
  CalendarMonthModel toModel() {
    return CalendarMonthModel(
      year: year,
      month: month,
      days: days?.map((day) => day.toModel()).toList(),
    );
  }
}
