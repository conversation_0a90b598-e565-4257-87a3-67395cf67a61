import '../../domain/entities/price_breakdown_entity.dart';
import '../models/checkout_response_model.dart';
import 'price_item_mapper.dart';

extension PriceBreakdownMapper on PriceBreakdownModel {
  PriceBreakdownEntity toEntity() {
    return PriceBreakdownEntity(
      oneTimeTotal: oneTimeTotal?.toEntity(),
      monthlyCharges: monthlyCharges?.map((charge) => charge.toEntity()).toList(),
      monthlyChargesTotal: monthlyChargesTotal?.toEntity(),
      subTitle: subTitle,
      oneTimeChanges: oneTimeChanges?.map((change) => change.toEntity()).toList(),
      title: title,
    );
  }
}

extension PriceBreakdownEntityMapper on PriceBreakdownEntity {
  PriceBreakdownModel toModel() {
    return PriceBreakdownModel(
      oneTimeTotal: oneTimeTotal?.toModel(),
      monthlyCharges: monthlyCharges?.map((charge) => charge.toModel()).toList(),
      monthlyChargesTotal: monthlyChargesTotal?.toModel(),
      subTitle: subTitle,
      oneTimeChanges: oneTimeChanges?.map((change) => change.toModel()).toList(),
      title: title,
    );
  }
}
