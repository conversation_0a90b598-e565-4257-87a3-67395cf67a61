import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/calendar_response_model.dart';

import '../../domain/entities/calendar_day_entity.dart';

extension CalendarDayMapper on CalendarDayModel {
  CalendarDayEntity toEntity() {
    return CalendarDayEntity(
        date: date,
        day: day,
        isDisabled: isDisabled,
        isCurrent: isCurrent,
        dayValue: dayValue);
  }
}

extension CalendarDayEntityMapper on CalendarDayEntity {
  CalendarDayModel toModel() {
    return CalendarDayModel(
      date: date,
      day: day,
      isDisabled: isDisabled,
      isCurrent: isCurrent,
    );
  }
}
