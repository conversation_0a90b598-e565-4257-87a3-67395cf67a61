class RentApplicationProgressModel {
  final String? title;
  final String? subTitle;
  final String? actions;
  final String? status;
  final String? body;

  const RentApplicationProgressModel({
    this.title,
    this.subTitle,
    this.actions,
    this.status,
    this.body,
  });

  factory RentApplicationProgressModel.fromJson(Map<String, dynamic> json) {
    return RentApplicationProgressModel(
      title: json['title'] as String?,
      subTitle: json['sub_title'] as String?,
      actions: json['actions'] as String?,
      status: json['status'] as String?,
      body: json['body'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'sub_title': subTitle,
      'actions': actions,
      'status': status,
      'body': body,
    };
  }
} 