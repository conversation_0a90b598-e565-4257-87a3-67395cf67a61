import 'package:thrivve/features/rent_a_car/car_details/data/models/price_breakdown_response_model.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/models/step_data_model.dart';

class CheckoutResponseModel {
  final SubscriptionModel? subSubscription;
  final List<StepModel>? steps;
  final String? remainingSteps;
  final bool? allowCheckOut;
  final RentCarPriceBreakdownResponseModel? costBreakdown;

  const CheckoutResponseModel({
    this.subSubscription,
    this.steps,
    this.remainingSteps,
    this.allowCheckOut,
    this.costBreakdown,
  });

  factory CheckoutResponseModel.fromJson(Map<String, dynamic> json) {
    return CheckoutResponseModel(
      subSubscription: json['sub_scription'] != null
          ? SubscriptionModel.fromJson(json['sub_scription'])
          : null,
      steps: json['steps'] != null
          ? List<StepModel>.from(
              json['steps'].map((x) => StepModel.fromJson(x)))
          : null,
      remainingSteps: json['remaining_steps'],
      allowCheckOut: json['allow_check_out'],
      costBreakdown: json['cost_breakdown'] != null
          ? RentCarPriceBreakdownResponseModel.fromJson(json['cost_breakdown'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sub_scription': subSubscription?.toJson(),
      'steps': steps?.map((x) => x.toJson()).toList(),
      'remaining_steps': remainingSteps,
      'allow_check_out': allowCheckOut,
      'cost_breakdown': costBreakdown?.toJson(),
    };
  }
}

class SubscriptionModel {
  final int? applicationId;
  final VehicleObjectModel? vehicleObject;
  final PackageModel? package;
  final InsuranceModel? insurance;
  final MilageModel? milage;
  final AddOnsModel? addOns;
  final CommitmentMonthsModel? commitmentMonths;

  const SubscriptionModel({
    this.applicationId,
    this.vehicleObject,
    this.package,
    this.insurance,
    this.milage,
    this.commitmentMonths,
    this.addOns,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      applicationId: json['application_id'],
      vehicleObject: json['vehicle_object'] != null
          ? VehicleObjectModel.fromJson(json['vehicle_object'])
          : null,
      package: json['package'] != null
          ? PackageModel.fromJson(json['package'])
          : null,
      insurance: json['insurance'] != null
          ? InsuranceModel.fromJson(json['insurance'])
          : null,
      milage:
          json['milage'] != null ? MilageModel.fromJson(json['milage']) : null,
      addOns: json['add_ons'] != null
          ? AddOnsModel.fromJson(json['add_ons'])
          : null,
      commitmentMonths: json['commitment_months'] != null
          ? CommitmentMonthsModel.fromJson(json['commitment_months'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'application_id': applicationId,
      'vehicle_object': vehicleObject?.toJson(),
      'package': package?.toJson(),
      'insurance': insurance?.toJson(),
      'milage': milage?.toJson(),
      'commitment_months': commitmentMonths?.toJson(),
    };
  }
}

class VehicleObjectModel {
  final String? title;
  final MainImageModel? mainImage;
  final String? subTitle;

  const VehicleObjectModel({
    this.title,
    this.mainImage,
    this.subTitle,
  });

  factory VehicleObjectModel.fromJson(Map<String, dynamic> json) {
    return VehicleObjectModel(
      title: json['title'],
      mainImage: json['main_image'] != null
          ? MainImageModel.fromJson(json['main_image'])
          : null,
      subTitle: json['sub_title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'main_image': mainImage?.toJson(),
      'sub_title': subTitle,
    };
  }
}

class MainImageModel {
  final String? url;

  const MainImageModel({
    this.url,
  });

  factory MainImageModel.fromJson(Map<String, dynamic> json) {
    return MainImageModel(
      url: json['url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
    };
  }
}

class PackageModel {
  final String? icon;
  final String? title;

  const PackageModel({
    this.icon,
    this.title,
  });

  factory PackageModel.fromJson(Map<String, dynamic> json) {
    return PackageModel(
      icon: json['icon'],
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'icon': icon,
      'title': title,
    };
  }
}

class InsuranceModel {
  final String? icon;
  final String? title;

  const InsuranceModel({
    this.icon,
    this.title,
  });

  factory InsuranceModel.fromJson(Map<String, dynamic> json) {
    return InsuranceModel(
      icon: json['icon'],
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'icon': icon,
      'title': title,
    };
  }
}

class AddOnsModel {
  final String? icon;
  final String? title;

  const AddOnsModel({
    this.icon,
    this.title,
  });

  factory AddOnsModel.fromJson(Map<String, dynamic> json) {
    return AddOnsModel(
      icon: json['icon'],
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'icon': icon,
      'title': title,
    };
  }
}

class MilageModel {
  final String? icon;
  final String? title;

  const MilageModel({
    this.icon,
    this.title,
  });

  factory MilageModel.fromJson(Map<String, dynamic> json) {
    return MilageModel(
      icon: json['icon'],
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'icon': icon,
      'title': title,
    };
  }
}

class CommitmentMonthsModel {
  final String? iconn;
  final String? description;

  const CommitmentMonthsModel({
    this.iconn,
    this.description,
  });

  factory CommitmentMonthsModel.fromJson(Map<String, dynamic> json) {
    return CommitmentMonthsModel(
      iconn: json['iconn'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iconn': iconn,
      'description': description,
    };
  }
}

class StepModel {
  final String? title;
  final String? subTitle;
  final String? icon;
  final bool? isDone;
  final String? step;
  final StepDataModel? data;

  const StepModel({
    this.title,
    this.subTitle,
    this.icon,
    this.isDone,
    this.step,
    this.data,
  });

  factory StepModel.fromJson(Map<String, dynamic> json) {
    return StepModel(
      title: json['title'],
      subTitle: json['sub_title'],
      icon: json['icon'],
      isDone: json['is_done'],
      step: json['step'],
      data: json['data'] != null ? StepDataModel.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'sub_title': subTitle,
      'icon': icon,
      'is_done': isDone,
      'step': step,
      'data': data?.toJson(),
    };
  }
}

class CostBreakdownModel {
  final ExtraNoteModel? extraNote;
  final PriceItemModel? price;
  final PriceBreakdownModel? priceBreakdown;
  final PriceItemModel? oneTimeCharges;
  final PriceItemModel? enrolmentFee;

  const CostBreakdownModel({
    this.extraNote,
    this.price,
    this.priceBreakdown,
    this.oneTimeCharges,
    this.enrolmentFee,
  });

  factory CostBreakdownModel.fromJson(Map<String, dynamic> json) {
    return CostBreakdownModel(
      extraNote: json['extra_note'] != null
          ? ExtraNoteModel.fromJson(json['extra_note'])
          : null,
      price:
          json['price'] != null ? PriceItemModel.fromJson(json['price']) : null,
      priceBreakdown: json['price_breakdown'] != null
          ? PriceBreakdownModel.fromJson(json['price_breakdown'])
          : null,
      oneTimeCharges: json['one_time_charges'] != null
          ? PriceItemModel.fromJson(json['one_time_charges'])
          : null,
      enrolmentFee: json['enrolment_fees'] != null
          ? PriceItemModel.fromJson(json['enrolment_fees'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'extra_note': extraNote?.toJson(),
      'price': price?.toJson(),
      'price_breakdown': priceBreakdown?.toJson(),
      'one_time_charges': oneTimeCharges?.toJson(),
    };
  }
}

class PriceBreakdownModel {
  final PriceItemModel? oneTimeTotal;
  final List<PriceItemModel>? monthlyCharges;
  final PriceItemModel? monthlyChargesTotal;
  final String? subTitle;
  final List<PriceItemModel>? oneTimeChanges;
  final String? title;

  const PriceBreakdownModel({
    this.oneTimeTotal,
    this.monthlyCharges,
    this.monthlyChargesTotal,
    this.subTitle,
    this.oneTimeChanges,
    this.title,
  });

  factory PriceBreakdownModel.fromJson(Map<String, dynamic> json) {
    return PriceBreakdownModel(
      oneTimeTotal: json['one_time_total'] != null
          ? PriceItemModel.fromJson(json['one_time_total'])
          : null,
      monthlyCharges: json['monthly_charges'] != null
          ? List<PriceItemModel>.from(
              json['monthly_charges'].map((x) => PriceItemModel.fromJson(x)))
          : null,
      monthlyChargesTotal: json['monthly_charges_total'] != null
          ? PriceItemModel.fromJson(json['monthly_charges_total'])
          : null,
      subTitle: json['sub_title'],
      oneTimeChanges: json['one_time_changes'] != null
          ? List<PriceItemModel>.from(
              json['one_time_changes'].map((x) => PriceItemModel.fromJson(x)))
          : null,
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'one_time_total': oneTimeTotal?.toJson(),
      'monthly_charges': monthlyCharges?.map((x) => x.toJson()).toList(),
      'monthly_charges_total': monthlyChargesTotal?.toJson(),
      'sub_title': subTitle,
      'one_time_changes': oneTimeChanges?.map((x) => x.toJson()).toList(),
      'title': title,
    };
  }
}

class PriceItemModel {
  final double? price;
  final String? currency;
  final String? description;
  final double? discountPrice;
  final double? totalPrice;

  const PriceItemModel({
    this.price,
    this.currency,
    this.description,
    this.discountPrice,
    this.totalPrice,
  });

  factory PriceItemModel.fromJson(Map<String, dynamic> json) {
    return PriceItemModel(
      price: json['price']?.toDouble(),
      currency: json['currency'],
      description: json['description'],
      discountPrice: json['discount_price']?.toDouble(),
      totalPrice: json['total_price']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'price': price,
      'currency': currency,
      'description': description,
      'discount_price': discountPrice,
      'total_price': totalPrice,
    };
  }
}

class ExtraNoteModel {
  final String? description;
  final String? note;

  const ExtraNoteModel({
    this.description,
    this.note,
  });

  factory ExtraNoteModel.fromJson(Map<String, dynamic> json) {
    return ExtraNoteModel(
      description: json['description'],
      note: json['note'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'note': note,
    };
  }
}
