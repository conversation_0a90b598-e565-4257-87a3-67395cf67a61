class KYCResponseModel {
  final String? dateOfBirth;
  final int? milageId;
  final int? paymentMethodId;
  final int? nationalId;
  final int? customerId;
  final int? cityId;
  final String? drivingLicenceUrl;
  final String? identityDocumentUrl;
  final String? nationality;
  final int? commitmentMonths;
  final String? pickupDate;
  final int? vehicleId;
  final int? insuranceId;
  final String? campaignName;
  final String? stepName;
  final String? captainName;
  final int? vehiclePricingId;

  const KYCResponseModel({
    this.dateOfBirth,
    this.milageId,
    this.paymentMethodId,
    this.nationalId,
    this.customerId,
    this.cityId,
    this.drivingLicenceUrl,
    this.identityDocumentUrl,
    this.nationality,
    this.commitmentMonths,
    this.pickupDate,
    this.vehicleId,
    this.insuranceId,
    this.campaignName,
    this.stepName,
    this.captainName,
    this.vehiclePricingId,
  });

  factory KYCResponseModel.fromJson(Map<String, dynamic> json) {
    return KYCResponseModel(
      dateOfBirth: json['date_of_birth'] as String?,
      milageId: json['milage_id'] as int?,
      paymentMethodId: json['payment_method_id'] as int?,
      nationalId: json['national_id'] as int?,
      customerId: json['customer_id'] as int?,
      cityId: json['city_id'] as int?,
      drivingLicenceUrl: json['driving_licence_url'] as String?,
      identityDocumentUrl: json['identity_document_url'] as String?,
      nationality: json['nationality'] as String?,
      commitmentMonths: json['commitment_months'] as int?,
      pickupDate: json['pickup_date'] as String?,
      vehicleId: json['vehicle_id'] as int?,
      insuranceId: json['insurance_id'] as int?,
      campaignName: json['campaign_name'] as String?,
      stepName: json['step_name'] as String?,
      captainName: json['captain_name'] as String?,
      vehiclePricingId: json['vehicle_pricing_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date_of_birth': dateOfBirth,
      'milage_id': milageId,
      'payment_method_id': paymentMethodId,
      'national_id': nationalId,
      'customer_id': customerId,
      'city_id': cityId,
      'driving_licence_url': drivingLicenceUrl,
      'identity_document_url': identityDocumentUrl,
      'nationality': nationality,
      'commitment_months': commitmentMonths,
      'pickup_date': pickupDate,
      'vehicle_id': vehicleId,
      'insurance_id': insuranceId,
      'campaign_name': campaignName,
      'step_name': stepName,
      'captain_name': captainName,
      'vehicle_pricing_id': vehiclePricingId,
    };
  }
} 