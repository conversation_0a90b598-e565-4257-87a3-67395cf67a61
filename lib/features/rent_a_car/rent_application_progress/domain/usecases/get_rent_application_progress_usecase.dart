import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/domain/entities/rent_application_progress_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/domain/repositories/rent_application_progress_repository.dart';

class GetRentApplicationProgressUseCase
    implements UseCase<List<RentApplicationProgressEntity>?, NoParams> {
  final RentApplicationProgressRepository repository;

  GetRentApplicationProgressUseCase(this.repository);

  @override
  Future<Either<Failure, List<RentApplicationProgressEntity>?>> call(
      NoParams params) async {
    return await repository.getRentApplicationProgress();
  }
} 