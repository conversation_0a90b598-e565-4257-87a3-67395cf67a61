import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/data/data_sources/rent_application_progress_remote_data_source.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/data/mappers/rent_application_progress_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/domain/entities/rent_application_progress_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/domain/repositories/rent_application_progress_repository.dart';

class RentApplicationProgressRepositoryImpl
    implements RentApplicationProgressRepository {
  final RentApplicationProgressRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  RentApplicationProgressRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<RentApplicationProgressEntity>?>> getRentApplicationProgress() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.getRentApplicationProgress();
        return Right(result?.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
} 