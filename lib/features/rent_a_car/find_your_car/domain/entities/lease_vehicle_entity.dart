class LeaseVihecleEntity {
  final int? supplierId;
  final num? rate;
  final String? monthlyPrice;
  final bool? fixedFleet;
  final List<String>? notes;
  final int? id;
  final String? dailyPriceBetween;
  final String? contractType;
  final String? priceNote;
  final String? title;
  final String? image;
  final int? vehicleId;
  final bool? isAvailable;
  final String? period;
  final String? milage;
  final String? manufactureYear;
  final String? currency;
  final String? monthlyPriceTitle;
  final String? availableTitle;

  LeaseVihecleEntity({
    this.supplierId,
    this.rate,
    this.monthlyPrice,
    this.fixedFleet,
    this.notes,
    this.id,
    this.dailyPriceBetween,
    this.contractType,
    this.priceNote,
    this.title,
    this.image,
    this.vehicleId,
    this.isAvailable,
    this.period,
    this.milage,
    this.manufactureYear,
    this.currency,
    this.monthlyPriceTitle,
    this.availableTitle,
  });
}
