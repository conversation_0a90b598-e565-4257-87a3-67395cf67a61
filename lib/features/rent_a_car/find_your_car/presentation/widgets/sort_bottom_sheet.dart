import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

import 'package:thrivve/features/rent_a_car/find_your_car/presentation/manager/find_car_controller.dart';
import 'package:thrivve/generated/assets.dart';

class SortBottomSheet extends GetView<FindCarController> {
  const SortBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.circular(20.sp)),
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 20.sp),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.close,
                  color: context.black,
                  size: 32.sp,
                ),
              ),
              CustomTextWidget(
                title: "sort_by".tr,
                size: 15.sp,
                fontWeight: FontWeight.w600,
              ),
              SizedBox(width: 32.sp),
            ],
          ),
          SizedBox(
            height: 5.sp,
          ),
          Divider(
            height: 20.sp,
            thickness: 2.sp,
            color: context.borderColor,
          ),
          SizedBox(
            height: 30.sp,
          ),
          Obx(() {
            if (controller.isSortOptionsLoading.value) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 20.sp),
                child: CircularProgressIndicator(),
              );
            }

            return Column(
              children: controller.sortOptions.asMap().entries.map((entry) {
                int index = entry.key;
                var sortOption = entry.value;
                return _buildBottomsheetItem(
                  context,
                  image: _getIconPath(sortOption.icon ?? ''),
                  title: sortOption.title ?? '',
                  subtitle: sortOption.subTitle ?? '',
                  value: sortOption.value ?? '',
                  index: index,
                );
              }).toList(),
            );
          }),
          SizedBox(
            height: 30.sp,
          )
        ],
      ),
    );
  }

  String _getIconPath(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'star':
        return "assets/thrivvePhotos/star.svg";
      case 'list':
        return "assets/thrivvePhotos/list.svg";
      case 'money':
        return "assets/thrivvePhotos/usd.svg";
      default:
        return "assets/thrivvePhotos/list.svg"; // Default icon
    }
  }

  Widget _buildBottomsheetItem(BuildContext context,
      {required String title,
      required String subtitle,
      required String image,
      required String value,
      required int index}) {
    return Obx(() => GestureDetector(
          onTap: () {
            controller.selectedFilterIndex.value = index;
            controller.updateSortOption(value, title);
            Get.back();
          },
          child: Container(
            color: Colors.transparent,
            child: Row(
              children: [
                Container(
                  height: 40.h,
                  width: 40.w,
                  padding: EdgeInsets.all(10.sp),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: context.appBackgroundColor,
                  ),
                  child: SvgPicture.asset(
                    image,
                    height: 16.sp,
                    width: 16.sp,
                    colorFilter: ColorFilter.mode(
                      context.black,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                SizedBox(
                  width: 10.sp,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWidget(
                      title: title,
                      size: 12.sp,
                      fontWeight:
                          (controller.selectedFilterIndex.value == index)
                              ? FontWeight.w700
                              : FontWeight.w400,
                    ),
                    CustomTextWidget(
                      title: subtitle,
                      size: 11.sp,
                      color: context.black.withOpacity(0.7),
                      fontWeight: FontWeight.w400,
                    )
                  ],
                ),
                const Spacer(),
                if (controller.selectedFilterIndex.value == index)
                  Image.asset(
                    Assets.thrivvePhotosSuccess,
                    height: 28.sp,
                  )
              ],
            ).marginSymmetric(vertical: 15.sp),
          ),
        ));
  }
}
