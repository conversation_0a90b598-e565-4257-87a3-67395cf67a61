import 'package:flutter/material.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

import '../../../../generated/assets.dart';
import 'dynamic_text_widget.dart';

class NotificationItem extends StatelessWidget {
  final Function()? onTap;
  final String title;
  final String? subTitle;
  final String time;
  final String image;
  final bool isSeen;
  final bool isApproved;
  final String? action;

  const NotificationItem(
      {super.key,
      this.onTap,
      required this.isSeen,
      this.isApproved = true,
      required this.title,
      required this.subTitle,
      required this.time,
      this.action,
      this.image = Assets.thrivvePhotosId});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: action != null ? onTap : null,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
            color: context.containerColor,
            borderRadius: BorderRadius.circular(16.0)),
        child: Padding(
          padding:
              const EdgeInsets.only(top: 16, bottom: 16, left: 16, right: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      height: 48,
                      width: 48,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: context.iconAndBtnBackground,
                      ),
                      child: Image.asset(
                        image,
                        width: 24,
                        height: 24,
                        color: context.blackIconColor,
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          DynamicTextWidget(
                            text: title,
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: context.black),
                          ),
                          Visibility(
                            visible: subTitle?.isNotEmpty ?? false,
                            child: Column(
                              children: [
                                const SizedBox(
                                  height: 4,
                                ),
                                DynamicTextWidget(
                                  text: subTitle.toString(),
                                  style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: context.errorMessageColor),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Row(children: [
                Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: CustomTextWidget(
                    title: time,
                    size: 10,
                    fontWeight: FontWeight.w500,
                    color: context.black.withValues(alpha: 0.40),
                  ),
                ),
                Visibility(
                  visible: action != null && !isSeen,
                  child: Icon(Icons.arrow_forward_ios_rounded,
                      size: 14, color: context.lightBlack),
                )
              ])
            ],
          ),
        ),
      ),
    );
  }
}
