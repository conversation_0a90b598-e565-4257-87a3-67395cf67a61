import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/unseen_number.dart';
import '../repositories/notification_repository.dart';

class GetUnSeenNotificationUseCase implements UseCase<UnseenNumber?, NoParams> {
  final NotificationRepository? notificationRepository;

  GetUnSeenNotificationUseCase({this.notificationRepository});

  @override
  Future<Either<Failure, UnseenNumber?>?> call(NoParams noParams) async {
    return await notificationRepository?.getUnseenNotificationNum();
  }
}
