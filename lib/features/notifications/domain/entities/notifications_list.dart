import 'package:equatable/equatable.dart';

class NotificationList extends Equatable {
  final List<Notification>? listOfNotification;

  const NotificationList({required this.listOfNotification});

  // add duammy data
  static NotificationList dummyData = NotificationList(
    listOfNotification: Notification.dummyData,
  );

  @override
  List<Object?> get props => [listOfNotification];
}

class Notification extends Equatable {
  final int notificationId;
  final String? title;
  final String? desc;
  final String? time;
  final DateTime? creation;
  final String? action;
  final String? notificationStatus;
  final bool? isSeen;

  const Notification({
    required this.notificationId,
    required this.title,
    required this.action,
    required this.desc,
    required this.time,
    required this.isSeen,
    required this.notificationStatus,
    required this.creation,
  });

  // add duammy data
  static List<Notification> dummyData = [
    Notification(
        notificationId: 1,
        title: "title 1",
        action: "action 1",
        desc: "desc 1",
        time: "time 1",
        isSeen: false,
        notificationStatus: "notificationStatus 1",
        creation: DateTime.now()),
    Notification(
      notificationId: 2,
      title: "title 2",
      action: "action 2",
      desc: "desc 2",
      time: "time 2",
      isSeen: false,
      notificationStatus: "notificationStatus 2",
        creation: DateTime.now()),

    Notification(
      notificationId: 3,
      title: "title 3",
      action: "action 3",
      desc: "desc 3",
      time: "time 3",
      isSeen: false,
      notificationStatus: "notificationStatus 3",
      creation: DateTime(2021, 10, 11),
    ),
    Notification(
      notificationId: 4,
      title: "title 4",
      action: "action 4",
      desc: "desc 4",
      time: "time 4",
      isSeen: false,
      notificationStatus: "notificationStatus 4",
      creation: DateTime(2021, 10, 12),
    ),
    Notification(
      notificationId: 5,
      title: "title 5",
      action: "action 5",
      desc: "desc 5",
      time: "time 5",
      isSeen: false,
      notificationStatus: "notificationStatus 5",
      creation: DateTime(2021, 10, 13),
    ),
    Notification(
      notificationId: 6,
      title: "title 6",
      action: "action 6",
      desc: "desc 6",
      time: "time 6",
      isSeen: false,
      notificationStatus: "notificationStatus 6",
      creation: DateTime(2021, 10, 14),
    ),
    Notification(
      notificationId: 7,
      title: "title 7",
      action: "action 7",
      desc: "desc 7",
      time: "time 7",
      isSeen: false,
      notificationStatus: "notificationStatus 7",
      creation: DateTime(2021, 10, 15),
    ),
    Notification(
      notificationId: 8,
      title: "title 8",
      action: "action 8",
      desc: "desc 8",
      time: "time 8",
      isSeen: false,
      notificationStatus: "notificationStatus 8",
      creation: DateTime(2021, 10, 16),
    ),
  ];

  @override
  List<Object?> get props => [
        notificationId,
        action,
        isSeen,
        title,
        desc,
        time,
        notificationStatus,
      ];
}
