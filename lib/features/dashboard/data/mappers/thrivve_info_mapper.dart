import '../../domain/entities/thrivve_info_entity.dart';
import '../models/thrivve_info_model.dart';

extension ThrivveInfoMapper on ThrivveInfoEntity {
  ThrivveInfoModel toModel() {
    return ThrivveInfoModel(
      mobile: mobile,
      email: email,
      whatsappUrl: whatsappUrl,
      whatsappMobile: whatsappMobile,
      locationUrl: locationUrl,
      titleMap: titleMap,
      address: address,
      latitude: latitude,
      longitude: longitude,
    );
  }
}

extension ThrivveInfoModelMapper on ThrivveInfoModel {
  ThrivveInfoEntity toEntity() {
    return ThrivveInfoEntity(
      mobile: mobile,
      email: email,
      whatsappUrl: whatsappUrl,
      whatsappMobile: whatsappMobile,
      locationUrl: locationUrl,
      titleMap: titleMap,
      address: address,
      latitude: latitude,
      longitude: longitude,
    );
  }
}
