import '../../domain/entities/withdraw_balance.dart';

class WithdrawModel extends WithdrawBalance {
  final num? balance;
  final num? availableBalance;

  const WithdrawModel({
    required this.balance,
    required this.availableBalance,
  }) : super(withdrawBalance: (availableBalance as num) ?? 0.0);

  factory WithdrawModel.fromJson(Map<String, dynamic> json) => WithdrawModel(
        balance: json["balance"],
        availableBalance: json["available_balance"],
      );
}
