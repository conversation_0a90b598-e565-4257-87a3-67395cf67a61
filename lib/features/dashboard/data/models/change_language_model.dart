class ChangeLanguageModel {
  final String language;
  final String accessToken;
  final String refreshToken;

  const ChangeLanguageModel({
    required this.language,
    required this.accessToken,
    required this.refreshToken,
  });

  factory ChangeLanguageModel.fromJson(Map<String, dynamic> json) {
    return ChangeLanguageModel(
      language: json['language'] as String,
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
    );
  }
} 