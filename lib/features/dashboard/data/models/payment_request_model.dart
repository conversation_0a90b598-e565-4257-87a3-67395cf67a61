import '../../domain/entities/payment_request.dart';

class PaymentRequestResultModel extends PaymentRequest {
  @override
  final String message;
  final int id;

  const PaymentRequestResultModel({
    required this.message,
    required this.id,
  }) : super(message: message, paymentId: id);

  factory PaymentRequestResultModel.fromJson(Map<String, dynamic> json) =>
      PaymentRequestResultModel(
        message: json["message"],
        id: json["id"],
      );
}
