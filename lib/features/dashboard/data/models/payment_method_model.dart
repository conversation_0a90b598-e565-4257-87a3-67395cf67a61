import '../../domain/entities/payment_method.dart';

class PaymentMethodModel extends PaymentMethod {
  @override
  final String? paymentType;
  @override
  final int? id;
  @override
  final String? bankName;
  final String? image;
  @override
  final String? description;
  @override
  final String? status;
  @override
  final String? statusWarning;
  final String? beneficiaryName;
  @override
  final String? beneficiaryIban;
  @override
  final bool? isDefault;
  //bank_id
  @override
  final int? bankId;

  PaymentMethodModel({
    required this.paymentType,
    required this.id,
    required this.bankName,
    required this.description,
    required this.image,
    required this.beneficiaryName,
    this.status,
    this.bankId,
    this.statusWarning,
    this.beneficiaryIban,
    this.isDefault,
  }) : super(
          paymentType: paymentType,
          bankId: bankId,
          id: id,
          bankName: bankName,
          bankImage: image,
          description: description,
          status: status,
          statusWarning: statusWarning,
          beneficiaryIban: beneficiaryIban,
          isDefault: isDefault,
          isSelected: isDefault,
          bankBeneficiaryName: beneficiaryName,
        );

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) =>
      PaymentMethodModel(
        paymentType: json["payment_type"],
        id: json["id"],
        bankId: json["bank_id"],
        bankName: json["bank_name"],
        description: json["description"],
        image: json["bank_image"],
        status: json["status"],
        statusWarning: json["status_warning"],
        beneficiaryIban: json["beneficiary_iban"],
        beneficiaryName: json["beneficiary_name"],
        isDefault: json["is_default"],
      );
}
