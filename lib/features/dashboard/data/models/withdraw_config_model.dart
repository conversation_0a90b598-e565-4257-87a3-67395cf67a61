import '../../domain/entities/withdraw_config.dart';

class WithdrawConfigModel extends WithdrawConfig {
  final dynamic maxValuePercentage;
  final dynamic minimumAmount;
  final dynamic increaseBy;

  WithdrawConfigModel({
    this.maxValuePercentage,
    this.minimumAmount,
    this.increaseBy,
  }) : super(
          maxValuePerc: double.parse(maxValuePercentage.toString()),
          minAmount: double.parse(minimumAmount.toString()),
          increaseByValue: double.parse(increaseBy.toString()),
        );

  factory WithdrawConfigModel.fromJson(Map<String, dynamic> json) =>
      WithdrawConfigModel(
        maxValuePercentage: json["max_value_percentage"],
        minimumAmount: json["minimum_amount"],
        increaseBy: json["increase_by"],
      );
}
