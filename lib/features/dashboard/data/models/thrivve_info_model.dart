class ThrivveInfoModel {
  final String? mobile;
  final String? email;
  final String? whatsappUrl;
  final String? whatsappMobile;
  final String? locationUrl;
  final String? address;
  final String? titleMap;
  final double? latitude;
  final double? longitude;

  const ThrivveInfoModel({
    this.mobile,
    this.email,
    this.whatsappUrl,
    this.whatsappMobile,
    this.locationUrl,
    this.address,
    this.titleMap,
    this.latitude,
    this.longitude,
  });

  factory ThrivveInfoModel.fromJson(Map<String, dynamic> json) {
    return ThrivveInfoModel(
      mobile: json['mobile'],
      email: json['email'],
      whatsappUrl: json['wahtsapp_url'],
      whatsappMobile: json['wahtsapp_mobile'],
      locationUrl: json['location_url'],
      titleMap: json['title_map'],
      address: json['address_map'],
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mobile': mobile,
      'email': email,
      'wahtsapp_url': whatsappUrl,
      'wahtsapp_mobile': whatsappMobile,
      'location_url': locationUrl,
      'title_map': titleMap,
      'address_map': address,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}
