import 'package:thrivve/features/dashboard/data/models/transaction_v2_model.dart';

import '../../domain/entities/main_transaction.dart';

class TransactionV2ListModel extends MainTransaction {
  final List<TransactionV2Model>? items;
  final dynamic nextNum;

  const TransactionV2ListModel({
    this.items,
    this.nextNum,
  }) : super(
          transactions: items,
          hasMore: nextNum != null,
        );

  factory TransactionV2ListModel.fromJson(Map<String, dynamic> json) =>
      TransactionV2ListModel(
        items: json["items"] == null
            ? []
            : List<TransactionV2Model>.from(
                json["items"]!.map((x) => TransactionV2Model.fromJson(x))),
        nextNum: json["next_num"],
      );
}
