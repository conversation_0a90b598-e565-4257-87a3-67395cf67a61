import '../../domain/entities/banners.dart';

class BannersModel extends Banners {
  final String? backgroundUrl;
  final String? link;
  @override
  final String? title;
  @override
  final String? description;
  final String? iconUrl;
  final dynamic id;

  BannersModel({
    required this.backgroundUrl,
    required this.link,
    required this.description,
    required this.iconUrl,
    required this.id,
    required this.title,
  }) : super(
            backgroundImageUrl: backgroundUrl,
            iconImageUrl: iconUrl,
            title: title,
            description: description,
            linkUrl: link,
            ids: int.parse(id.toString()));

  factory BannersModel.fromJson(Map<String, dynamic> json) => BannersModel(
        backgroundUrl: json["background_url"],
        link: json["link"],
        description: json["description"],
        iconUrl: json["icon_url"],
        id: json["id"],
        title: json["title"],
      );
}
