
import '../../domain/entities/insights.dart';

class InsightsModel extends Insights {
  final double? baseValue;
  final List<ChartDatum>? chartData;

  InsightsModel({
    this.baseValue,
    this.chartData,
  }) : super(
            basedValue: baseValue?.toDouble(),
            listOfInsights: chartData
                ?.map((e) => Insight(
                    value: e.value?.toDouble(),
                    label: e.label,
                    date: e.date,
                    percentage: e.percentage?.toDouble()))
                .toList());

  factory InsightsModel.fromJson(Map<String, dynamic> json) => InsightsModel(
        baseValue: json["base_value"],
        chartData: json["chart_data"] == null
            ? []
            : List<ChartDatum>.from(
                json["chart_data"]!.map((x) => ChartDatum.fromJson(x))),
      );
}

class ChartDatum {
  final String? date;
  final String? label;
  final double? value;
  final double? percentage;

  ChartDatum({
    this.date,
    this.label,
    this.value,
    this.percentage,
  });

  factory ChartDatum.fromJson(Map<String, dynamic> json) => ChartDatum(
        date: json["date"],
        label: json["label"],
        value: json["value"],
        percentage: json["percentage"],
      );
}
