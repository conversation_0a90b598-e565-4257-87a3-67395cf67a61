import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/network_connection/network_widget.dart';
import 'package:thrivve/core/util/phone_dilar.dart';
import 'package:thrivve/features/dashboard/domain/entities/product.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/login_signup/sheet_login_or_register_widget.dart';
import 'package:url_launcher/url_launcher.dart';

enum WebViewDisplayMode { fullPage, bottomSheet }

enum WebViewLoadingState { loading, loaded, error }

class MyWebViewWidget2 extends StatefulWidget {
  final String? url;
  final String? leadId;
  final String token;
  final Map<String, dynamic>? dlParams;
  final Product? product;
  final WebViewDisplayMode displayMode;

  const MyWebViewWidget2({
    super.key,
    this.url,
    required this.token,
    required this.leadId,
    this.dlParams,
    this.displayMode = WebViewDisplayMode.fullPage,
    this.product,
  });

  static Future<dynamic> showAsBottomSheet({
    required BuildContext context,
    required String? url,
    required String token,
    required String leadId,
    Map<String, dynamic>? dlParams,
  }) {
    return showModalBottomSheet(
      context: context,
      enableDrag: true,
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useSafeArea: true,
      builder: (context) => MyWebViewWidget2(
        url: url,
        token: token,
        leadId: leadId,
        dlParams: dlParams,
        displayMode: WebViewDisplayMode.bottomSheet,
      ),
    );
  }

  @override
  State<MyWebViewWidget2> createState() => _MyWebViewWidget2State();
}

class _MyWebViewWidget2State extends State<MyWebViewWidget2> {
  late InAppWebViewController _controller;
  WebViewLoadingState _loadingState = WebViewLoadingState.loading;
  Timer? _loadingTimer;
  bool _isDisposed = false;
  bool _jsInjected = false;
  bool _initialLoadComplete = false;
  DateTime? _lastErrorTime;
  int _consecutiveErrors = 0;
  static const int _maxConsecutiveErrors = 3;
  static const Duration _errorCooldown = Duration(seconds: 30);

  @override
  void initState() {
    super.initState();
    _startLoadingTimeout();
  }

  void _startLoadingTimeout() {
    _loadingTimer?.cancel();
    _loadingTimer = Timer(const Duration(seconds: 10), () {
      if (!_isDisposed && !_jsInjected) {
        _handleLoadingTimeout();
      }
    });
  }

  void _handleLoadingTimeout() {
    if (_loadingState != WebViewLoadingState.loaded) {
      _updateLoadingState(WebViewLoadingState.error);
    }
  }

  void _updateLoadingState(WebViewLoadingState newState) {
    if (_isDisposed) return;

    if (newState == WebViewLoadingState.error) {
      final now = DateTime.now();
      if (_lastErrorTime != null) {
        if (now.difference(_lastErrorTime!) < _errorCooldown) {
          _consecutiveErrors++;
        } else {
          _consecutiveErrors = 1;
        }
      } else {
        _consecutiveErrors = 1;
      }
      _lastErrorTime = now;

      if (_consecutiveErrors >= _maxConsecutiveErrors) {
        // If we've had too many errors, don't retry automatically
        _logShowError();
      }
    } else {
      _consecutiveErrors = 0;
      _lastErrorTime = null;
    }

    setState(() {
      _loadingState = newState;
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    _loadingTimer?.cancel();
    super.dispose();
  }

  InAppWebViewSettings get _webViewSettings => InAppWebViewSettings(
        javaScriptEnabled: true,
        domStorageEnabled: true,
        useShouldOverrideUrlLoading: true,
        javaScriptCanOpenWindowsAutomatically: true,
        clearCache: false,
        mediaPlaybackRequiresUserGesture: false,
        allowsInlineMediaPlayback: true,
        useHybridComposition: true,
        supportZoom: false,
        verticalScrollBarEnabled: true,
        horizontalScrollBarEnabled: false,
        disableVerticalScroll: false,
        useOnLoadResource: true,
        transparentBackground:
            widget.displayMode == WebViewDisplayMode.bottomSheet,
      );

  Future<void> _injectJavaScript() async {
    if (_jsInjected) return;

    try {
      final result = await _controller.evaluateJavascript(
        source: _initializationJs,
      );

      if (result == null || result is! Map || result['success'] != true) {
        throw Exception(
            'JavaScript injection failed: ${result?['error'] ?? 'Unknown error'}');
      }

      if (kDebugMode) {
        print('JavaScript injection successful');
      }

      _jsInjected = true;
      _loadingTimer?.cancel();
      _updateLoadingState(WebViewLoadingState.loaded);
    } catch (e) {
      await _handleWebViewError('JavaScript injection failed: $e');
      _updateLoadingState(WebViewLoadingState.error);
    }
  }

  Future<void> _handleWebViewError(String error,
      [StackTrace? stackTrace]) async {
    if (_isDisposed) return;

    if (kDebugMode) {
      print('WebView Error: $error');
    }

    await FirebaseCrashlytics.instance
        .setCustomKey('url', widget.url ?? 'null');
    await FirebaseCrashlytics.instance
        .setCustomKey('leadId', widget.leadId ?? 'null');
    await FirebaseCrashlytics.instance
        .setCustomKey('displayMode', widget.displayMode.toString());
    final storage = await getIt<UserSecureDataSource>();
    final user = await storage.getUserData();
    final customerId = user?.customerId;
    await FirebaseCrashlytics.instance.recordError(
      error,
      stackTrace ?? StackTrace.current,
      reason: 'WebView Error',
      fatal: false,
      information: [
        'CustomerId: $customerId',
        'URL: ${widget.url}',
        'LeadID: ${widget.leadId}',
        'Display Mode: ${widget.displayMode}',
      ],
    );
  }

  String get _initializationJs => '''
    (function() {
      try {
        if (typeof localStorage === 'undefined') {
          throw new Error('localStorage is not available');
        }

        localStorage.setItem('token', '${widget.token}');
        localStorage.setItem('leadId', '${widget.leadId}');
        
        const event = new CustomEvent('setLeadId', {
          detail: { 
            leadId: '${widget.leadId}',
            token: '${widget.token}'
          }
        });
        window.dispatchEvent(event);

        const storedToken = localStorage.getItem('token');
        const storedLeadId = localStorage.getItem('leadId');
       
        if (storedToken !== '${widget.token}' || storedLeadId !== '${widget.leadId}') {
          throw new Error('Storage verification failed');
        }

        return { 
          success: true,
          token: storedToken,
          leadId: storedLeadId
        };
      } catch (error) {
        console.error('Initialization error:', error);
        return { 
          success: false,
          error: error.message
        };
      }
    })()
  ''';

  Widget _buildWebView(BuildContext mContext) {
    final validUrl = (widget.url ?? '').isNotEmpty ? widget.url : '';

    return Stack(
      children: [
        InAppWebView(
          initialUrlRequest: URLRequest(url: WebUri(validUrl ?? '')),
          initialSettings: _webViewSettings,
          onWebViewCreated: (controller) {
            _controller = controller;
            _controller.addJavaScriptHandler(
              handlerName: "ReactNativeWebView",
              callback: _handleJavaScriptMessage,
            );
          },
          onLoadStart: (controller, url) {
            if (!_isDisposed) {
              _updateLoadingState(WebViewLoadingState.loading);
              _jsInjected = false;
              _startLoadingTimeout();
            }
          },
          onReceivedError: (controller, request, error) async {
            if (!_isDisposed) {
              await _handleWebViewError(
                'Failed to load URL (${error.hashCode}): ${error.description}',
                StackTrace.current,
              );
              _updateLoadingState(WebViewLoadingState.error);
            }
          },
          onLoadStop: (controller, url) async {
            if (!_isDisposed) {
              await _injectJavaScript();
              await _setupScrolling();
              _initialLoadComplete = true;
            }
          },
          shouldOverrideUrlLoading: (controller, navigationAction) async {
            var uri = navigationAction.request.url;
            if (uri != null) {
              if (uri.scheme == 'tel' ||
                  uri.scheme == 'mailto' ||
                  uri.scheme == 'sms' ||
                  uri.scheme == 'whatsapp') {
                await launchUrl(uri);
                return NavigationActionPolicy.CANCEL;
              }
            }
            return NavigationActionPolicy.ALLOW;
          },
        ),
        if (_loadingState == WebViewLoadingState.loading)
          Center(
            child: CircularProgressIndicator(
              color: mContext.appPrimaryColor,
            ),
          ),
        if (_loadingState == WebViewLoadingState.error)
          NetworkConnectionWidget(
            fromWebView: true,
            onClickRetry: _onClickReload,
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.displayMode == WebViewDisplayMode.bottomSheet) {
      final mediaQuery = MediaQuery.of(context);
      final height = mediaQuery.size.height * 0.8;

      return Container(
        constraints: BoxConstraints(
          maxHeight: height,
          minHeight: mediaQuery.size.height * 0.3,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20)),
                child: _buildWebView(context),
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: _buildWebView(context),
      ),
    );
  }

  Future<void> _setupScrolling() async {
    if (widget.displayMode != WebViewDisplayMode.bottomSheet) return;

    await _controller.evaluateJavascript(source: """
      if (!document.getElementById('webview-scroll-style')) {
        const style = document.createElement('style');
        style.id = 'webview-scroll-style';
        style.textContent = `
          html, body {
            overflow-y: scroll !important;
            overflow-x: hidden !important;
            -webkit-overflow-scrolling: touch !important;
            height: 100% !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            background-color: transparent !important;
          }
          body {
            padding: 0px !important;
            box-sizing: border-box !important;
            min-height: 100vh !important;
            display: flex !important;
            flex-direction: column !important;
          }
          ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }
        `;
        document.head.appendChild(style);
      }
      
      const observer = new MutationObserver((mutations) => {
        const bodyHeight = Math.max(
          document.body.scrollHeight,
          document.documentElement.scrollHeight,
          document.body.offsetHeight,
          document.documentElement.offsetHeight,
          document.body.clientHeight,
          document.documentElement.clientHeight
        );
        document.documentElement.style.minHeight = bodyHeight + 'px';
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    """);
  }

  Future<void> _handleJavaScriptMessage(List<dynamic> args) async {
    if (_isDisposed) return;

    String message = args.isNotEmpty ? args[0].toString() : "";
    if (kDebugMode) {
      print('JavaScript message received: $message');
    }

    try {
      final action =
          message.split('/')[0].isEmpty ? message : message.split('/')[0];

      switch (action) {
        case 'closeWebView':
        case 'complete':
          widget.displayMode == WebViewDisplayMode.bottomSheet
              ? Navigator.pop(context, true)
              : Get.back(result: true);
          break;

        case 'page':
          final page = message.split('/')[1];
          widget.displayMode == WebViewDisplayMode.bottomSheet
              ? Navigator.pop(context, page)
              : Get.back(result: page);
          break;

        case 'externalLink':
          await _goToExternal(message);
          break;

        case 'new_register':
          if (widget.displayMode == WebViewDisplayMode.bottomSheet) {
            Navigator.pop(context);
          }
          showLoginOrRegisterSheet(
            context,
            dlParams: widget.dlParams,
            product: widget.product,
          );
          break;

        case 'call_dial':
          final phone = message.split('/')[1];
          _dialPhone(phone);
          break;
      }
    } catch (e) {
      await _handleWebViewError(
        'Error handling JavaScript message: $e\nMessage: $message',
        StackTrace.current,
      );
    }
  }

  Future<void> _goToExternal(String page) async {
    try {
      final link = page.replaceAll('externalLink/', '');
      if (link.isNotEmpty) {
        final uri = Uri.parse(link);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          await _handleWebViewError(
            'Failed to launch URL: $link',
            StackTrace.current,
          );
        }
      }
    } catch (e) {
      await _handleWebViewError(
        'Error launching external URL: $e',
        StackTrace.current,
      );
    }
  }

  void _dialPhone(String phone) {
    try {
      PhoneDialer.dial(phone);
    } catch (e) {
      _handleWebViewError(
        'Failed to dial phone number: $phone\nError: $e',
        StackTrace.current,
      );
    }
  }

  void _onClickReload() {
    if (_isDisposed) return;

    _jsInjected = false;
    _initialLoadComplete = false;
    _updateLoadingState(WebViewLoadingState.loading);
    _startLoadingTimeout();
    _logRetryError();

    // Add a small delay before reload to ensure state is reset
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!_isDisposed) {
        _controller.reload();
      }
    });
  }

  Future<void> _logShowError() async {
    if (_isDisposed) return;

    try {
      final storage = getIt<UserSecureDataSource>();
      final user = await storage.getUserData();
      final customerId = user?.customerId;
      final deviceId = await getDeviceId();

      getIt<IAnalyticsLogger>().logEvent('error_web_view', parameters: {
        'customerId': customerId ?? 'Guest',
        'device_id': deviceId,
        'consecutive_errors': _consecutiveErrors.toString(),
        'url': widget.url ?? 'null',
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error logging show error: $e');
      }
    }
  }

  Future<void> _logRetryError() async {
    if (_isDisposed) return;

    try {
      final storage = await getIt<UserSecureDataSource>();
      final user = await storage.getUserData();
      final customerId = user?.customerId;
      final deviceId = await getDeviceId();

      getIt<IAnalyticsLogger>().logEvent('click_retry_web_view', parameters: {
        'customerId': customerId ?? 'Guest',
        'device_id': deviceId,
        'consecutive_errors': _consecutiveErrors.toString(),
        'url': widget.url ?? 'null',
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error logging retry error: $e');
      }
    }
  }

  Future<String> getDeviceId() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id; // Android ID
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? 'unknown'; // UUID for vendor
      }
      return 'unknown';
    } catch (e) {
      if (kDebugMode) {
        print('Error getting device ID: $e');
      }
      return 'unknown';
    }
  }
}
