import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/main_app_bar_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/login_signup/sheet_login_or_register_widget.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';

class CustomAppbar extends StatelessWidget {
  PersonalInfoEntity? personalInfo;
  GlobalKey profileWidgetKey;
  final bool? isLogin;
  CustomAppbar(
    this.personalInfo,
    this.profileWidgetKey,
    this.isLogin,
  );
  Widget loggedAppBar(BuildContext context) {
    final unseenNotificationNum = context.select((MainHomeBloc mainHomeBloc) =>
        mainHomeBloc.state.unseenNotificationNum);
    final isNotificationNeedActions = context.select(
        (MainHomeBloc mainHomeBloc) =>
            mainHomeBloc.state.isNotificationNeedActions);

    return MainAppBarWidget(
      profileWidgetKey: profileWidgetKey,
      unseenNotificationNum: unseenNotificationNum,
      isNotificationNeedActions: isNotificationNeedActions,
      personalInfo: personalInfo,
    );
  }

  Widget notLogedInAppbar(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            child: SvgPicture.asset("assets/thrivvePhotos/thrivve_header.svg"),
          ),
          const Spacer(),
          InkWell(
            onTap: () {
              showLoginOrRegisterSheet(
                Get.context!,
                dlParams: {},
              );
            },
            child: SizedBox(
              height: 40.sp,
              child: Row(
                children: [
                  SizedBox(
                    width: 18.w,
                    height: 18.h,
                    child: SvgPicture.asset(
                      "assets/thrivvePhotos/unregister_user.svg",
                    ),
                  ),
                  SizedBox(
                    width: 6.sp,
                  ),
                  CustomTextWidget(
                    title: 'sign_in'.tr,
                    size: 14,
                    fontWeight: FontWeight.w600,
                    color: context.outlineButtonColor,
                    height: 1.2,
                    decoration: TextDecoration.underline,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return isLogin == null
        ? SizedBox()
        : isLogin == true
            ? loggedAppBar(context)
            : notLogedInAppbar(context);
  }
}
