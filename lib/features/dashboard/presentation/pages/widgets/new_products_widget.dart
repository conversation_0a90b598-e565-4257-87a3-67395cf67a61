import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/dashboard/domain/entities/new_dashboard_products_entity.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/dashboard/presentation/pages/dashboard_page.dart';

/// Widget to display a list of new products in a dashboard

class NewProductsWidget extends StatelessWidget {
  const NewProductsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      buildWhen: (previous, current) =>
          previous.newProducts != current.newProducts,
      builder: (context, state) {
        // Don't show during loading
        if (state.getNewProductsStatus == AppStatus.loading) {
          return _buildShimmerPlaceholder(context);
        }
        List<DashboardProductItemEntity> newProducts = state.newProducts ?? [];
        if (newProducts.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: EdgeInsets.symmetric(horizontal: 16.sp),
          child: Column(
            children: [
              if (newProducts.isNotEmpty)
                InkWell(
                    onTap: () {
                      productAction(context, newProducts[0]);
                    },
                    child: MainNewProductWidget(product: newProducts[0])),
              SizedBox(height: 8.sp),
              if (newProducts.length > 1)
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: newProducts.length > 1
                            ? InkWell(
                                onTap: () {
                                  productAction(context, newProducts[1]);
                                },
                                child: SubNewProductWidget(
                                  product: newProducts[1],
                                ))
                            : const SizedBox.shrink(),
                      ),
                      SizedBox(width: 8.sp),
                      Expanded(
                        child: newProducts.length > 2
                            ? InkWell(
                                onTap: () {
                                  productAction(context, newProducts[2]);
                                },
                                child: SubNewProductWidget(
                                  product: newProducts[2],
                                ))
                            : const SizedBox.shrink(),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShimmerPlaceholder(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: context.borderAddBranch,
      highlightColor: context.appBackgroundColor,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 20.sp),
        child: Column(
          children: [
            // Shimmer for MainNewProductWidget
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Placeholder for image
                Container(
                  height: 350.sp,
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15.sp)),
                ),
                SizedBox(height: 12.sp),
                Row(
                  children: [
                    Expanded(
                        child: Container(
                      height: 200.sp,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15.sp)),
                    )),
                    SizedBox(
                      width: 10.sp,
                    ),
                    Expanded(
                        child: Container(
                      height: 200.sp,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15.sp)),
                    ))
                  ],
                )
              ],
            ),

            // Shimmer for SubNewProductWidget (two cards)
          ],
        ),
      ),
    );
  }

  productAction(BuildContext context, DashboardProductItemEntity? product) {
    if (product?.isSoon == true) {
      cheekSoon(product?.productName, context);
    } else {
      clickStatusProgress(
        context,
        applicationTypeEnum: product?.applicationTypeEnum,
        dlParams: {
          "product_id": product?.id,
        },
        homePageMessage: getIt<DashboardBloc>().state.homePageMessage,
        dataWorkWithUber: getIt<DashboardBloc>().state.dataWorkWithUber,
      );
    }
  }
}

/// Widget to display the main featured product
class MainNewProductWidget extends StatelessWidget {
  final DashboardProductItemEntity product;

  const MainNewProductWidget({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _ProductCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                Get.find<ThemeController>().isDarkMode()
                    ? "assets/thrivvePhotos/icons/man_dark.png"
                    : "assets/thrivvePhotos/icons/man.png",
                height: 214.sp,
                width: 214.sp,
                fit: BoxFit.cover,
                // alignment: Alignment.topCenter,
              ),
              _ProductContent(
                product: product,
                showIcon: false,
              ),
            ],
          ),
        ),
        if ((product.extraNote ?? '').isNotEmpty)
          Positioned(
              left: Get.locale == Locale("en") ? 20.sp : null,
              right: Get.locale == Locale("ar") ? 20.sp : null,
              top: 16.sp,
              child: _buildIsNew(product.extraNote ?? ''))
      ],
    );
  }

  Widget _buildIsNew(String extraNote) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 7.sp),
      decoration: BoxDecoration(
          color: Get.context!.outlineButtonColor,
          borderRadius: BorderRadius.circular(20.sp)),
      child: CustomTextWidget(
        title: extraNote,
        color: Get.context!.whiteColor,
        fontWeight: FontWeight.w700,
        size: 12,
      ),
    );
  }
}

/// Widget to display secondary products
class SubNewProductWidget extends StatelessWidget {
  final DashboardProductItemEntity product;

  const SubNewProductWidget({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return _ProductCard(
      child: _ProductContent(
        product: product,
        isSubProduct: true,
      ),
    );
  }
}

/// Reusable product card container
class _ProductCard extends StatelessWidget {
  final Widget child;

  const _ProductCard({required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        border: Border.all(width: 0.5, color: Colors.black.withOpacity(0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(1, 1), // Shadow position
          ),
        ],
        color: context.newProductCardColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: child,
    );
  }
}

/// Reusable product content widget
class _ProductContent extends StatelessWidget {
  final bool showIcon;

  final DashboardProductItemEntity product;
  final bool isSubProduct;

  const _ProductContent(
      {required this.product, this.showIcon = true, this.isSubProduct = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.sp),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showIcon)
                Center(
                  child: Image.asset(
                    convertStringToImage(product.icon ?? ''),
                    height: 48.sp,
                    fit: BoxFit.scaleDown,
                  ),
                ),
              SizedBox(
                height: 16.sp,
              ),
              CustomTextWidget(
                title: product.title,
                fontWeight: FontWeight.w600,
                size: 15,
              ),
              SizedBox(height: 16.sp),
              CustomTextWidget(
                title: "${product.description}\n",
                fontWeight: FontWeight.w400,
                size: 14,
                height: 1.6,
                color: Get.isDarkMode ? Color(0xff9A999E) : Color(0xff4F4F4F),
              ),
            ],
          ),
          if (isSubProduct) const Spacer(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CustomTextWidget(
                title: product.actionTitle,
                fontWeight: FontWeight.w700,
                size: 12,
                height: 0.4,
                color: Get.find<ThemeController>().isDarkMode()
                    ? Colors.white
                    : Color(0xff4F4F4F),
              ),
              SizedBox(
                width: 6.sp,
              ),
              Transform.scale(
                scaleX: Get.locale?.languageCode == 'ar'
                    ? -1.0
                    : 1, // Negative value flips horizontally
                child: Image.asset(
                  "assets/thrivvePhotos/icons/arrow.png",
                  width: 12.sp,
                  color: Get.find<ThemeController>().isDarkMode()
                      ? Colors.white
                      : Color(0xff4F4F4F),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
