import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

class SectionHeaderWidget extends StatelessWidget {
  final Function() onTab;
  final String? title;
  final EdgeInsetsDirectional? padding;
  const SectionHeaderWidget({
    super.key,
    required this.onTab,
    this.title,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomTextWidget(
            title: title ?? "",
            size: 16,
            fontWeight: FontWeight.w600,
          ),
          InkWell(
            onTap: onTab,
            child: DecoratedBox(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: context.appPrimaryColor,
                    width: 1.0,
                  ),
                ),
              ),
              child: CustomTextWidget(
                title: "see_all".tr,
                size: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          )
        ],
      ),
    );
  }
}
