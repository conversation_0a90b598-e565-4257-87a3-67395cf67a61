import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/const.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../../generated/assets.dart';
import 'main_app_bar_widget.dart';

class UserIconAndProfileStatusWidget extends StatelessWidget {
  final Function() onTap;

  final PersonalInfoEntity? personalInfo;
  final GlobalKey profileWidgetKey;

  const UserIconAndProfileStatusWidget({
    super.key,
    required this.onTap,
    this.personalInfo,
    required this.profileWidgetKey,
  });

  @override
  Widget build(BuildContext context) {
    bool showWallet = context.select((MainHomeBloc mainHomeBloc) =>
            mainHomeBloc.state.personalInfo?.showWallet) ??
        true;
    return Row(
      children: [
        _buildUserAvatar(context),
        if (showWallet) SizedBox(width: 8.w),
        if (showWallet) _buildProfileStatusWidget(context),
      ],
    );
  }

  Widget _buildUserAvatar(BuildContext context) {
    return InkWell(
      key: profileWidgetKey,
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          personalInfo?.image != null
              ? SizedBox(
                  width: 40.w,
                  height: 40.h,
                  child: CircleAvatar(
                    radius: 19.r,
                    backgroundImage: CachedNetworkImageProvider(
                      personalInfo?.image ?? "",
                    ),
                  ),
                )
              : buildFallbackAvatar(personalInfo?.fullName, context),
          Visibility(
            visible: personalInfo?.isCompleteProfile == true &&
                personalInfo?.profileReviewStatus ==
                    withdrawTransactionsApprovedStatus,
            child: Positioned(
              right: -6.w,
              bottom: 0,
              child: Container(
                width: 16.w,
                height: 16.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: context.confirmIconColor,
                  border: Border.all(
                    color: context.whiteColor!,
                    width: 2.w,
                  ),
                ),
                child: Icon(
                  Icons.done_rounded,
                  color: context.whiteColor,
                  size: 10.r,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileStatusWidget(BuildContext context) {
    if (personalInfo?.isCompleteProfile == false &&
        (personalInfo?.profileReviewStatus == null ||
            personalInfo?.profileReviewStatus == profileReviewStatusRejected)) {
      return _buildIncompleteProfileWidget(context);
    } else {
      return _buildProfileReviewStatusWidget(context);
    }
  }

  Widget _buildIncompleteProfileWidget(BuildContext context) {
    return _buildProfileStatusItem(
      text: "complete_profile".tr,
      textColor: context.black,
      context: context,
      onTap: () {
        showContactDialog(context: context);
      },
    );
  }

  Widget _buildProfileReviewStatusWidget(BuildContext context) {
    switch (personalInfo?.profileReviewStatus) {
      case profileReviewStatusExpirySoon:
        return _buildExpiringSoonWidget(context);
      case profileReviewStatusExpired:
        return _buildExpiredWidget(context);
      case profileReviewStatusInReview:
      case profileReviewStatusPending:
        return _buildUnderReviewWidget(context);
      default:
        return Container();
    }
  }

  Widget _buildExpiringSoonWidget(BuildContext context) {
    return _buildProfileStatusItem(
      context: context,
      asset: Assets.thrivvePhotosId,
      text: "id_expiring_soon".tr,
      textColor: context.black,
      onTap: () {
        Get.toNamed(AppRoutes.identityIInfo);
      },
    );
  }

  Widget _buildExpiredWidget(BuildContext context) {
    return _buildProfileStatusItem(
      context: context,
      asset: Assets.thrivvePhotosId,
      text: "id_expired".tr,
      textColor: context.errorMessageColor,
      onTap: () {
        showContactDialog(context: context);
      },
    );
  }

  Widget _buildUnderReviewWidget(BuildContext context) {
    return _buildProfileStatusItem(
      context: context,
      text: "under_review".tr,
      textColor: context.black,
      onTap: () {
        userProfileInReviewBottomSheet(context);
      },
    );
  }

  Widget _buildProfileStatusItem({
    required String text,
    Color? textColor,
    String? asset,
    String? onTapText,
    bool? isClickable = true,
    required BuildContext context,
    Function()? onTap,
  }) {
    return InkWell(
      onTap: isClickable == true
          ? () {
              onTap?.call();
            }
          : null,
      child: Container(
        height: 30.h,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: context.notificationBackgroundColor,
          borderRadius: BorderRadius.circular(16.0.r),
          border: Border.all(color: context.confirmIconColor!, width: 1.w),
        ),
        child: Row(
          children: [
            if (asset != null) ...[
              Image.asset(
                asset,
                width: 16.w,
                height: 16.h,
                color: textColor,
              ),
              SizedBox(width: 2.w),
            ],
            CustomTextWidget(
              title: text,
              color: textColor,
              size: 12,
              height: null,
              fontWeight: FontWeight.w700,
            ),
            if (onTapText != null)
              CustomTextWidget(
                title: onTapText,
                color: textColor,
                size: 10,
                fontWeight: FontWeight.w500,
              ),
            if (isClickable == true) ...[
              SizedBox(width: 10.w),
              Icon(
                Icons.arrow_forward_ios_rounded,
                color: context.black,
                size: 10.sp,
              )
            ],
          ],
        ),
      ),
    );
  }
}

void userProfileInReviewBottomSheet(context) {
  showGeneralBottomSheet(
    context: context,
    icon: Assets.thrivvePhotosId,
    title: "your_account_is_under_review".tr,
    description: "account_verification_in_progress".tr,
    firstBtnOnClick: () {
      Get.back();
    },
    firstBtnText: "understood".tr,
    imageWidth: 40.w,
    imageHeight: 40.h,
  );
}

Widget buildFallbackAvatar(String? name, BuildContext context) {
  return Container(
    width: 38.w,
    height: 38.h,
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: context.appPrimaryColor,
    ),
    child: Center(
      child: Text(
        getFirstAndLastCharFromFullName(name ?? "") ?? "",
        style: TextStyle(
          color: Colors.white,
          fontSize: 12.sp,
          fontWeight: FontWeight.w400,
        ),
      ),
    ),
  );
}
