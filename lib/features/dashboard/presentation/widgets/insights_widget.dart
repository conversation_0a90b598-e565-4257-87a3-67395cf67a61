import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; // Import flutter_screenutil
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

import '../../../../core/util/const.dart';
import '../../domain/entities/insights.dart';

class InsightsWidget extends StatefulWidget {
  final Function(String value)? onPress;
  final Function(Insight value)? onTouch;

  final String? availableBalance;
  final String? currency;
  final String? date;
  final String? filterBy;
  final bool showFilter;
  final List<Insight>? listOfChart;

  const InsightsWidget({
    super.key,
    this.onPress,
    this.availableBalance,
    this.currency,
    this.filterBy,
    this.date = "21 Aug",
    this.showFilter = true,
    required this.listOfChart,
    this.onTouch,
  });

  @override
  State<InsightsWidget> createState() => _InsightsWidgetState();
}

class _InsightsWidgetState extends State<InsightsWidget> {
  final Duration animDuration = const Duration(milliseconds: 250);
  int touchedIndex = -1;
  late double maxValue;
  List<Insight>? listOfChart = [];

  @override
  void initState() {
    super.initState();
    print(
        "listOfChart: ${widget.listOfChart} lang : ${Get.locale?.languageCode}");
    listOfChart = Get.locale?.languageCode == valueArLanguage
        ? widget.listOfChart?.reversed.toList()
        : widget.listOfChart;
  }

  @override
  Widget build(BuildContext context) {
    maxValue = getMaxValue(listOfChart ?? []);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        buildTitle(),
        SizedBox(height: 8.h), // Use ScreenUtil to set height
        buildBarChart(),
      ],
    );
  }

  Widget buildTitle() {
    return CustomTextWidget(
      title: "insight".tr,
      size: 12, // Use ScreenUtil to set font size
      color: context.lightBlack,
      fontWeight: FontWeight.w400,
    );
  }

  Widget buildBarChart() {
    return SizedBox(
      height: 138.h, // Use ScreenUtil to set height
      child: BarChart(
        mainBarData(),
        swapAnimationDuration: animDuration,
      ),
    );
  }

  BarChartData mainBarData() {
    return BarChartData(
      alignment: BarChartAlignment.spaceBetween,
      barTouchData: buildBarTouchData(),
      titlesData: buildTitlesData(),
      borderData: FlBorderData(show: false),
      barGroups: showingGroups() ?? [],
      gridData: const FlGridData(show: false),
    );
  }

  BarTouchData buildBarTouchData() {
    return BarTouchData(
      touchTooltipData: buildTouchTooltipData(),
      touchCallback: (FlTouchEvent event, barTouchResponse) {
        setState(() {
          if (event.isInterestedForInteractions ||
              barTouchResponse == null ||
              barTouchResponse.spot == null) {
            touchedIndex = -1;
            return;
          }
          touchedIndex = barTouchResponse.spot!.touchedBarGroupIndex;
          widget.onTouch!(listOfChart![touchedIndex]);
        });
      },
    );
  }

  AxisTitles get doNotShowTitle =>
      const AxisTitles(sideTitles: SideTitles(showTitles: false));

  FlTitlesData buildTitlesData() {
    return FlTitlesData(
      show: true,
      rightTitles: doNotShowTitle,
      topTitles: doNotShowTitle,
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          getTitlesWidget: getTitles,
        ),
      ),
      leftTitles: doNotShowTitle,
    );
  }

  BarTouchTooltipData buildTouchTooltipData() {
    return BarTouchTooltipData(
        tooltipRoundedRadius: 2,
        tooltipPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
        // Use ScreenUtil to set padding
        tooltipBorder:
            BorderSide(color: context.appPrimaryColor!, width: 0.5.w),
        tooltipMargin: 2.w,
        // Use ScreenUtil to set margin
        getTooltipItem: (group, groupIndex, rod, rodIndex) {
          return BarTooltipItem(
            "${listOfChart?[groupIndex].value.toString()} ${widget.currency}",
            TextStyle(
              color: context.outlineButtonColor,
              fontWeight: FontWeight.w400,
              fontSize: 11.sp, // Use ScreenUtil to set font size
            ),
          );
        });
  }

  List<BarChartGroupData>? showingGroups() => listOfChart
      ?.map((e) => makeGroupData(
            x: listOfChart?.indexOf(e) ?? 0,
            y: e.percentage ?? 0.0,
            isTouched: listOfChart?.indexOf(e) == touchedIndex,
          ))
      .toList();

  Widget getTitles(double value, TitleMeta meta) => SideTitleWidget(
        axisSide: meta.axisSide,
        space: 4.w, // Use ScreenUtil to set space
        child: CustomTextWidget(
          title: listOfChart![value.toInt()].label.toString().split(",")[0],
          color: context.lightBlack,
          fontWeight: FontWeight.w400,
          size: 11, // Use ScreenUtil to set font size

          textAlign: TextAlign.center,
        ),
      );

  double getMaxValue(List<Insight> insights) {
    return insights
        .map((e) => e.percentage ?? 0.0)
        .reduce((a, b) => a > b ? a : b);
  }

  BarChartGroupData makeGroupData({
    required int x,
    required double y,
    bool isTouched = false,
    double width = 26,
    List<int> showTooltips = const [],
  }) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: y,
          borderRadius: BorderRadius.all(Radius.circular(2.r)),
          width: width.w,
          color: context.appPrimaryColor,
          borderSide: isTouched
              ? BorderSide(color: context.appPrimaryColor!, width: 2.w)
              : BorderSide(
                  color: context.containerColor ?? Colors.grey, width: 0.w),
          backDrawRodData: BackgroundBarChartRodData(
            show: true,
            fromY: 0,
            toY: maxValue != 0 ? maxValue : 1,
            // this is the max value = 1 for showing the background when the max value is 0
            color: context.insightsBackground,
          ),
        ),
      ],
      showingTooltipIndicators: showTooltips,
    );
  }
}
