import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/generated/assets.dart';

class UpdateAvailableWidget extends StatelessWidget {
  final String? title;
  final String? description;
  final String? buttonText;
  final Function()? onTap;
  final String? icon;

  const UpdateAvailableWidget({
    super.key,
    this.title = "New Release Available",
    this.description =
        "A new release has been shipped to the store! For a better user experience, please install the latest version.",
    this.buttonText = "Tap Here To Update Now",
    this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final balance = context.select((MainHomeBloc mainHomeBloc) =>
        mainHomeBloc.state.withdrawBalance?.withdrawBalance);
    return InkWell(
      onTap: () {
        if (onTap != null) {
          onTap!();
        }
      },
      child: Container(
        margin: EdgeInsets.only(
            right: 16.w, left: 16.w, top: (balance ?? 0) == 0 ? 16.h : 28.h),
        decoration: BoxDecoration(
          color: context.syncBoxBackground,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  !(icon?.isEmpty ?? true)
                      ? Text(
                          icon ?? "",
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 18.sp,
                          ),
                        )
                      : SvgPicture.asset(
                          Assets.assetsThrivvePhotosInfoIcon,
                          color: context.black,
                          height: 24,
                        ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title ?? "",
                          style: TextStyle(
                            color: context?.black,
                            fontWeight: FontWeight.w600,
                            fontSize: 13.sp,
                          ),
                        ),
                        SizedBox(height: 5.h),
                        CustomFormattedText(
                          textAlign: WrapAlignment.start,
                          text: description ?? "",
                          strongStyle: TextStyle(
                            fontSize: 12,
                            height: 1.5,
                            fontFamily: Get.locale?.languageCode == 'en'
                                ? "NotoSans"
                                : "NotoSansArabic",
                            color: context?.lightBlack,
                            fontWeight: FontWeight.w700,
                          ),
                          style: TextStyle(
                            fontSize: 12,
                            fontFamily: Get.locale?.languageCode == 'en'
                                ? "NotoSans"
                                : "NotoSansArabic",
                            height: 1.5,
                            color: context?.lightBlack,
                            fontWeight: FontWeight.w400,
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              height: 42.h,
              decoration: BoxDecoration(
                color: context.syncBoxBtnBackground,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20.r),
                  bottomRight: Radius.circular(20.r),
                ),
              ),
              child: Center(
                child: Text(
                  buttonText ?? "",
                  style: TextStyle(
                    color: context.black,
                    fontWeight: FontWeight.w600,
                    fontSize: 13.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
