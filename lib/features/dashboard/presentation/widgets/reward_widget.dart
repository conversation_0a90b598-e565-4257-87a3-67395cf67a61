import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

class RewardsBottomSheet extends StatelessWidget {
  const RewardsBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: context.bottomsheetColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: Stack(
        children: [
          // Close button at top right
          PositionedDirectional(
            top: 16.h,
            start: 16.w,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                width: 24.w,
                height: 24.w,
                decoration: BoxDecoration(
                  color: context.appBackgroundColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(Icons.close, size: 16.w, color: context.black),
                ),
              ),
            ),
          ),

          // Main content
          Padding(
            padding: EdgeInsets.only(top: 48.h),
            child: Column(
              children: [
                // Scrollable content
                SizedBox(
                  height: 10.h,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Introduction text
                        CustomTextWidget(
                          title: 'rewards_intro'.tr,
                          size: 10,
                          height: 1.4,
                          color: context.black,
                          paddingBottom: 6.h,
                          fontWeight: FontWeight.w600,
                        ),

                        // Available Packages
                        _buildSection(
                          context,
                          title: '',
                          items: [
                            'package_1'.tr,
                            'package_2'.tr,
                            'package_3'.tr,
                          ],
                        ),

                        // Eligibility Requirements
                        _buildSection(
                          context,
                          title: 'eligibility_requirements'.tr,
                          items: [
                            'requirement_work_hours'.tr,
                            'requirement_weekly_hours'.tr,
                            'requirement_acceptance_rate'.tr,
                            'requirement_monthly_trips'.tr,
                            'requirement_cancellation_rate'.tr,
                            'requirement_rating'.tr,
                            'requirement_car_installments'.tr,
                            'requirement_performance'.tr,
                            'requirement_program'.tr,
                          ],
                        ),

                        // Important Notes
                        _buildSection(
                          context,
                          title: 'important_notes'.tr,
                          items: [
                            'note_income_calculation'.tr,
                            'note_weekly_hours'.tr,
                          ],
                        ),

                        SizedBox(height: 80.h), // Space for button
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Fixed button at bottom
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsetsDirectional.only(
                start: 16.w,
                end: 16.w,
                bottom: 32.h,
              ),
              decoration: BoxDecoration(
                color: context.bottomsheetColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    offset: const Offset(0, -2),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: CustomButton(
                text: 'got_it'.tr,
                colorText: Colors.white,
                onPressed: () => Navigator.pop(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<String> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title.isNotEmpty)
          CustomTextWidget(
            title: title,
            size: 10,
            fontWeight: FontWeight.w600,
            paddingBottom: 8.h,
            color: context.black,
          ),
        ...items.map((item) => _buildBulletPoint(
              item,
              context,
            )),
        SizedBox(height: 24.h),
      ],
    );
  }

  Widget _buildBulletPoint(String text, BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.only(start: 8.w, top: 6.h),
            child: Container(
              width: 4.w,
              height: 4.w,
              decoration: BoxDecoration(
                color: context.black,
                shape: BoxShape.circle,
              ),
            ),
          ),
          SizedBox(width: 2.sp),
          Expanded(
            child: CustomTextWidget(
              title: text,
              size: 10,
              height: 1.4,
              color: context.black.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}

void showRewardsBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: context.bottomsheetColor,
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 20.h,
      ),
      child: const RewardsBottomSheet(),
    ),
  );
}
