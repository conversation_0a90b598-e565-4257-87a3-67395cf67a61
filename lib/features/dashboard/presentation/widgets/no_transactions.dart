import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

class NoTransactions extends StatelessWidget {
  final Function() onTap;

  const NoTransactions({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 46,
            height: 46,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: context.containerColor,
            ),
            child: Icon(
              Icons.arrow_upward_rounded,
              color: context.black,
            ),
          ),
          const SizedBox(
            height: 16,
          ),
          CustomTextWidget(
            title: "no_transactions_msj".tr,
            size: 14,
            fontWeight: FontWeight.w500,
            color: context.black,
          ),
          const SizedBox(
            height: 8,
          ),
          CustomTextWidget(
            title: "create_first_transaction_msj".tr,
            size: 12,
            fontWeight: FontWeight.w400,
            color: context.hintTextColor2,
          ),
          const SizedBox(
            height: 8,
          ),
          InkWell(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: context.appPrimaryColor,
                borderRadius: BorderRadius.circular(16.0),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.arrow_upward_rounded,
                    color: context.whiteColor,
                    size: 14,
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  CustomTextWidget(
                    title: "send".tr,
                    color: context.whiteColor,
                    size: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
