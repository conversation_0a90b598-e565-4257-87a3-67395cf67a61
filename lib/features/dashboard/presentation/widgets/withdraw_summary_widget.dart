import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; // Import flutter_screenutil
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/wallet_item.dart';

import '../../../../core/util/const.dart';
import '../../../../core/util/date_util.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/transaction.dart';

class WithdrawSummaryWidget extends StatelessWidget {
  final Transaction? transaction;
  final bool? isWithdrawRequestSummary;

  const WithdrawSummaryWidget({
    super.key, // Fix the super.key
    this.transaction,
    this.isWithdrawRequestSummary = false,
  }); // Fix the super.key

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 0.w, right: 0.w),
      // Use ScreenUtil to set margin
      child: Stack(
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(bottom: 64.h, right: 8.w),
            // Use ScreenUtil to set margin
            decoration: BoxDecoration(
              color: context.bottomsheetColor,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(16.r),
              // Use ScreenUtil to set border radius
              boxShadow: <BoxShadow>[
                BoxShadow(
                  color: context.shadowColor,
                  blurRadius: 0.0,
                  offset: Offset(0.0, 0.0),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(16.w),
                  // Use ScreenUtil to set padding
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Row(
                          children: [
                            Stack(children: [
                              isUberOrAmazon(transaction?.iconType)
                                  ? Container(
                                      alignment: Alignment.center,
                                      height: 40.h,
                                      width: 40.w,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                      ),
                                      child: Image.asset(
                                        getIcon(
                                          transaction?.iconType,
                                        ),
                                      ),
                                    )
                                  : Container(
                                      alignment: Alignment.center,
                                      height: 40.h,
                                      width: 40.w,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: context.appBackgroundColor,
                                      ),
                                      child: Image.asset(
                                        getIcon(
                                          transaction?.iconType,
                                        ),
                                        width: 20.w,
                                        height: 20.h,
                                        color: context.blackIconColor,
                                      ),
                                    ),
                              Visibility(
                                visible: isRejected(transaction?.statusKey),
                                child: Positioned(
                                  right: 1.w, // Use ScreenUtil to set right
                                  bottom: 1.h, // Use ScreenUtil to set bottom
                                  child: Icon(
                                    Icons.cancel_rounded,
                                    color: Colors.red,
                                    size: 16.sp, // Use ScreenUtil to set size
                                  ),
                                ),
                              ),
                            ]),
                            SizedBox(
                              width: 8.w, // Use ScreenUtil to set width
                            ),
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomTextWidget(
                                    title: transaction?.title ?? "",
                                    size: 14,
                                    // Use ScreenUtil to set font size
                                    fontWeight: FontWeight.w500,
                                    color: context.black,
                                  ),
                                  SizedBox(height: 8.h),
                                  CustomTextWidget(
                                      title: DateUtil.getDateFormat(
                                              transaction?.creation) ??
                                          "",
                                      size: 12,
                                      // Use ScreenUtil to set font size
                                      fontWeight: FontWeight.w400,
                                      color: context.lightBlack),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 8.w, // Use ScreenUtil to set width
                      ),
                      CustomTextWidget(
                          title: transaction?.totalAmountWithFees ?? "",
                          size: 12,
                          // Use ScreenUtil to set font size
                          fontWeight: FontWeight.w500,
                          color: context.black),
                    ],
                  ),
                ),
                SizedBox(
                  width: 16.w, // Use ScreenUtil to set width
                ),
                Divider(
                  height: 1.h, // Use ScreenUtil to set height
                ),
                SizedBox(
                  height: 24.h, // Use ScreenUtil to set height
                ),
                Padding(
                  padding: EdgeInsets.only(right: 16.w, left: 16.w),
                  // Use ScreenUtil to set padding
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomTextWidget(
                        title: "reference_id".tr,
                        color: context?.black,
                        size: 14, // Use ScreenUtil to set font size
                        fontWeight: FontWeight.w400,
                      ),
                      CustomTextWidget(
                        title: transaction?.referenceId ?? "",
                        color: context?.hintTextColor2,
                        size: 14, // Use ScreenUtil to set font size
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                ),
                if (transaction?.status != null) ...[
                  SizedBox(
                    height: 24.h, // Use ScreenUtil to set height
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 16.w, left: 16.w),
                    // Use ScreenUtil to set padding
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomTextWidget(
                          title: "status".tr,
                          color: context.black,
                          size: 14, // Use ScreenUtil to set font size
                          fontWeight: FontWeight.w400,
                        ),
                        Container(
                          padding: EdgeInsets.all(8.w),
                          // Use ScreenUtil to set padding
                          decoration: BoxDecoration(
                              color: isPending(transaction?.statusKey)
                                  ? context.orangeTextColor!.withOpacity(0.12)
                                  : context.notificationBackgroundColor,
                              borderRadius: BorderRadius.circular(8.r)),
                          // Use ScreenUtil to set border radius
                          child: CustomTextWidget(
                            title: transaction?.status ?? "",
                            color: isPending(transaction?.statusKey)
                                ? context.orangeTextColor
                                : context.secondaryColor,
                            size: 10,
                            // Use ScreenUtil to set font size
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      ],
                    ),
                  ),
                ],
                Visibility(
                  visible: isWithdrawRequestSummary == false,
                  child: Column(
                    children: [
                      if (transaction?.totalAmountWithFees != null) ...[
                        SizedBox(
                          height: 24.h, // Use ScreenUtil to set height
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 16.w, left: 16.w),
                          // Use ScreenUtil to set padding
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomTextWidget(
                                title: "amount".tr,
                                color: context.black,
                                size: 14,
                                // Use ScreenUtil to set font size
                                fontWeight: FontWeight.w400,
                              ),
                              CustomTextWidget(
                                title: transaction?.totalAmountWithFees ?? "",
                                color: context.hintTextColor2,
                                size: 14,
                                // Use ScreenUtil to set font size
                                fontWeight: FontWeight.w400,
                              )
                            ],
                          ),
                        ),
                      ],
                      if (transaction?.fees != null) ...[
                        SizedBox(
                          height: 24.h, // Use ScreenUtil to set height
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 16.w, left: 16.w),
                          // Use ScreenUtil to set padding
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomTextWidget(
                                title: "transaction_fee".tr,
                                color: context.black,
                                size: 14,
                                // Use ScreenUtil to set font size
                                fontWeight: FontWeight.w400,
                              ),
                              CustomTextWidget(
                                title: transaction?.fees ?? "",
                                color: context.hintTextColor2,
                                size: 14,
                                // Use ScreenUtil to set font size
                                fontWeight: FontWeight.w400,
                              )
                            ],
                          ),
                        ),
                      ],
                      if ((transaction?.description ?? '').isNotEmpty) ...[
                        SizedBox(
                          height: 24.h, // Use ScreenUtil to set height
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 16.w, left: 16.w),
                          // Use ScreenUtil to set padding
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "remarks".tr,
                                style: TextStyle(
                                  color: context.black,
                                  fontSize: 14.sp,
                                  // Use ScreenUtil to set font size
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Expanded(
                                child: Text(
                                  transaction?.description ?? "",
                                  textAlign: TextAlign.end,
                                  style: TextStyle(
                                    color: context.hintTextColor2,
                                    fontSize: 14.sp,
                                    // Use ScreenUtil to set font size
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Visibility(
                  visible: isWithdrawRequestSummary == true,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 24.h, // Use ScreenUtil to set height
                      ),
                      Divider(
                        height: 1.h, // Use ScreenUtil to set height
                      ),
                      SizedBox(
                        height: 24.h, // Use ScreenUtil to set height
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 16.w, left: 16.w),
                        // Use ScreenUtil to set margin
                        padding: EdgeInsets.only(
                            right: 16.w, left: 16.w, top: 8.h, bottom: 8.h),
                        // Use ScreenUtil to set padding
                        decoration: BoxDecoration(
                            color: context.messageBackgroundColor,
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.r))),
                        // Use ScreenUtil to set border radius
                        child: Row(
                          children: [
                            Image.asset(
                              Assets.thrivvePhotosTime,
                              width: 20.w, // Use ScreenUtil to set width
                              height: 30.h, // Use ScreenUtil to set height
                            ),
                            SizedBox(
                              width: 8.w, // Use ScreenUtil to set width
                            ),
                            Expanded(
                              child: Text(
                                transaction?.message ?? "",
                                style: TextStyle(
                                    color: context.messageColor,
                                    fontSize: 12.sp,
                                    // Use ScreenUtil to set font size
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 24.h, // Use ScreenUtil to set height
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 0.0,
            right: 0.0,
            left: 0.0,
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Center(
                child: FloatingActionButton(
                  backgroundColor: context.bottomsheetColor,
                  onPressed: () {
                    Get.back();
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(90.r),
                  ),
                  mini: true,
                  elevation: 5.0,
                  child: Icon(Icons.close, color: context.black),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
