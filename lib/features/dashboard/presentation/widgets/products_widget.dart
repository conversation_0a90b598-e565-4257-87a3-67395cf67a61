import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/section_header_widget.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../products/presentation/widgets/list_of_products_vertical_widget.dart';
import '../../domain/entities/product.dart';
import 'list_of_products_horizontal_widget.dart';

class ProductsWidget extends StatelessWidget {
  final bool showAsListview; // can be gridview
  final List<Product> listOfProducts;
  final PersonalInfoEntity? personalInfo;
  final bool? isInstantPaymentIsEnable;
  final bool? showAllProducts;
  final bool? showTitle;

  const ProductsWidget(
      {super.key,
      required this.showAsListview,
      required this.listOfProducts,
      required this.personalInfo,
      required this.isInstantPaymentIsEnable,
      this.showTitle = false,
      this.showAllProducts = false});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle == true)
          SectionHeaderWidget(
            onTab: () {
              getIt<IAnalyticsLogger>()
                  .logEvent(AnalyticsActions.browseProducts, parameters: {
                AnalyticsActions.screenNameParams: AnalyticsActions.dashboard
              });
              Get.toNamed(AppRoutes.productsPage, arguments: {
                "personalInfo": personalInfo,
                "canWithdraw": isInstantPaymentIsEnable,
              });
            },
            title: "do_more_with_thrivve".tr,
          ),
        SizedBox(height: showTitle == true ? 16.h : 0),
        showAsListview == false
            ? ListOfProductsVerticalWidget(
                // gridview
                listOfProducts: listOfProducts,
                personalInfo: personalInfo,
                isInstantPaymentIsEnable: isInstantPaymentIsEnable,
              )
            : ListOfProductsHorizontalWidget(
                // vertical or herozintal
                listOfProducts: listOfProducts,
                personalInfo: personalInfo,
                canWithdraw: isInstantPaymentIsEnable,
                scrollDirection:
                    showAllProducts == true ? Axis.vertical : Axis.horizontal,
              ),
      ],
    );
  }
}
