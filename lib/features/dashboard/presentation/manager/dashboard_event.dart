part of 'dashboard_bloc.dart';

@immutable
abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

class CompleteYourApplicationEvent extends DashboardEvent {
  final String? url;

  const CompleteYourApplicationEvent({this.url});

  @override
  List<Object?> get props => [url];
}

class GetWidgetNotificationEvent extends DashboardEvent {
  const GetWidgetNotificationEvent();

  @override
  List<Object?> get props => [];
}

class GetUnderProcessingListEvent extends DashboardEvent {
  const GetUnderProcessingListEvent();

  @override
  List<Object?> get props => [];
}

class GetLastProgress extends DashboardEvent {
  const GetLastProgress();

  @override
  List<Object?> get props => [];
}

class CreateWorkWithUberApplicationEvent extends DashboardEvent {
  final Map<String, dynamic> input;
  const CreateWorkWithUberApplicationEvent({
    required this.input,
  });

  @override
  List<Object?> get props => [
        input,
      ];
}

class OnboardingDialogEvent extends DashboardEvent {
  const OnboardingDialogEvent();

  @override
  List<Object?> get props => [];
}

class SetOnboardingDialogEvent extends DashboardEvent {
  final String? key;

  const SetOnboardingDialogEvent({this.key});

  @override
  List<Object?> get props => [key];
}

class GetTransactionsEvent extends DashboardEvent {
  const GetTransactionsEvent();

  @override
  List<Object?> get props => [];
}

class GetProductsEvent extends DashboardEvent {
  final int numberOfProducts;

  const GetProductsEvent({this.numberOfProducts = 4});

  @override
  List<Object?> get props => [numberOfProducts];
}

// get transactions
class GetListOfTransactionsEvent extends DashboardEvent {}

class GetInsightsEvent extends DashboardEvent {
  const GetInsightsEvent();

  @override
  List<Object?> get props => [];
}

class StarterEvent extends DashboardEvent {
  const StarterEvent();

  @override
  List<Object?> get props => [];
}

class CloseSyncNotificationEvent extends DashboardEvent {
  const CloseSyncNotificationEvent();

  @override
  List<Object?> get props => [];
}

class ClickLeaseToOwnSoonEvent extends DashboardEvent {}

class UpdateTheAppEvent extends DashboardEvent {
  final String? url;

  const UpdateTheAppEvent({
    this.url,
  });

  @override
  List<Object?> get props => [
        url,
      ];
}

class GetTopUpCreationData extends DashboardEvent {
  const GetTopUpCreationData();

  @override
  List<Object?> get props => [];
}

class GetNewDashboardProductsEvent extends DashboardEvent {
  const GetNewDashboardProductsEvent();

  @override
  List<Object?> get props => [];
}

class SetHomePageMessageNullEvent extends DashboardEvent {
  const SetHomePageMessageNullEvent();

  @override
  List<Object?> get props => [];
}
