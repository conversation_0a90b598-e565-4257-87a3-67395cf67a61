import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/features/dashboard/data/models/work_with_uber_all_data_model.dart';
import 'package:thrivve/features/dashboard/domain/entities/last_progress_entity.dart';
import 'package:thrivve/features/dashboard/domain/entities/message_entity.dart';
import 'package:thrivve/features/dashboard/domain/entities/new_dashboard_products_entity.dart';
import 'package:thrivve/features/dashboard/domain/enum/application_type_enum.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/click_drive_to_own_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_dashboard_new_products_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_last_progress_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_work_with_uber_all_data_use_case.dart';
import 'package:thrivve/features/topUp/domain/entities/top_up.dart';
import 'package:thrivve/features/under_processing/domain/entities/input_under_processing_entity.dart';
import 'package:thrivve/features/under_processing/domain/entities/under_processing_entity.dart';
import 'package:thrivve/features/under_processing/domain/use_cases/get_list_under_processing_use_case.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/localDataSource/user_secure_data_source.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/const.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../topUp/domain/use_cases/get_top_up_creation_data_use_case.dart';
import '../../../withdraw/domain/entities/onboarding_item.dart';
import '../../../withdraw/domain/use_cases/get_onboarding_settings_use_case.dart';
import '../../domain/entities/banners.dart';
import '../../domain/entities/insights.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/transaction.dart';
import '../../domain/use_cases/get_insights_use_case.dart';
import '../../domain/use_cases/get_products_use_case.dart';
import '../../domain/use_cases/get_sync_info_use_case.dart';
import '../../domain/use_cases/get_transactions_use_case.dart';
import '../../domain/use_cases/home_page_message_use_case.dart';
import '../../domain/use_cases/set_sync_info_seen_use_case.dart';
import '../pages/my_web_view_widget_2.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetTransactionsUseCase getTransactionsUseCase;
  final HomePageMessageUseCase getHomePageMessageUseCase;
  final GetInsightsUseCase getInsightsUseCase;
  final GetProductsUseCase getProductsUseCase;
  final GetSyncInfoUseCase getSyncInfoUseCase;
  final SetSyncInfoSeenUseCase setSyncInfoSeenUseCase;
  final UserSecureDataSource? userSecureDataSource;
  final GetOnboardingSettingUseCase getOnboardingSettingUseCase;
  final GetTopUpCreationDataUseCase getTopUpCreationDataUseCase;
  final GetListUnderProcessingUseCase getListUnderProcessingUseCase;
  final GetWorkWithUberAllDataUseCase getWorkWithUberAllDataUseCase;
  final GetLastProgressUseCase getLastProgressUseCase;
  final GetDashboardNewProductsUseCase getDashboardNewProductsUseCase;
  final ClickDriveToOwnUseCase clickDriveToOwnUseCase;
  static const _numberOfTransactionsByDefault = 4;
  static const _numberOfTransactionsIfThereIsNoProducts = 6;
  static const _numberOfUnderProcessing = 1000;

  DashboardBloc({
    required this.getTransactionsUseCase,
    required this.getInsightsUseCase,
    required this.getProductsUseCase,
    required this.userSecureDataSource,
    required this.getSyncInfoUseCase,
    required this.getOnboardingSettingUseCase,
    required this.setSyncInfoSeenUseCase,
    required this.getTopUpCreationDataUseCase,
    required this.getHomePageMessageUseCase,
    required this.getListUnderProcessingUseCase,
    required this.getWorkWithUberAllDataUseCase,
    required this.getLastProgressUseCase,
    required this.getDashboardNewProductsUseCase,
    required this.clickDriveToOwnUseCase,
  }) : super(const DashboardState()) {
    on<GetListOfTransactionsEvent>(_onGetListOfTransactionsCalled);
    on<GetInsightsEvent>(_onGetInsightsCalled);
    on<GetProductsEvent>(_onGetProductsCalled);
    on<StarterEvent>(_onStarterEventCalled);
    on<CloseSyncNotificationEvent>(_onCloseSyncNotificationEventCalled);
    on<UpdateTheAppEvent>(_onUpdateTheAppEventCalled);
    on<OnboardingDialogEvent>(_onOnboardingDialogEvent);
    on<GetTopUpCreationData>(_onGetTopUpCreationData);
    on<SetOnboardingDialogEvent>(_onSetOnboardingDialogEvent);
    on<CompleteYourApplicationEvent>(_onCompleteYourApplicationEvent);
    on<GetWidgetNotificationEvent>(_onGetWidgetNotificationEvent);
    on<GetUnderProcessingListEvent>(_onGetUnderProcessingListEvent);
    on<GetLastProgress>(_onGetLastProgress);
    on<GetNewDashboardProductsEvent>(_onGetNewProductsCalled);
    on<SetHomePageMessageNullEvent>(_onSetHomePageMessageNull);
    on<ClickLeaseToOwnSoonEvent>(_onClickDriveToOwnComingSoon);
  }
  Future<void> _onClickDriveToOwnComingSoon(
    ClickLeaseToOwnSoonEvent event,
    Emitter<DashboardState> emit,
  ) async {
    final isLogin = await userSecureDataSource?.isLogin();
    if (isLogin == false) {
      return;
    }
    await clickDriveToOwnUseCase(NoParams());
  }

  Future<void> _onGetLastProgress(
    GetLastProgress event,
    Emitter<DashboardState> emit,
  ) async {
    // emit(state.copyWith(getLastProgressStatus: () => AppStatus.loading));
    //
    // final getLastProgressData = await getLastProgressUseCase(
    //   NoParams(),
    // );
    // getLastProgressData?.fold(
    //   (failure) {
    //     emit(
    //       state.copyWith(
    //         getLastProgressStatus: () => AppStatus.failure,
    //         errorMessage: () => mapFailureToMessage(failure),
    //       ),
    //     );
    //   },
    //   (data) {
    //     emit(
    //       state.copyWith(
    //         getLastProgressStatus: () => AppStatus.success,
    //         lastProgressEntity: () => data,
    //       ),
    //     );
    //   },
    // );
  }

  Future<void> _onGetUnderProcessingListEvent(
    GetUnderProcessingListEvent event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(getUnderProcessingStatus: () => AppStatus.loading));

    final getListUnderProcessingUseCaseData =
        await getListUnderProcessingUseCase(
      InputUnderProcessingEntity(
        page: 0,
        perPage: _numberOfUnderProcessing,
      ),
    );
    getListUnderProcessingUseCaseData?.fold((failure) {
      emit(state.copyWith(
        getUnderProcessingStatus: () => AppStatus.failure,
        errorMessage: () => mapFailureToMessage(failure),
      ));
    }, (data) {
      emit(state.copyWith(
        getUnderProcessingStatus: () => AppStatus.success,
        listUnderProcessing: () => data ?? [],
      ));
    });
  }

  Future<void> _onGetWidgetNotificationEvent(
    GetWidgetNotificationEvent event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(
      getHomePageMessageStatus: () => AppStatus.loading,
    ));

    try {
      // First get the home page message
      final result = await getHomeMessage();
      if (result == null) {
        emit(state.copyWith(
          getHomePageMessageStatus: () => AppStatus.failure,
          errorMessage: () => 'Failed to get home page message',
        ));
        return;
      }

      await result.fold(
        (failure) {
          emit(state.copyWith(
            getHomePageMessageStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure),
          ));
        },
        (homePageData) async {
          // Only get work with uber data if we have a valid
          ApplicationTypeEnum? applicationType =
              homePageData?.applicationTypeEnum;

          if (homePageData?.idApplication != null &&
              applicationType == ApplicationTypeEnum.Uber) {
            final workWithUberResult = await getWorkWithUberRequest();

            if (workWithUberResult == null) {
              emit(state.copyWith(
                getHomePageMessageStatus: () => AppStatus.failure,
                errorMessage: () => 'Failed to get work with uber data',
              ));
              return;
            }
            workWithUberResult.fold(
              (failure) {
                emit(state.copyWith(
                  getHomePageMessageStatus: () => AppStatus.failure,
                  errorMessage: () => mapFailureToMessage(failure),
                ));
              },
              (workWithUberData) {
                emit(state.copyWith(
                  getHomePageMessageStatus: () => AppStatus.success,
                  homePageMessage: () => homePageData,
                  dataWorkWithUber: () => workWithUberData,
                ));
              },
            );
          } else {
            // If no idApplication, just emit the home page message
            emit(state.copyWith(
              getHomePageMessageStatus: () => AppStatus.success,
              homePageMessage: () => homePageData,
              dataWorkWithUber: () => null,
            ));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        getHomePageMessageStatus: () => AppStatus.failure,
        errorMessage: () => 'An unexpected error occurred',
      ));
    }
  }

  Future<void> _onCompleteYourApplicationEvent(
    CompleteYourApplicationEvent event,
    Emitter<DashboardState> emit,
  ) async {
    final isSheet = (event.url ?? '').contains('sheet');
    var token = await userSecureDataSource?.getAccessToken();
    var leadId = await userSecureDataSource?.getLeadId();
    if (isSheet) {
      await MyWebViewWidget2.showAsBottomSheet(
        context: Get.context!,
        url: event.url,
        token: token ?? '',
        leadId: leadId ?? '',
      );
    } else {
      Get.to(() => MyWebViewWidget2(
            url: event.url,
            token: token ?? "",
            leadId: leadId ?? "",
          ))?.then((value) {
        if (value != null && value is bool && value) {
          add(GetWidgetNotificationEvent());
        } else if (value != null && value is String && value == "topUpPage") {
          add(const GetTopUpCreationData());
        }
      });
    }
  }

  Future<void> _onSetOnboardingDialogEvent(
      SetOnboardingDialogEvent event, Emitter<DashboardState> emit) async {
    if (event.key == "showProfileOnboardingDialog") {
      userSecureDataSource?.setProfileOnboardingForFirstTimeDone();
    } else if (event.key == "showTransactionOnboardingDialog") {
      userSecureDataSource?.setTransactionOnboardingForFirstTimeDone();
    }
  }

  Future<void> _onGetTopUpCreationData(
      GetTopUpCreationData event, Emitter<DashboardState> emit) async {
    emit(state.copyWith(
      getTopUpStatus: () => AppStatus.loading,
    ));

    final result = await getTopUpCreationDataUseCase(NoParams());
    result.fold(
      (failure) {
        emit(state.copyWith(
          getTopUpStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (topUp) {
        emit(state.copyWith(
          getTopUpStatus: () => AppStatus.success,
          topUp: () => topUp,
        ));
      },
    );
  }

  Future<void> _onOnboardingDialogEvent(
      OnboardingDialogEvent event, Emitter<DashboardState> emit) async {
    final isTransactionOnboardingForFirstTime =
        userSecureDataSource?.isTransactionOnboardingForFirstTime();
    if (isTransactionOnboardingForFirstTime == true) {
      if (state.showProfileOnboardingDialog != true) {
        // check if profile onboarding is done then show transaction onboarding
        var onboardingSettings = await getOnboardingSettingUseCase(
          OnboardingParams(key: viewTransactionOnboardingKey),
        );

        onboardingSettings.fold(
          (failure) {
            emit(state.copyWith(
              errorMessage: () => mapFailureToMessage(failure),
            ));
          },
          (onboardingItems) async {
            emit(state.copyWith(
              transactionOnboardingItem: () => onboardingItems?.first,
              showTransactionOnboardingDialog: () => true,
            ));
          },
        );
      }
    }
  }

  Future<void> _onUpdateTheAppEventCalled(
    UpdateTheAppEvent event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      await launchURL(
          url: event.url ?? '', mode: LaunchMode.externalApplication);
    } catch (e) {
      print("error: $e");
      errorSnackBar(
        context: Get.context!,
        title: "err".tr,
        message: e.toString(),
      );
    }
  }

  Future<void> _onCloseSyncNotificationEventCalled(
    CloseSyncNotificationEvent event,
    Emitter<DashboardState> emit,
  ) async {
    // userSecureDataSource?.setSyncNotification(false);
    final setSyncInfoSeenUseCaseData = await setSyncInfoSeenUseCase(NoParams());

    setSyncInfoSeenUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(),
            ), (data) {
      emit(state.copyWith(
          homePageMessage: () => null,
          getHomePageMessageStatus: () =>
              AppStatus.failure // to dismiss the sync notification
          ));
    });
  }

  Future<void> _onStarterEventCalled(
    StarterEvent event,
    Emitter<DashboardState> emit,
  ) async {
    final isProfileOnboardingForFirstTime =
        userSecureDataSource?.isProfileOnboardingForFirstTime();
    if (isProfileOnboardingForFirstTime == true) {
      emit(state.copyWith(
        showProfileOnboardingDialog: () => true,
      ));
    }

    // var isNewVersionAvailable = await checkIfThereAreVersionUpdate();
    // var appUpdateMessage = await getAppUpdateObject();
    //
    // if (isNewVersionAvailable) {
    //   var appUpdateLink = getUpdateLink();
    //   emit(state.copyWith(
    //     showUpdateAvailable: () => true,
    //     appUpdateLink: () => appUpdateLink,
    //     appUpdateMessage: () => appUpdateMessage,
    //   ));
    // }
    add(GetWidgetNotificationEvent());

    // final getSyncInfoUseCaseData = getSyncInfoUseCase(NoParams());
    // await getSyncInfoUseCaseData.then((value) => value?.fold(
    //         (failure) => emit(
    //               state.copyWith(),
    //             ), (data) async {
    //       if (data != null) {
    //         emit(state.copyWith(
    //           syncNotification: () => data,
    //           showSyncStatusUpdate: () => true,
    //           showUpdateAvailable: () => false,
    //         ));
    //       } else {
    //         var isNewVersionAvailable = await checkIfThereAreVersionUpdate();
    //         var appUpdateMessage = await getAppUpdateObject();
    //
    //         if (isNewVersionAvailable) {
    //           var appUpdateLink = getUpdateLink();
    //           emit(state.copyWith(
    //             showUpdateAvailable: () => isNewVersionAvailable,
    //             appUpdateLink: () => appUpdateLink,
    //             appUpdateMessage: () => appUpdateMessage,
    //           ));
    //         }
    //       }
    //     }));

    // var syncNotification = await checkIfThereAreSyncStatusUpdate();
    // if (syncNotification != null) {
    //   emit(state.copyWith(
    //     showSyncStatusUpdate: () => true,
    //     syncNotification: () => syncNotification,
    //   ));
    // } else {
    //   var isNewVersionAvailable = await checkIfThereAreVersionUpdate();
    //   var appUpdateMessage = await getAppUpdateObject();
    //   if (isNewVersionAvailable) {
    //     var appUpdateLink = getUpdateLink();
    //     emit(state.copyWith(
    //       showUpdateAvailable: () => isNewVersionAvailable,
    //       appUpdateLink: () => appUpdateLink,
    //       appUpdateMessage: () => appUpdateMessage,
    //     ));
    //   }
    // }
  }

  // Future<AppUpdateData> getAppUpdateObject() async {
  //   var userData = userSecureDataSource?.getUserData();
  //
  //   return AppUpdateData(
  //     title: userData?.appUpdateAvailableTitle,
  //     message: userData?.appUpdateAvailableMessage,
  //     buttonText: userData?.appUpdateAvailableTitleButton,
  //   );
  // }
  //
  // Future<bool> checkIfThereAreVersionUpdate() async {
  //   var userData = userSecureDataSource?.getUserData();
  //   String platform = getPlatForm().toLowerCase();
  //   var versionPlatform = await PackageInfo.fromPlatform();
  //   var appVersion = versionPlatform.version;
  //   var isNewVersionAvailableVariable = false;
  //   if (platform == 'ios') {
  //     isNewVersionAvailableVariable =
  //         isNewVersionAvailable(appVersion, userData!.latestIosVersion!);
  //   } else if (platform == 'android') {
  //     isNewVersionAvailableVariable =
  //         isNewVersionAvailable(appVersion, userData!.latestAndroidVersion!);
  //   }
  //
  //   return isNewVersionAvailableVariable;
  // }
  //
  // Future<bool> checkIfThereAreVersionUpdateV2({
  //   required String android,
  //   required String ios,
  // }) async {
  //   String platform = getPlatForm().toLowerCase();
  //   var versionPlatform = await PackageInfo.fromPlatform();
  //   var appVersion = versionPlatform.version;
  //   var isNewVersionAvailableVariable = false;
  //   if (platform == 'ios') {
  //     isNewVersionAvailableVariable =
  //         isNewVersionAvailable(appVersion, android);
  //   } else if (platform == 'android') {
  //     isNewVersionAvailableVariable = isNewVersionAvailable(appVersion, ios);
  //   }
  //
  //   return isNewVersionAvailableVariable;
  // }

// Future<SyncNotification?> checkIfThereAreSyncStatusUpdate() async {
//   bool? showSyncStatusUpdate =
//       userSecureDataSource?.getSyncNotification();
//   final syncNotificationData =
//       userSecureDataSource?.getSyncNotificationData();
//   if (showSyncStatusUpdate != null && showSyncStatusUpdate) {
//     return syncNotificationData;
//   }
//   return null;
// }

  Future<void> _onGetInsightsCalled(
    GetInsightsEvent event,
    Emitter<DashboardState> emit,
  ) async {
    printInfo(info: "GetInsightsEvent called");
    emit(state.copyWith(
      getInsightsStatus: () => AppStatus.loading,
    ));

    final getInsightsUseCaseData = await getInsightsUseCase(NoParams());
    printInfo(info: "getInsightsUseCaseData: $getInsightsUseCaseData result");
    getInsightsUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                  getInsightsStatus: () => AppStatus.failure,
                  errorMessage: () => mapFailureToMessage(failure)),
            ), (data) {
      emit(state.copyWith(
        getInsightsStatus: () => AppStatus.success,
        insights: () => data!,
      ));
    });
  }

  Future<void> _onGetProductsCalled(
    GetProductsEvent event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(
      getProductsStatus: () => AppStatus.loading,
    ));
    final getProductsUseCaseData = await getProductsUseCase(
        ProductsParams(page: 1, perPage: event.numberOfProducts));

    getProductsUseCaseData?.fold((failure) {
      emit(
        state.copyWith(
            getProductsStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure)),
      );
    }, (data) {
      emit(state.copyWith(
        getProductsStatus: () => AppStatus.success,
        listOfProducts: () => data?.products
            ?.where((element) => element.isActive == true)
            .toList(),
      ));
    });
  }

  Future<void> _onGetListOfTransactionsCalled(
    GetListOfTransactionsEvent event,
    Emitter<DashboardState> emit,
  ) async {
    printInfo(info: "GetListOfTransactionsEvent called");
    emit(state.copyWith(
      getListOfTransactionsStatus: () => AppStatus.loading,
    ));

    var getTransactionsUseCaseData = getTransactionsUseCase(TransactionsParams(
      status: withdrawTransactionsApprovedStatus,
      page: 1,
      perPage: _numberOfTransactionsByDefault,
    ));
    printInfo(
        info: "getTransactionsUseCaseData: $getTransactionsUseCaseData result");

    await getTransactionsUseCaseData.then((value) => value?.fold(
            (failure) => emit(
                  state.copyWith(
                    getListOfTransactionsStatus: () => AppStatus.failure,
                    errorMessage: () => mapFailureToMessage(failure),
                  ),
                ), (data) {
          emit(
            state.copyWith(
              getListOfTransactionsStatus: () => AppStatus.success,
              havaNoTransactions: () => data?.transactions?.isEmpty,
              listOfTransactions: () => data?.transactions,
              showAsListview: () => (((data?.transactions?.length ?? 0) >=
                      _numberOfTransactionsByDefault) ||
                  (data?.transactions?.isEmpty ?? true)),
            ),
          );
          if (data?.transactions?.isNotEmpty == true) {
            add(const OnboardingDialogEvent());
          }
        }));
  }

  Future<void> _onGetNewProductsCalled(
    GetNewDashboardProductsEvent event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(
      getNewProductsStatus: () => AppStatus.loading,
    ));
    final getNewProductsUseCaseData =
        await getDashboardNewProductsUseCase(NoParams());

    getNewProductsUseCaseData?.fold((failure) {
      emit(
        state.copyWith(
            getNewProductsStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure)),
      );
    }, (data) {
      if (Get.find<ThemeController>().dashbaordLightColor.value !=
          data?.backgroundLightColor) {
        Get.find<ThemeController>().dashbaordLightColor.value =
            data?.backgroundLightColor;
      }
      if (Get.find<ThemeController>().dashbaordDarkColor.value !=
          data?.backgroundDarkColor) {
        Get.find<ThemeController>().dashbaordDarkColor.value =
            data?.backgroundDarkColor;
      }

      if (Get.find<ThemeController>().dashbaordBacgroundLightImage.value !=
          data?.backgroundLightImage) {
        Get.find<ThemeController>().dashbaordBacgroundLightImage.value =
            data?.backgroundLightImage;
      }
      if (Get.find<ThemeController>().dashbaordBacgroundDarkImage.value !=
          data?.backgroundDarkImage) {
        Get.find<ThemeController>().dashbaordBacgroundDarkImage.value =
            data?.backgroundDarkImage;
      }
      emit(state.copyWith(
        getNewProductsStatus: () => AppStatus.success,
        getNewProducts: () => data?.items,
        getLightBackgroundColor: () => data?.backgroundLightColor,
        getDarkBackgroundColor: () => data?.backgroundDarkColor,
        getLightBackgroundImage: () => data?.backgroundDarkImage,
        getDarkBackgroundImage: () => data?.backgroundLightImage,
      ));
    });
  }

  Future<void> _onSetHomePageMessageNull(
    SetHomePageMessageNullEvent event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(
      homePageMessage: () => null,
      getHomePageMessageStatus: () => AppStatus.success,
    ));
  }

  Future<Either<Failure, MessageEntity?>?> getHomeMessage() async {
    final homePageMessageResult = await getHomePageMessageUseCase(NoParams());
    return homePageMessageResult;
  }

  Future<Either<Failure, WorkWithUberAllDataModel?>?>
      getWorkWithUberRequest() async {
    final result = await getWorkWithUberAllDataUseCase(NoParams());
    return result;
  }
}
