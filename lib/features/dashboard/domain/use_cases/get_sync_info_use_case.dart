import 'package:dartz/dartz.dart';
import 'package:thrivve/features/dashboard/domain/entities/sync_notification.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../repositories/dashboard_repository.dart';

class GetSyncInfoUseCase implements UseCase<SyncNotification?, NoParams> {
  final DashboardRepository? dashboardRepository;

  GetSyncInfoUseCase({this.dashboardRepository});

  @override
  Future<Either<Failure, SyncNotification?>?> call(NoParams params) async {
    return await dashboardRepository?.getSyncInfo();
  }
}


