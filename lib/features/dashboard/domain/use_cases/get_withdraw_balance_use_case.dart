import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/withdraw_balance.dart';
import '../repositories/dashboard_repository.dart';

class GetWithdrawBalanceUseCase
    implements UseCase<WithdrawBalance?, NoParams> {
  final DashboardRepository dashboardRepository;

  GetWithdrawBalanceUseCase(this.dashboardRepository);

  @override
  Future<Either<Failure, WithdrawBalance?>?> call(NoParams noParam) async {
    return await dashboardRepository.getWithdrawBalance();
  }
}
