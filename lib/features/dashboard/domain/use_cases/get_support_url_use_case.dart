import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../repositories/dashboard_repository.dart';

class GetSupportUrlUseCase implements UseCase<String?, NoParams> {
  final DashboardRepository? dashboardRepository;

  GetSupportUrlUseCase({this.dashboardRepository});

  @override
  Future<Either<Failure, String?>?> call(NoParams params) async {
    return await dashboardRepository?.getSupportUrl();
  }
}

