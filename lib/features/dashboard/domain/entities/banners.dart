import 'package:equatable/equatable.dart';

class Banners extends Equatable {
  final String? backgroundImageUrl;
  final String? iconImageUrl;
  final String? title;
  final String? description;
  final String? linkUrl;
  final int? ids;

  const Banners(
      {required this.backgroundImageUrl,
      required this.iconImageUrl,
      required this.title,
      this.ids,
      required this.description,
      required this.linkUrl});

  @override
  List<Object?> get props => [
        backgroundImageUrl,
        iconImageUrl,
        ids,
        title,
        description,
        linkUrl,
      ];
}
