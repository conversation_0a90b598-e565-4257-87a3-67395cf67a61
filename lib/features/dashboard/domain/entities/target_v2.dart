import 'package:equatable/equatable.dart';

enum TargetProgressType {
  orders("orders"),
  earnings("earnings"),
  workingHours("working hours"),
  successRate("success rate");

  final dynamic jsonValue;

  const TargetProgressType(this.jsonValue);

  static TargetProgressType fromValue(jsonValue) =>
      TargetProgressType.values.singleWhere((i) => jsonValue == i.jsonValue);
}

class TargetV2 extends Equatable {
  final TargetProgressType? type;
  final String? period;
  final double? baseTarget;
  final double? currentTarget;
  final double? progress;
  final double? progressToShow;
  final bool? active;

  const TargetV2({
    this.type,
    this.period,
    this.baseTarget,
    this.active,
    this.currentTarget,
    this.progress,
    this.progressToShow,
  });

  @override
  List<Object?> get props => [
        type,
        period,
        baseTarget,
        currentTarget,
        progress,
        progressToShow,
        active,
      ];
}
