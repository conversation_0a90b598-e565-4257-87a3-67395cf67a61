import 'package:equatable/equatable.dart';

class PaymentMethod extends Equatable {
  final String? paymentType;
  final int? id;
  final int? bankId;
  final String? bankName;
  final String? bankBeneficiaryName;
  final String? bankImage;
  final String? status;
  final String? statusWarning;
  final String? beneficiaryIban;
  final String? description;
  final bool? isDefault;
  bool? isSelected;

  PaymentMethod({
    required this.paymentType,
    required this.id,
    required this.bankName,
    required this.bankBeneficiaryName,
    required this.bankImage,
    this.status,
    this.bankId,
    this.statusWarning,
    this.beneficiaryIban,
    this.description,
    this.isDefault,
    this.isSelected,
  });

  @override
  List<Object?> get props => [
        bankImage,
        bankId,
        bankBeneficiaryName,
        description,
        paymentType,
        id,
        bankName,
        isSelected,
        status,
        statusWarning,
        beneficiaryIban,
        isDefault,
      ];

  PaymentMethod copyWith({bool? isSelected}) {
    return PaymentMethod(
      paymentType: paymentType,
      description: description,
      id: id,
      bankId: bankId,
      bankName: bankName,
      bankBeneficiaryName: bankBeneficiaryName,
      bankImage: bankImage,
      status: status,
      statusWarning: statusWarning,
      beneficiaryIban: beneficiaryIban,
      isDefault: isDefault,
      isSelected: isSelected,
    );
  }
}
