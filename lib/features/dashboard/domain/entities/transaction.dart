import 'package:equatable/equatable.dart';

class Transaction extends Equatable {
  final String? totalAmountWithFees;
  final bool? isPositiveTransactions;
  final String? amountWithoutFees;
  final String? fees;
  final String? message;
  final String? referenceId;
  final String? title;
  final String? description;
  final String? paymentType;
  final String? status;
  final String? remarks;
  final String? iconType;
  final String? subTitle;
  final String? transactionType;
  final String? statusKey;
  final DateTime? creation;

  const Transaction({
    this.totalAmountWithFees,
    this.transactionType,
    this.iconType,
    this.subTitle,
    this.amountWithoutFees,
    this.remarks,
    this.fees,
    this.message,
    this.description,
    this.referenceId,
    this.title,
    this.paymentType,
    this.status,
    this.isPositiveTransactions,
    this.creation,
    this.statusKey,
  });

  @override
  List<Object?> get props => [
        transactionType,
        message,
        totalAmountWithFees,
        iconType,
        subTitle,
        statusKey,
        amountWithoutFees,
        remarks,
        fees,
        referenceId,
        title,
        paymentType,
        status,
        isPositiveTransactions,
        creation,
        description,
      ];
}
