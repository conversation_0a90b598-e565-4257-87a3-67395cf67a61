import 'package:equatable/equatable.dart';
import 'package:thrivve/features/dashboard/domain/entities/product.dart';

class Products extends Equatable {
  final List<Product>? products;
  final NoProductsObject? noProductsObject;

  const Products({
    required this.products,
    required this.noProductsObject,
  });

  @override
  List<Object?> get props => [
        products,
        noProductsObject,
      ];
}

class NoProductsObject extends Equatable {
  final String? title;
  final String? message;
  final bool? hasAction;

  final String? image;

  const NoProductsObject({
    this.title,
    this.message,
    this.hasAction,
    this.image,
  });

  @override
  List<Object?> get props => [
        title,
        message,
        hasAction,
        image,
      ];
}
