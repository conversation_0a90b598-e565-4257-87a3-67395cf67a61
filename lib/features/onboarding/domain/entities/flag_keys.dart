import 'flag.dart';

enum FlagKeys {
  banner,
  instant_payment,
  top_up,
  browse_our_products,
  list_of_products,
  signup,
  cards,
  is_allow_guests_apply_for_product,
  earn_150_sar,
}

// FlagKeys? stringToEnum(String status) {
//   return FlagKeys.values
//       .firstWhere((e) => e.toString().split('.').last == status);
// }

// Function to convert string to FlagKeys enum
FlagKeys? stringToEnum(String? flagKey) {
  switch (flagKey) {
    case 'instant_payment':
      return FlagKeys.instant_payment;
    case 'cards':
      return FlagKeys.cards;
    case 'top_up':
      return FlagKeys.top_up;
    case 'earn_150_sar':
      return FlagKeys.earn_150_sar;
    case 'list_of_products':
      return FlagKeys.list_of_products;
    case 'signup':
      return FlagKeys.signup;
    case 'browse_our_products':
      return FlagKeys.browse_our_products;
    case 'is_allow_guests_apply_for_product':
      return FlagKeys.is_allow_guests_apply_for_product;
    case 'banner':
      return FlagKeys.banner;
    default:
      return null;
  }
}

// Helper function to find all enabled flags
List<Flag?> findAllEnabledFlags(List<Flag?>? data) {
  return data?.where((element) => element?.isEnabled == true).toList() ?? [];
}
