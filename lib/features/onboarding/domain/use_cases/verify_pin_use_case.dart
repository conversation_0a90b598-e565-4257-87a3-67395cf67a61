import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../repositories/auth_repository.dart';

class VerifyPinUseCase implements UseCase<bool?, VerifyPinParams> {
  AuthRepository? authRepository;

  VerifyPinUseCase({this.authRepository});

  @override
  Future<Either<Failure, bool?>?> call(VerifyPinParams params) async {
    return await authRepository?.verifyPinCode(pin: params.pinCode);
  }
}

class VerifyPinParams {
  String? pinCode;

  VerifyPinParams({required this.pinCode});
}
