import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';

import '../entities/country.dart';
import '../repositories/auth_repository.dart';

class GetCountriesUseCase implements UseCase<List<Country>?, NoParams> {
  AuthRepository? authRepository;

  GetCountriesUseCase({this.authRepository});

  @override
  Future<Either<Failure, List<Country>?>?> call(NoParams params) async {
    return await authRepository?.getCountries();
  }
}
