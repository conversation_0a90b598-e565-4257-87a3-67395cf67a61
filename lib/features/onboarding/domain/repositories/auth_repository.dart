import 'package:dartz/dartz.dart';
import 'package:thrivve/features/onboarding/data/models/change_pin_response.dart';
import 'package:thrivve/features/onboarding/domain/entities/check_token_response_entity.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/send_otp_use_case.dart';

import '../../../../core/error/failures.dart';
import '../../../pin/domain/entities/thrivve_user.dart';
import '../entities/country.dart';
import '../entities/flag.dart';
import '../entities/return_otp.dart';
import '../entities/verify_otp.dart';

abstract class AuthRepository {
  Future<Either<Failure, ReturnOtp?>?>? sendOtp({
    required SendOptParams params,
  });

  Future<Either<Failure, VerifyOtp?>?>? verifyOtp({
    required String? mobile,
    required String? otpCode,
  });

  Future<Either<Failure, List<Country>?>?>? getCountries();

  Future<Either<Failure, String?>?>? getCurrentCountryCode();

  Future<Either<Failure, ThrivveUser?>> signIn({
    required bool? isNew,
    required String? language,
    required String? platform,
    required String? platformVersion,
    required String? appVersion,
    required String? pushNotificationToken,
  });
  Future<Either<Failure, bool?>> verifyPinCode({
    required String? pin,
  });
  Future<Either<Failure, ChangePinResponse?>> changePinCode(
      {required String? pin, required String? newPinCode});

  Future<Either<Failure, List<Flag?>?>> getFlags();

  Future<Either<Failure, String?>?>? logout();

  // checkTokenStatus
  Future<Either<Failure, CheckTokenResponseEntity?>?>? checkTokenStatus();
}
