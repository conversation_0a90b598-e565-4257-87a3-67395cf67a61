import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import '../../../../core/widget/icon_button_widget.dart';
import '../../domain/entities/country.dart';
import 'country_item.dart';

class SelectCountryBottomSheet extends StatefulWidget {
  final List<Country> listOfCounties;
  final Country? selectedCountry;
  final Function(Country) onSelectedCountry;

  const SelectCountryBottomSheet({
    super.key,
    required this.listOfCounties,
    required this.selectedCountry,
    required this.onSelectedCountry,
  });

  @override
  State<SelectCountryBottomSheet> createState() =>
      _SelectCountryBottomSheetState();
}

class _SelectCountryBottomSheetState extends State<SelectCountryBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0.r),
            topRight: Radius.circular(20.0.r),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 40.h,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: Text(
                      "select_a_country".tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15.sp,
                        color: context.black,
                      ),
                    ),
                  ),
                  const Positioned(
                    right: 0,
                    child: IconButtonWidget(),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            Divider(
              height: 0.4.h,
              color: context.black!.withOpacity(0.20),
            ),
            SizedBox(
              height: 20.h,
            ),
            ListView.builder(
              key: const Key("listViewWidgetKey"),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: widget.listOfCounties.length,
              itemBuilder: (context, index) {
                var country = widget.listOfCounties[index];
                return Container(
                  margin: EdgeInsets.only(bottom: 18.h),
                  child: InkWell(
                    onTap: () {
                      widget.onSelectedCountry(country);
                      Get.back();
                    },
                    child: CountryItem(
                      country: country,
                      isSelected: widget.selectedCountry?.id == country.id,
                      language: Get.locale?.languageCode ?? "en",
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
