import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class SuffixIcon extends StatelessWidget {
  final Function() onTap;
  final IconData? icon;

  const SuffixIcon({
    super.key,
    required this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.fromBorderSide(BorderSide(color: context.black!)),
      ),
      child: InkWell(
        onTap: onTap,
        child: Icon(
          icon ?? Icons.close,
          color: context.black,
          size: 12.w,
        ),
      ),
    );
  }
}
