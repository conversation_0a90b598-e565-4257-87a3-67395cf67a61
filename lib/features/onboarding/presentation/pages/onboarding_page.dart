import 'dart:async';

import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:thrivve/core/deep_link/deep_link.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/extentions/safe_cast.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/quick_actions/app_quick_actions.dart';
import 'package:thrivve/core/util/analytics_actions.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/onboarding/presentation/arguments/on_boarding_arguments.dart';
import 'package:thrivve/features/onboarding/presentation/manager/onboarding_bloc.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/const.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../../core/widget/border_button.dart';
import '../../../../generated/assets.dart';
import '../widgets/onboarding_widget.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  OnboardingPageState createState() => OnboardingPageState();
}

class OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  late Timer _timer;
  int _currentPage = 0;
  late OnboardingBloc bloc;
  final arguments = safeCast<OnBoardingArguments>(Get.arguments);
  @override
  void initState() {
    super.initState();
    bloc = getIt<OnboardingBloc>();
    _startAutoScroll();
    WidgetsBinding.instance.addPostFrameCallback((duration) {
      init();
      AppQuickActions().initialize();
    });
  }

  Future<void> init() async {
    streamSubscription = await getIt<DeepLink>().listenDynamicLinks();
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    streamSubscription.cancel();
    super.dispose();
  }

  late StreamSubscription<Map<dynamic, dynamic>> streamSubscription;

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 10), (Timer timer) {
      if (_currentPage < 2) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }

      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => bloc
        ..add(GetFlagsEvent())
        ..add(GetCountriesEvent()),
      child: BlocListener<OnboardingBloc, OnboardingState>(
        listenWhen: (previous, current) => previous.weblink != current.weblink,
        listener: (context, state) {
          if (state.weblink != null) {
            openTheUriInWebPage(
              url: state.weblink,
              context: context,
            );
          }
        },
        child: BlocBuilder<OnboardingBloc, OnboardingState>(
          builder: (context, state) {
            return Scaffold(
              body: Padding(
                padding: EdgeInsets.only(
                    left: 16.w, right: 16.w, top: 8.h, bottom: 56.h),
                child: Stack(
                  children: [
                    Positioned(
                        right: 0,
                        left: 0,
                        top: 0,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            SizedBox(
                              height: 44.h,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    needHelpOnboardingBottomSheet(
                                        context: context,
                                        chatWithUsClick: () {
                                          context
                                              .read<OnboardingBloc>()
                                              .add(GetSupportUrlEvent());
                                        });
                                  },
                                  child: Image.asset(
                                    Assets.thrivvePhotosNeedHelpFaq,
                                    width: 28.w,
                                    height: 28.h,
                                    color: context.outlineButtonColor,
                                  ),
                                ),
                                BorderButton(
                                  height: 30,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                  text: state.language == valueEnLanguage
                                      ? 'اللغة العربية'
                                      : 'English',
                                  fontFamily: state.language == valueEnLanguage
                                      ? "NotoSans"
                                      : "NotoSansArabic",
                                  onTab: () {
                                    context.read<OnboardingBloc>().add(
                                          ChangeLanguageEvent(
                                            language: state.language ==
                                                    valueEnLanguage
                                                ? valueArLanguage
                                                : valueEnLanguage,
                                          ),
                                        );
                                  },
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 108.h,
                            ),
                            ExpandablePageView(
                              controller: _pageController,
                              onPageChanged: (int page) {
                                setState(() {
                                  _currentPage = page;
                                });
                              },
                              children: [
                                OnboardingWidget(
                                  title: 'onboarding_title_1'.tr,
                                  subtitle: 'onboarding_desc_1'.tr,
                                  image: Assets.thrivvePhotosNoProductFound,
                                ),
                                OnboardingWidget(
                                  title: 'onboarding_title_2'.tr,
                                  subtitle: 'onboarding_desc_2'.tr,
                                  image: Assets.thrivvePhotosNoInvoicesFound,
                                ),
                                OnboardingWidget(
                                  title: 'onboarding_title_3'.tr,
                                  subtitle: 'onboarding_desc_3'.tr,
                                  image:
                                      Assets.thrivvePhotosNoTransactionsFound,
                                ),
                              ],
                            ),
                          ],
                        )),
                    Positioned(
                      right: 0,
                      left: 0,
                      bottom: 0,
                      child: Column(
                        children: [
                          SmoothPageIndicator(
                            controller: _pageController, // PageController
                            count: 3,
                            effect: WormEffect(
                              type: WormType.normal,
                              dotWidth: 8.w,
                              dotHeight: 8.h,
                              spacing: 17.w,
                              dotColor:
                                  context.appPrimaryColor.withOpacity(0.20),
                              activeDotColor: context.outlineButtonColor,
                            ), // your preferred effect
                          ),
                          SizedBox(
                            height: 22.h,
                          ),
                          BorderButton(
                            key: ValueKey('loginButton'),
                            text: "login".tr,
                            fontWeight: FontWeight.w600,
                            fontSize: 11.sp,
                            colorText: Colors.white,
                            onTab: () {
                              // createNewAccount(
                              //   context: context,
                              //   isLogin: true,
                              // );
                            },
                            color: context.appPrimaryColor,
                          ),
                          if (state.isSignUpIsEnable == true) ...[
                            SizedBox(
                              height: 16.h,
                            ),
                            BorderButton(
                              text: "create_new_account".tr,
                              fontWeight: FontWeight.w600,
                              fontSize: 11.sp,
                              onTab: () {
                                // createNewAccount(
                                //   context: context,
                                //   isLogin: false,
                                // );
                              },
                            ),
                          ],
                          if (state.isBrowsProductsIsEnable == true) ...[
                            SizedBox(
                              height: 16.h,
                            ),
                            InkWell(
                              key: ValueKey('BrowsProductsButton'),
                              onTap: () {
                                Get.toNamed(AppRoutes.productsPage, arguments: {
                                  "personalInfo": null,
                                  "canWithdraw": false,
                                  "isPublic": true,
                                  AnalyticsActions.screenNameParams:
                                      AnalyticsActions.onboarding,
                                });
                              },
                              child: DecoratedBox(
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: context.appPrimaryColor!,
                                      width: 1.0,
                                    ),
                                  ),
                                ),
                                child: CustomTextWidget(
                                  title: "browse_our_products".tr,
                                  textAlign: TextAlign.center,
                                  color: context.outlineButtonColor,
                                  size: 12,
                                  fontWeight: FontWeight.w600,
                                  height: 0,
                                ),
                              ),
                            ),
                          ]
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
//
//   void createNewAccount({
//     required BuildContext context,
//     required bool isLogin,
//   }) {
//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: context.containerColor,
//       builder: (context) {
//         return BlocProvider.value(
//             value: bloc,
//             child: SignUpBottomSheetWidget(
//               isLogin: isLogin,
//               dlParams: arguments?.dlParams,
//             ));
//       },
//     );
//   }
// }
