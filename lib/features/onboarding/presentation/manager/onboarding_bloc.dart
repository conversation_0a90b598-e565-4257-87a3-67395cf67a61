import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/notifications_fcm/firebase_messaging_impl.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/analytics_actions.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_support_url_use_case.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/onboarding/domain/entities/return_otp.dart';
import 'package:thrivve/features/pin/domain/use_cases/sing_in_use_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/manager/vehicle_details_controller.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/localDataSource/user_secure_data_source.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../pin/domain/entities/thrivve_user.dart';
import '../../domain/entities/country.dart';
import '../../domain/entities/flag_keys.dart';
import '../../domain/entities/verify_otp.dart';
import '../../domain/use_cases/get_countries_use_case.dart';
import '../../domain/use_cases/get_current_country_code_use_case.dart';
import '../../domain/use_cases/get_flags_use_case.dart';
import '../../domain/use_cases/send_otp_use_case.dart';
import '../../domain/use_cases/verify_otp_use_case.dart';

part 'onboarding_event.dart';
part 'onboarding_state.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  final GetFlagsUseCase getFlagsUseCase;
  final GetSupportUrlUseCase getSupportUrlUseCase;
  final SendOtpUseCase sendOtpUseCase;
  final VerifyOtpUseCase verifyOtpUseCase;
  final GetCountriesUseCase getCountriesUseCase;
  final GetCurrentCountryCodeUseCase getCurrentCountryCodeUseCase;
  final UserSecureDataSource userSecureDataSource;
  final SignInUseCase signInUseCase;

  OnboardingBloc({
    required this.getFlagsUseCase,
    required this.sendOtpUseCase,
    required this.verifyOtpUseCase,
    required this.getCountriesUseCase,
    required this.getCurrentCountryCodeUseCase,
    required this.getSupportUrlUseCase,
    required this.userSecureDataSource,
    required this.signInUseCase,
  }) : super(const OnboardingState()) {
    on<GetFlagsEvent>(_getFlagsEventOnClick);
    on<SendOtpEvent>(_getSendOTPEventOnClick);
    on<VerifyOtpEvent>(_getVerifyOtpEventOnClick);
    on<GetCountriesEvent>(_getCountriesEventOnClick);
    on<ResentVerificationCodeEvent>(_getReSendOTPEventOnClick);
    on<SelectedCountryEvent>(_selectedCountryEventOnClick);
    on<ChangeLanguageEvent>(_changeLanguageEventOnClick);
    on<GetSupportUrlEvent>(_getSupportUrlEventOnClick);
    on<SaveLeadIdEvent>(_saveLeadIdEventOnClick);
    on<GetSignInOnly>(_getSignInOnly);
  }

  Future<void> _saveLeadIdEventOnClick(
    SaveLeadIdEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    if (event.leadId != null) {
      await userSecureDataSource.setLeadId(event.leadId);
      getIt
          .get<IAnalyticsLogger>()
          .logProperty(AnalyticsActions.leadId, event.leadId!);
    }
  }

  Future<void> _getSignInOnly(
    GetSignInOnly event,
    Emitter<OnboardingState> emit,
  ) async {
    String platformVersion = await getPlatFormVersion();
    String? language = Get.locale?.languageCode; //todo
    String platform = getPlatForm();
    var versionPlatform = await PackageInfo.fromPlatform();
    var appVersion = versionPlatform.version;

    final fcmToken =
        await FireBaseMessagingImpl.fireBaseMessagingImpl.getTokenFirebase();
    print("pushNotificationToken $fcmToken");
    emit(state.copyWith(
      loginStatus: () => AppStatus.loading,
    ));
    final signInUseCaseData = await signInUseCase(
      SignInParams(
        pin: null,
        isNew: false,
        platform: platform,
        platformVersion: platformVersion,
        appVersion: appVersion,
        pushNotificationToken: fcmToken,
        language: language,
      ),
    );
    await signInUseCaseData?.fold((failure) async {
      emit(state.copyWith(
        loginStatus: () => AppStatus.failure,
        errorMessage: () => mapFailureToMessage(failure),
      ));
    }, (data) async {
      emit(
        state.copyWith(
          loginStatus: () => AppStatus.success,
          errorMessage: () => '',
        ),
      );

      final applicationTypeEnum = data?.applicationTypeEnum;
      await saveUserToLocalStorage(data, userSecureDataSource);
      final isPinEnabled = await getIt<UserSecureDataSource>().isPinEnabled();
      final isFaceIdEnabled =
          await getIt<UserSecureDataSource>().isBioMetricEnabled();
      Get.find<ThemeController>().getTheme();
      getIt<MainHomeBloc>().add(InitializeMainHomeEvent());
      if (isFaceIdEnabled || isPinEnabled) {
        Get.offAllNamed(AppRoutes.pinScreen);
      } else {
        if (Get.currentRoute == AppRoutes.rentVehicleDetailsPage &&
            applicationTypeEnum == null) {
          getIt<MainHomeBloc>().add(InitializeMainHomeEvent());
          Get.find<VehicleDetailsController>().reviewAndCheckout();
        } else {
          reinitializeBloc();
          Get.offAllNamed(AppRoutes.homePage);
        }
      }
    });
  }

  Future<void> _getSupportUrlEventOnClick(
    GetSupportUrlEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    if (state.supportUrl != null && state.supportUrl?.isNotEmpty == true) {
      openWhatsApp(url: state.supportUrl ?? "");
      return;
    }
    final getSupportUrlUseCaseData = await getSupportUrlUseCase(NoParams());
    getSupportUrlUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) {
      emit(state.copyWith(supportUrl: () => data));
      openWhatsApp(url: data ?? "");
    });
  }

  Future<void> _changeLanguageEventOnClick(
    ChangeLanguageEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    await userSecureDataSource.setLanguage(event.language);
    emit(state.copyWith(language: () => event.language));
    Get.updateLocale(Locale(event.language!));
  }

  Future<void> _selectedCountryEventOnClick(
    SelectedCountryEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    await userSecureDataSource.setCountryCode(event.country?.code ?? "sa");
    emit(state.copyWith(selectedCountry: () => event.country));
  }

  Future<void> _getFlagsEventOnClick(
    GetFlagsEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    if (Get.parameters["weblink"] != null) {
      emit(state.copyWith(
        weblink: () => Get.parameters["weblink"],
      ));
    }
    var newCurrentSavedLanguage = await userSecureDataSource.getLanguage();
    emit(state.copyWith(language: () => newCurrentSavedLanguage ?? ""));
    
    final getFlagsUseCaseData = await getFlagsUseCase(NoParams());
    if (getFlagsUseCaseData == null) return;
    
    await getFlagsUseCaseData.fold(
      (failure) async {
        emit(state.copyWith(
          getFlagStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (data) async {
        var isAppleAsLead = data?.where((element) =>
            stringToEnum(element!.flagKey!) ==
            FlagKeys.is_allow_guests_apply_for_product);
        await userSecureDataSource
            .setAppleAsLead(isAppleAsLead?.first?.isEnabled);
        final isSignUpIsEnable = data?.where(
            (element) => stringToEnum(element?.flagKey) == FlagKeys.signup);
        final isBrowsProductsIsEnable = data?.where((element) =>
            stringToEnum(element?.flagKey) == FlagKeys.browse_our_products);

        emit(state.copyWith(
          getFlagStatus: () => AppStatus.success,
          isSignUpIsEnable: () => isSignUpIsEnable?.first?.isEnabled,
          isBrowsProductsIsEnable: () =>
              isBrowsProductsIsEnable?.first?.isEnabled,
        ));
      },
    );
  }

  Future<void> _getCountriesEventOnClick(
    GetCountriesEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    emit(state.copyWith(getCountriesStatus: () => AppStatus.loading));

    final getCurrentCountryCodeUseCaseData =
        await getCurrentCountryCodeUseCase(NoParams());
    getCurrentCountryCodeUseCaseData?.fold(
        (failure) => emit(
            state.copyWith(errorMessage: () => mapFailureToMessage(failure))),
        (data) => emit(state.copyWith(
              currentCountryCode: () => data,
            )));

    final getCountriesUseCaseData = await getCountriesUseCase(NoParams());
    getCountriesUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            getCountriesStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))),
        (data) => emit(state.copyWith(
              listOfCountries: () => data,
              selectedCountry: () {
                var list = data?.where((element) =>
                    element.code?.toLowerCase() == state.currentCountryCode);
                if (list?.isEmpty == true) {
                  return data?.firstWhere(
                      (element) => element.code?.toLowerCase() == "sa");
                } else {
                  return list?.first;
                }
              },
              getCountriesStatus: () => AppStatus.success,
            )));
  }

  Future<void> _getSendOTPEventOnClick(
    SendOtpEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    emit(state.copyWith(
      status: () => AppStatus.loading,
      mobileNum: () => event.mobileNum,
    ));
    final data = event.dlParams ?? {};
    if (data.isNotEmpty) {
      final thCamp = await userSecureDataSource.getThrriveCamp();
      if (thCamp.isNotEmpty &&
          (data.containsKey('product_id') || data.containsKey('thr_prod_id'))) {
        event.dlParams?['thr_cam'] = thCamp;
        await userSecureDataSource.setThrriveCamp('');
      }
    }
    final sendOtpUseCaseData = await sendOtpUseCase(
      SendOptParams(
        isLogin: event.isLogin,
        mobile: event.mobileNum,
        name: event.name,
        dlParams: event.dlParams,
      ),
    );
    sendOtpUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                status: () => AppStatus.failure,
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) {
      addLoginAnalyticsEvent(event.mobileNum);
      emit(state.copyWith(
        successSendOtpMessage: () => data?.message,
        otpCode: () => kDebugMode ? data?.otp : "",
        returnOtp: () => data,
        status: () => AppStatus.success,
      ));
    });
  }

  void addLoginAnalyticsEvent(String? mobileNum) {
    getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.loginCompleted,
        parameters: {AnalyticsActions.mobile: mobileNum ?? ""});
  }

  void addOtpAnalyticsEvent() {
    getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.otpEntered);
  }

  Future<void> _getReSendOTPEventOnClick(
    ResentVerificationCodeEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    emit(state.copyWith(
        reSendOtpStatus: () => AppStatus.loading, counterReStart: () => false));

    final sendOtpUseCaseData = await sendOtpUseCase(SendOptParams(
      isLogin: event.isLogin,
      mobile: event.mobileNum,
    ));
    sendOtpUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            reSendOtpStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))), (data) {
      emit(state.copyWith(
        successSendOtpMessage: () => data?.message,
        otpCode: () => kDebugMode ? data?.otp : "", // for testing only
        counterReStart: () => true,
        reSendOtpStatus: () => AppStatus.success,
      ));
    });
  }

  Future<void> _getVerifyOtpEventOnClick(
    VerifyOtpEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    if (event.otpNum == null || event.otpNum?.isEmpty == true) {
      emit(state.copyWith(
        otpValidationErrorMessage: () => "otp_empty_msj".tr,
        isOtpNotValid: () => true,
      ));
      return;
    } else if (event.otpNum?.length != 6) {
      emit(state.copyWith(
        otpValidationErrorMessage: () => "otp_msj".tr,
        isOtpNotValid: () => true,
      ));
      return;
    }

    emit(state.copyWith(verifyStatus: () => AppStatus.loading));

    final verifyOtpUseCaseData = await verifyOtpUseCase(
      VerifyOptParams(
        mobile: event.mobileNum,
        otpCode: event.otpNum,
      ),
    );
    verifyOtpUseCaseData?.fold(
      (failure) => emit(
        state.copyWith(
          verifyStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ),
      ),
      (data) async {
        addOtpAnalyticsEvent();
        saveLoginTokenLocalStorage(data,
            userSecureDataSource: userSecureDataSource);

        emit(
          state.copyWith(
            verifyOtp: () => data,
            verifyStatus: () => AppStatus.success,
          ),
        );
      },
    );
  }

  Future<void> saveLoginTokenLocalStorage(VerifyOtp? data,
      {required UserSecureDataSource? userSecureDataSource}) async {
    await userSecureDataSource?.setTempToken(data?.token ?? "");
  }
}
