import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class BankCardWidget extends StatelessWidget {
  final String cardLastFourDigits;

  const BankCardWidget({super.key, required this.cardLastFourDigits});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Image.asset(Assets.thrivvePhotosVisa,
            width: 273.w, height: 166.h, fit: BoxFit.fill),
        Text(
          "${"physical_card".tr}  **** $cardLastFourDigits",
          style: TextStyle(
            color: context.black,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
