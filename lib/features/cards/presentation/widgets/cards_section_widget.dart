import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/features/cards/presentation/widgets/bank_card_widget.dart';

import '../../domain/entities/bank_card.dart';

class CardsSectionWidget extends StatelessWidget {
  final List<BankCard> bankCards;

  const CardsSectionWidget(
      {super.key,
      this.bankCards = const [
        BankCard(cardLastFourDigits: '1234'),
        BankCard(cardLastFourDigits: '5678'),
        BankCard(cardLastFourDigits: '9012'),
      ]});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 216.h,
      child: ListView.builder(
          shrinkWrap: false,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: bankCards.length,
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) {
            return Row(
              children: [
                index == 0
                    ? SizedBox(
                        width: 51.w,
                      )
                    : Container(),
                Row(
                  children: [
                    const BankCardWidget(
                      cardLastFourDigits: '1234',
                    ),
                    index != bankCards.length - 1
                        ? SizedBox(
                            width: 25.w,
                          )
                        : Container(),
                  ],
                ),
                index == bankCards.length - 1
                    ? SizedBox(
                        width: 51.w,
                      )
                    : Container(),
              ],
            );
          }),
    );
  }
}
