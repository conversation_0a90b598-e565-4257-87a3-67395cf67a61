import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/assets.dart';
import 'add_to_apple_button.dart';
import 'card_action_button.dart';

class CardActionButtonsWidget extends StatelessWidget {
  const CardActionButtonsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CardActionButton(
              // onTap: () {},
              title: "add_money".tr,
              icon: Assets.thrivvePhotosAddIcon,
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              width: 23.w,
            ),
            CardActionButton(
              // onTap: () {},
              title: "card_details".tr,
              icon: Assets.thrivvePhotosCard,
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              width: 23.w,
            ),
            CardActionButton(
              // onTap: () {},
              title: "freeze_card".tr,
              icon: Assets.thrivvePhotosFreezeCard,
            ),
          ],
        ),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 32.h,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
          child: const AddToAppleButton(
              // onTap: () {},
              ),
        ),
      ],
    );
  }
}
