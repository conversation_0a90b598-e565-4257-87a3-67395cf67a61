import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/generated/assets.dart';

extension CardTypeExtention on String? {
  CardTypeEnum toCardType() {
    switch (this) {
      case 'migs':
        return CardTypeEnum.migs;
      case 'tabby':
        return CardTypeEnum.tabby;
      case 'tamara':
        return CardTypeEnum.tamara;
      case 'apple_pay':
        return CardTypeEnum.apple_pay;
      case 'google_pay':
        return CardTypeEnum.google_pay;
      case 'stc_pay':
        return CardTypeEnum.stc_pay;
      case 'Bank':
        return CardTypeEnum.Bank;
      default:
        return CardTypeEnum.migs;
    }
  }

  String toImagePayment() {
    switch (this) {
      case 'migs':
        return Assets.paymentMethodVisaMaster;
      case 'tabby':
        return Assets.paymentMethodTappy;
      case 'tamara':
        return Assets.paymentMethodTamara;
      case 'apple_pay':
        return Assets.paymentMethodApplePay;
      case 'google_pay':
        return Assets.paymentMethodGooglePay;
      case 'stc_pay':
        return Assets.paymentMethodStc;
      case 'Bank':
        return Assets.paymentMethodBank;
      default:
        return Assets.paymentMethodVisaMaster;
    }
  }
}
