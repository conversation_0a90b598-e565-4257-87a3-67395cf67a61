import 'package:thrivve/features/topUp/data/models/checkout_input_model.dart';
import 'package:thrivve/features/topUp/domain/entities/checkout_input_entity.dart';

extension CheckoutInputExtention on CheckoutInputEntity {
  CheckoutInputModel toModel() {
    return CheckoutInputModel(
      paymentGateway: paymentGateway,
      amount: amount,
      integrationPaymentId: integrationPaymentId,
      itemId: itemId,
    );
  }
}
