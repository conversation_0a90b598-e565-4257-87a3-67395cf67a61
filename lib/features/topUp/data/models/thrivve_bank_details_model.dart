import '../../domain/entities/thrivve_bank_details.dart';


class ThrivveBankDetailsModel extends ThrivveBankDetails {
  const ThrivveBankDetailsModel({
    required String bankName,
    required String bankAccountIban,
    required String beneficiaryName,
    required String? currency,
  }) : super(
          bankName: bankName,
          bankAccountIban: bankAccountIban,
          beneficiaryName: beneficiaryName,
          currency: currency,
        );

  factory ThrivveBankDetailsModel.fromJson(Map<String, dynamic> json) {
    return ThrivveBankDetailsModel(
      bankName: json['bank_name'],
      bankAccountIban: json['company_iban'],
      beneficiaryName: json['company_account_name'],
      currency: json['currency'],
    );
  }


}
