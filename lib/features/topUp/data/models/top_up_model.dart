import '../../domain/entities/top_up.dart';

class TopUpModel extends TopUp {
  const TopUpModel({
    required double balance,
    required List<num> values,
    required bool isFirstRequestBefore,
    required String topUpSla,
    required String currency,
    int? applicationId,
    double? amountTopUp,
  }) : super(
          suggestedAmounts: values,
          isMadeTopUpRequestBefore: isFirstRequestBefore,
          paymentReceivedTime: topUpSla,
          currency: currency,
          applicationId: applicationId,
          amountTopUp: amountTopUp,
        );

  factory TopUpModel.fromJson(Map<String, dynamic> json) {
    return TopUpModel(
        balance: json['balance'],
        values: List<num>.from(json['values']),
        isFirstRequestBefore: json['is_request_before'],
        topUpSla: json['top_up_sla'],
        currency: json['currancy'],
        applicationId: json['application_id'],
        amountTopUp: json['amount_topup']);
  }
}
