import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/enum/top_up_type.dart';
import 'package:thrivve/core/util/iterable_compact_map.dart';
import 'package:thrivve/features/payment/domain/entities/saved_card_entity.dart';
import 'package:thrivve/features/topUp/domain/entities/checkout_input_entity.dart';
import 'package:thrivve/features/topUp/domain/entities/payment_checkout_entity.dart';
import 'package:thrivve/features/topUp/domain/entities/payment_method_entity.dart';
import 'package:thrivve/features/topUp/domain/entities/thrivve_bank_details.dart';
import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/features/topUp/domain/use_cases/checkout_payment_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/list_saved_cards_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/payment_methods_use_case.dart';

import '../../../../core/user_cases/user_case.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/general_helper.dart';
import '../../domain/entities/top_up.dart';
import '../../domain/entities/top_up_sccessfully.dart';
import '../../domain/use_cases/get_bank_details_use_case.dart';
import '../../domain/use_cases/make_top_up_request_use_case.dart';

part 'top_up_event.dart';
part 'top_up_state.dart';

class TopUpBloc extends Bloc<TopUpEvent, TopUpState> {
  final MakeTopUpRequestUseCase makeTopUpRequestUseCase;
  final GetThrivveBankDetailsDataUseCase getThrivveBankDetailsDataUseCase;
  final PaymentMethodsUseCase paymentMethodsUseCase;
  final CheckoutPaymentUseCase checkoutPaymentUseCase;
  final ListSavedCardsUseCase listSavedCardsUseCase;
  TopUpBloc({
    required this.listSavedCardsUseCase,
    required this.paymentMethodsUseCase,
    required this.checkoutPaymentUseCase,
    required this.makeTopUpRequestUseCase,
    required this.getThrivveBankDetailsDataUseCase,
  }) : super(
          TopUpState(
            topUp: Get.arguments['topUpObject'] as TopUp,
          ),
        ) {
    on<MakeTopUpRequest>(_onMakeTopUpRequest);
    on<SetTopUpAmount>(_onSetTopUpAmount);
    on<UploadReceiptImageEvent>(_onUploadReceiptImage);
    on<DeleteReceiptImageEvent>(_onDeleteReceiptImage);
    on<GetThrivveBankDetailsEvent>(_onGetThrivveBankDetails);
    on<CheckOutPaymentEvent>(_onCheckoutPayment);
    on<FetchPaymentMethodsEvent>(_fetchPaymentMethods);
    on<FetchSavedCardsEvent>(_fetchSavedCardsEvent);
    on<SelectedPaymentMethodEvent>(_onSelectedPaymentMethodEvent);
  }

  Future<void> _onSelectedPaymentMethodEvent(
      SelectedPaymentMethodEvent event, Emitter<TopUpState> emit) async {
    emit(
      state.copyWith(
        selectedPaymentMethod: () => event.paymentMethodEntity,
      ),
    );
  }

  Future<void> _fetchSavedCardsEvent(
      FetchSavedCardsEvent event, Emitter<TopUpState> emit) async {
    emit(
      state.copyWith(
        fetchSavedCardsStatus: () => AppStatus.loading,
      ),
    );
    final result = await listSavedCardsUseCase.call(NoParams());
    result?.fold(
      (failure) {
        final failureError = mapFailureToMessage(failure);
        emit(
          state.copyWith(
            errorMessage: () => failureError,
            fetchSavedCardsStatus: () => AppStatus.failure,
          ),
        );
      },
      (list) {
        emit(
          state.copyWith(
            fetchSavedCardsStatus: () => AppStatus.success,
            listSavedCards: () => list?.noneNullList() ?? [],
          ),
        );
      }, // Return the actual list on success
    );
  }

  Future<void> _fetchPaymentMethods(
      FetchPaymentMethodsEvent event, Emitter<TopUpState> emit) async {
    emit(
      state.copyWith(
        fetchPaymentMethodStatus: () => AppStatus.loading,
      ),
    );
    final result = await paymentMethodsUseCase.call(NoParams());
    result?.fold(
      (failure) {
        final failureError = mapFailureToMessage(failure);
        emit(
          state.copyWith(
            errorMessage: () => failureError,
            fetchPaymentMethodStatus: () => AppStatus.failure,
          ),
        );
      },
      (list) {
        emit(
          state.copyWith(
            fetchPaymentMethodStatus: () => AppStatus.success,
            listPayment: () => list?.noneNullList() ?? [],
            selectedPaymentMethod: () {
              final paymentMethods = (list?.noneNullList() ?? []);
              // First, try to find Apple Pay (iOS) or Google Pay (Android)
              var selected = paymentMethods.firstWhereOrNull(
                (e) =>
                    (e.type ==
                        (Platform.isIOS
                            ? CardTypeEnum.apple_pay
                            : CardTypeEnum.google_pay)) ||
                    e.type == CardTypeEnum.migs ||
                    e.type == CardTypeEnum.Bank,
              );
              return selected;
            },
          ),
        );
      }, // Return the actual list on success
    );
  }

  Future<void> _onCheckoutPayment(
      CheckOutPaymentEvent event, Emitter<TopUpState> emit) async {
    emit(
      state.copyWith(
        checkOutPaymentStatus: () => AppStatus.loading,
      ),
    );
    final result = await checkoutPaymentUseCase.call(event.checkoutInputEntity);
    result?.fold(
      (failure) {
        final failureError = mapFailureToMessage(failure);
        emit(
          state.copyWith(
            errorMessage: () => failureError,
            checkOutPaymentStatus: () => AppStatus.failure,
          ),
        );
      },
      (data) {
        if ((data?.clientSecret ?? '').isNotEmpty &&
            (data?.publicKey ?? '').isNotEmpty) {
          emit(
            state.copyWith(
              checkOutPaymentStatus: () => AppStatus.success,
              paymentCheckoutEntity: () => data,
            ),
          );
        } else {
          emit(
            state.copyWith(
              errorMessage: () =>
                  'Error in checkout payment, Contact with support.',
              checkOutPaymentStatus: () => AppStatus.failure,
            ),
          );
        }
      }, // Return the actual list on success
    );
  }

  Future<void> _onGetThrivveBankDetails(
      GetThrivveBankDetailsEvent event, Emitter<TopUpState> emit) async {
    emit(state.copyWith(
      getBankDataStatus: () => AppStatus.loading,
    ));

    final result = await getThrivveBankDetailsDataUseCase(NoParams());
    result.fold(
      (failure) {
        emit(state.copyWith(
          getBankDataStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (bankDetails) {
        emit(state.copyWith(
          getBankDataStatus: () => AppStatus.success,
          bankDetails: () => bankDetails,
        ));
      },
    );
  }

  Future<void> _onUploadReceiptImage(
      UploadReceiptImageEvent event, Emitter<TopUpState> emit) async {
    emit(state.copyWith(
      receiptImage: () => event.fileUrl,
      uploadImageStatus: () => AppStatus.success,
      isReceiptImageNotEmpty: () => true,
    ));
  }

  Future<void> _onDeleteReceiptImage(
      DeleteReceiptImageEvent event, Emitter<TopUpState> emit) async {
    emit(state.copyWith(
      receiptImage: () => null,
      isReceiptImageNotEmpty: () => false,
    ));
  }

  //_onSetTopUpAmount
  Future<void> _onSetTopUpAmount(
      SetTopUpAmount event, Emitter<TopUpState> emit) async {
    print('event.amount: ${event.amount}');
    emit(state.copyWith(
      confirmBtnStatus: () => event.amount != null && (event.amount ?? 0) > 0,
      topUpAmount: () => event.amount,
    ));
  }

  Future<void> _onMakeTopUpRequest(
      MakeTopUpRequest event, Emitter<TopUpState> emit) async {
    // check if any value is null
    if (((state.topUp?.payDirect ?? false)
            ? state.topUp?.amountTopUp == null
            : state.topUpAmount == null) ||
        state.receiptImage == null) {
      emit(state.copyWith(
        createRequestStatus: () => AppStatus.failure,
        errorMessage: () => 'please_fill_all_fields'.tr,
        confirmBtnStatus: () => false,
      ));
      return;
    }

    emit(state.copyWith(
        createRequestStatus: () => AppStatus.loading,
        confirmBtnStatus: () => false));

    final result = await makeTopUpRequestUseCase(
      TopUpParams(
          amount: (state.topUp?.payDirect ?? false)
              ? state.topUp?.amountTopUp ?? 0
              : state.topUpAmount ?? 0,
          receiptImage: state.receiptImage ?? '',
          applicationId: state.topUp?.applicationId,
          entryType: (state.topUp?.payDirect ?? false)
              ? TopUpEnum.deposit_account
              : TopUpEnum.current_account),
    );
    result.fold(
      (failure) {
        emit(state.copyWith(
          createRequestStatus: () => AppStatus.failure,
          confirmBtnStatus: () => true,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (data) {
        emit(state.copyWith(
          createRequestStatus: () => AppStatus.success,
          confirmationMessage: () => data,
        ));
      },
    );
  }
}
