import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

// add notes widget by list of string with icon and title
class TopUpInstructionWidget extends StatelessWidget {
  final String? title;
  final String? desc;
  final int step;

  const TopUpInstructionWidget({
    super.key,
    this.title,
    this.desc,
    required this.step,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 34.w,
          height: 34.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.numberBackgroundColor!
                .withOpacity(0.20), // Light purple circle background
          ),
          child: Center(
            child: CustomTextWidget(
                title: '$step',
                size: 13,
                fontWeight: FontWeight.w600,
                color: context.greenColor),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomFormattedText(
                  text: title ?? '',
                  style: TextStyle(
                      fontFamily: Get.locale?.languageCode == 'en'
                          ? "NotoSans"
                          : "NotoSansArabic",
                      fontSize: 12,
                      height: 1.3,
                      fontWeight: FontWeight.w700,
                      color: context.black)),
              SizedBox(height: 4.h),
              CustomTextWidget(
                  title: desc ?? '',
                  fontFamily: Get.locale?.languageCode == 'en'
                      ? "NotoSans"
                      : "NotoSansArabic",
                  size: 11,
                  height: 1.3,
                  fontWeight: FontWeight.w400,
                  color: context.lightBlack),
            ],
          ),
        ),
      ],
    );
  }
}
