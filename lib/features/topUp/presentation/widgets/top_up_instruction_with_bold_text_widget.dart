import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';

// add notes widget by list of string with icon and title
class TopUpInstructionWithBoldTextWidget extends StatelessWidget {
  final String? title;
  final int step;

  const TopUpInstructionWithBoldTextWidget({
    super.key,
    this.title,
    required this.step,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 34.w,
          height: 34.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.numberBackgroundColor!
                .withOpacity(0.20), // Light purple circle background
          ),
          child: Center(
            child: Text(
              '$step',
              style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600,
                  color: context.greenColor),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: CustomFormattedText(
              textAlign: WrapAlignment.start,
              text: title ?? '',
              strongStyle: TextStyle(
                  fontSize: 12,
                  height: 1.5,
                  fontFamily: Get.locale?.languageCode == 'en'
                      ? "NotoSans"
                      : "NotoSansArabic",
                  fontWeight: FontWeight.w700,
                  color: context.black),
              style: TextStyle(
                  fontSize: 12,
                  height: 1.5,
                  fontFamily: Get.locale?.languageCode == 'en'
                      ? "NotoSans"
                      : "NotoSansArabic",
                  fontWeight: FontWeight.w400,
                  color: context.black)),
        ),
      ],
    );
  }
}
