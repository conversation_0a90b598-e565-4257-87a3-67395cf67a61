import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/topUp/domain/entities/payment_method_entity.dart';
import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/features/topUp/presentation/widgets/payment_card_widget.dart';

class PaymentMethodsSheet extends StatelessWidget {
  const PaymentMethodsSheet({
    super.key,
    required this.payments,
    required this.paymentSelection,
    this.selectedPayment,
  });
  final List<PaymentMethodEntity> payments;
  final PaymentMethodEntity? selectedPayment;
  final void Function(PaymentMethodEntity payment) paymentSelection;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.bottomsheetColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //   _buildCloseButton(context),
              _headerPaymentMethods(context),
              // SizedBox(width: 40.w),
            ],
          ),
          SizedBox(height: 5.h),
          Divider(
            thickness: 1.h,
            color: context.black.withValues(alpha: 0.07),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            padding: EdgeInsetsDirectional.only(
              start: 16.w,
              end: 16.w,
              bottom: 18.h,
              top: 18.h,
            ),
            itemCount: payments.length,
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 18.h,
              );
            },
            itemBuilder: (context, index) {
              final method = payments[index];
              return PaymentMethodRadioCard(
                selectionCard: selectedPayment?.type ??
                    (Platform.isIOS
                        ? CardTypeEnum.apple_pay
                        : CardTypeEnum.google_pay),
                method: method,
                onTap: () => paymentSelection(method),
              );
            },
          ),
          SizedBox(height: 40.sp),
        ],
      ),
    );
  }

  Widget _headerPaymentMethods(BuildContext context) {
    return CustomTextWidget(
      title: 'please_select_a_payment_method'.tr.orDefault,
      fontWeight: FontWeight.w600,
      size: 15,
      paddingStart: 16.w,
      textAlign: TextAlign.center,
      color: context.black,
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(width: 16.w),
        Container(
          height: 40.h,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.appBackgroundColor,
          ),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.close,
              color: context.black,
              size: 20.w,
            ),
          ),
        ),
      ],
    );
  }
}
