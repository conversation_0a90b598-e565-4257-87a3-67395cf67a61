import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/identity_info_result.dart';
import '../repositories/identity_info_repository.dart';

class GetIdentityInfoUseCase implements UseCase<IdentityInfoResult?, NoParams> {
  final IdentityInfoRepository? _identityInfoRepository;

  GetIdentityInfoUseCase(this._identityInfoRepository);

  @override
  Future<Either<Failure, IdentityInfoResult?>?> call(NoParams params) async {
    return await _identityInfoRepository?.getIdentityInfo();
  }
}
