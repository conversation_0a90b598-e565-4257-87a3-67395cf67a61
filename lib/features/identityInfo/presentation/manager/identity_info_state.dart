part of 'identity_info_bloc.dart';

enum IdentityInfoStatus { initial, loading, success, failure }

class IdentityInfoState extends Equatable {
  const IdentityInfoState({
    this.saveDataStatus = IdentityInfoStatus.initial,
    this.status = IdentityInfoStatus.initial,
    this.uploadImageStatus = IdentityInfoStatus.initial,
    this.frontImageUrl,
    this.identityInfoData,
    this.isFrontImageEmpty = true,
    this.isDataSubmitReady = false,
    this.language,
    this.errorMessage,
    this.message,
    this.subtitle,
    this.isSubmitRejected = false,
  });

  final IdentityInfoStatus status;
  final IdentityInfoStatus saveDataStatus;
  final IdentityInfoStatus uploadImageStatus;
  final bool isSubmitRejected;
  final bool isFrontImageEmpty;
  final bool isDataSubmitReady;
  final String? errorMessage;
  final String? message;
  final String? subtitle;
  final String? language;
  final String? frontImageUrl;
  final IdentityInfoResult? identityInfoData;

  IdentityInfoState copyWith({
    IdentityInfoStatus Function()? status,
    IdentityInfoStatus Function()? saveDataStatus,
    IdentityInfoStatus Function()? uploadImageStatus,
    IdentityInfoResult? Function()? identityInfoData,
    bool Function()? isDataSubmitReady,
    bool Function()? isFrontImageEmpty,
    String? Function()? frontImageUrl,
    String? Function()? language,
    String? Function()? errorMessage,
    String? Function()? message,
    String? Function()? subtitle,
    bool Function()? isSubmitRejected,
  }) {
    return IdentityInfoState(
      saveDataStatus:
          saveDataStatus != null ? saveDataStatus() : this.saveDataStatus,
      isSubmitRejected:
          isSubmitRejected != null ? isSubmitRejected() : this.isSubmitRejected,
      identityInfoData:
          identityInfoData != null ? identityInfoData() : this.identityInfoData,
      uploadImageStatus: uploadImageStatus != null
          ? uploadImageStatus()
          : this.uploadImageStatus,
      status: status != null ? status() : this.status,
      isFrontImageEmpty: isFrontImageEmpty != null
          ? isFrontImageEmpty()
          : this.isFrontImageEmpty,
      frontImageUrl:
          frontImageUrl != null ? frontImageUrl() : this.frontImageUrl,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
      message: message != null ? message() : this.message,
      subtitle: subtitle != null ? subtitle() : this.subtitle,
      isDataSubmitReady: isDataSubmitReady != null
          ? isDataSubmitReady()
          : this.isDataSubmitReady,
      language: language != null ? language() : this.language,
    );
  }

  @override
  List<Object?> get props => [
        isSubmitRejected,
        saveDataStatus,
        isDataSubmitReady,
        identityInfoData,
        language,
        status,
        uploadImageStatus,
        errorMessage,
        message,
        subtitle,
        isFrontImageEmpty,
        frontImageUrl,
      ];
}
