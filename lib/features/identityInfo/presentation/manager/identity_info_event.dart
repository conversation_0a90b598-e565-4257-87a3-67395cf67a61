part of 'identity_info_bloc.dart';

@immutable
abstract class IdentityInfoEvent extends Equatable {
  const IdentityInfoEvent();

  @override
  List<Object?> get props => [];
}

class SaveIdentityInfoEvent extends IdentityInfoEvent {
  final int? notificationId;

  const SaveIdentityInfoEvent({
    required this.notificationId,
  });

  @override
  List<Object?> get props => [
        notificationId,
      ];
}

class GetIdentityInfoEvent extends IdentityInfoEvent {
  @override
  List<Object?> get props => [];
}

class DeleteFrontImageEvent extends IdentityInfoEvent {
  @override
  List<Object?> get props => [];
}

class UploadBackImageEvent extends IdentityInfoEvent {
  final String? filePath;

  const UploadBackImageEvent({this.filePath});

  @override
  List<Object?> get props => [filePath];
}

class UploadFrontImageEvent extends IdentityInfoEvent {
  final String? fileUrl;

  const UploadFrontImageEvent({this.fileUrl});

  @override
  List<Object?> get props => [fileUrl];
}
