import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';
import 'package:thrivve/features/products/presentation/widgets/new_product_widget.dart';

import '../../../../core/widget/app_error_message_widget.dart';
import '../manager/products_bloc.dart';

class ProductsPage extends StatelessWidget {
  ProductsPage({super.key});
  final personalInfo =
      (Get.arguments != null && Get.arguments["personalInfo"] != null)
          ? Get.arguments["personalInfo"] as PersonalInfoEntity?
          : null;
  final canWithdraw =
      (Get.arguments != null && Get.arguments["canWithdraw"] != null)
          ? Get.arguments["canWithdraw"] as bool?
          : null;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<ProductsBloc>()..add(const GetListOfProductsEvent()),
      child: Scaffold(
          body: ProductsList(
        canWithdraw: canWithdraw,
        personalInfo: personalInfo,
      )),
    );
  }
}

class ProductsList extends StatefulWidget {
  final PersonalInfoEntity? personalInfo;
  final bool? canWithdraw;
  const ProductsList({super.key, this.canWithdraw, this.personalInfo});

  @override
  State<ProductsList> createState() => _ProductsListState();
}

class _ProductsListState extends State<ProductsList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final offset = _scrollController.offset;
      final outOfRange = _scrollController.position.outOfRange;
      if (offset >= maxScrollExtent && !outOfRange) {
        if (context.read<ProductsBloc>().state.getListOfProductsStatuses !=
            ProductsStatuses.pageLoad) {
          context.read<ProductsBloc>().add(const GetListOfProductsEvent());
        }
      }
    });
  }

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProductsBloc, ProductsState>(
      listenWhen: (previous, current) =>
          current.getListOfProductsStatuses == ProductsStatuses.pageLoad,
      listener: (context, state) {
        _scrollToEnd();
      },
      child: BlocBuilder<ProductsBloc, ProductsState>(
        builder: (context, state) {
          switch (state.getListOfProductsStatuses) {
            case ProductsStatuses.loading:
              return ProductsShimmerLoading();
            case ProductsStatuses.failure:
              return AppErrorMessageWidget(
                error: state.errorMessage ?? '',
              );
            case ProductsStatuses.success:
            case ProductsStatuses.pageLoad:
              return state.listOfProducts?.isEmpty == true
                  ? Center(
                      child: CustomTextWidget(title: "no_products_found".tr),
                    )
                  : //
                  // ProductsShimmerLoading();
                  RefreshIndicator(
                      backgroundColor: context.whiteColor,
                      onRefresh: () async {
                        context
                            .read<ProductsBloc>()
                            .add(const GetListOfProductsEvent(refresh: true));
                      },
                      child: CustomScrollView(
                        controller: _scrollController,
                        slivers: [
                          CupertinoSliverNavigationBar(
                            backgroundColor: context.backgroundColor,
                            border: Border.all(color: Colors.transparent),
                            leading: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: context.appBackgroundColor,
                              ),
                              height: 40.h,
                              width: 40.w,
                              child: Material(
                                child: InkWell(
                                  onTap: () {
                                    Get.back();
                                  },
                                  child: Icon(
                                    Icons.arrow_back_rounded,
                                    size: 20.w,
                                    color: context.black,
                                  ),
                                ),
                              ),
                            ),
                            largeTitle: CustomTextWidget(
                              title: 'products'.tr,
                              color: context.black,
                              size: 22,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SliverToBoxAdapter(
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: 16.w,
                                right: 16.w,
                                bottom: 16.h,
                              ),
                              child: ListView.builder(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.zero,
                                itemCount: (state.getListOfProductsStatuses ==
                                        ProductsStatuses.pageLoad)
                                    ? (state.listOfProducts ?? []).length + 1
                                    : (state.listOfProducts ?? []).length,
                                itemBuilder: (BuildContext context, int index) {
                                  if (index ==
                                      (state.listOfProducts ?? []).length) {
                                    return Container(
                                      alignment: Alignment.center,
                                      width: 343,
                                      height: 112,
                                      margin: EdgeInsets.symmetric(vertical: 8),
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 10),
                                      decoration: BoxDecoration(
                                        color: context.bottomsheetColor,
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                      ),
                                      child: CircularProgressIndicator(),
                                    );
                                  }

                                  var product =
                                      (state.listOfProducts ?? [])[index];
                                  var backgroundColorLight = Color(int.parse(
                                    product.backgroundColorLight.replaceFirst(
                                      '#',
                                      '0xFF',
                                    ),
                                  ));
                                  var backgroundColorDark = Color(int.parse(
                                    product.backgroundColorDark.replaceFirst(
                                      '#',
                                      '0xFF',
                                    ),
                                  ));

                                  final id = product.id.toString();
                                  final applicationName =
                                      product.applicationName;
                                  return NewProductWidget(
                                    key: Key(applicationName == 'Work with Uber'
                                        ? applicationName ?? id
                                        : id),
                                    backgroundColorDark: backgroundColorDark,
                                    backgroundColorLight: backgroundColorLight,
                                    width: 343,
                                    height: 112,
                                    product: product,
                                    onTab: () {
                                      navigateTo(
                                          context,
                                          product,
                                          widget.personalInfo,
                                          widget.canWithdraw);
                                    },
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    );

            default:
              return const SizedBox();
          }
        },
      ),
    );
  }
}

class ProductsShimmerLoading extends StatelessWidget {
  const ProductsShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Shimmer.fromColors(
      key: const Key("productShimmerKey"),
      baseColor: context.borderAddBranch,
      highlightColor: context.appBackgroundColor,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(
            left: 16.w,
            right: 16.w,
            bottom: 16.h,
          ),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: AppBar().preferredSize.height,
                  child: Container(
                    height: 40.h,
                    width: 40.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: context.appBackgroundColor,
                    ),
                    child: Material(
                      child: InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.arrow_back_rounded,
                          color: Colors.black,
                          size: 20.w,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  height: 30,
                  width: 110,
                  color: Colors.black,
                ),
                SizedBox(
                  height: 10,
                ),
                ListView.builder(
                  padding: const EdgeInsets.all(0),
                  itemCount: 8,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      margin: EdgeInsets.symmetric(vertical: 8.sp),
                      height: 112.sp,
                      width: 343.sp,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
