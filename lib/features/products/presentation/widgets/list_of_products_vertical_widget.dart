import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';
import 'package:thrivve/features/products/presentation/widgets/product_widget.dart';
import 'package:thrivve/generated/assets.dart';

import '../../../../core/util/helper.dart';
import '../../../dashboard/domain/entities/product.dart';
import '../manager/products_bloc.dart';

class ListOfProductsVerticalWidget extends StatelessWidget {
  final List<Product> listOfProducts;
  final PersonalInfoEntity? personalInfo;
  final bool? isInstantPaymentIsEnable;

  const ListOfProductsVerticalWidget({
    super.key,
    required this.listOfProducts,
    required this.personalInfo,
    required this.isInstantPaymentIsEnable,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        bottom: 16.h,
      ),
      child: RefreshIndicator(
        onRefresh: () async {
          context.read<ProductsBloc>().add(const GetListOfProductsEvent());
        },
        child: GridView.builder(
          padding: EdgeInsets.zero,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            // mainAxisExtent: 164,
          ),
          itemCount: listOfProducts.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (BuildContext context, int index) {
            var product = listOfProducts[index];
            var backgroundColorLight = Color(int.parse(
              product.backgroundColorLight.replaceFirst(
                '#',
                '0xFF',
              ),
            ));
            var backgroundColorDark = Color(int.parse(
              product.backgroundColorDark.replaceFirst(
                '#',
                '0xFF',
              ),
            ));

            return ProductWidget(
              key: Key(product.id.toString()),
              backgroundColorLight: backgroundColorLight,
              backgroundColorDark: backgroundColorDark,
              width: 164,
              height: 164,
              product: product,
              onTab: () {
                navigateTo(
                    context, product, personalInfo, isInstantPaymentIsEnable);
              },
            );
          },
        ),
      ),
    );
  }
}

Widget _divider(double value, BuildContext context) {
  return Divider(
    thickness: value,
    height: value,
    color: context.black.withValues(alpha: 0.08),
  );
}

void showInfoBottomSheet(
    BuildContext context, String? title, String? description) {
  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return Container(
        decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0))),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title ?? "",
                      style: TextStyle(
                        color: context.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      key: const Key("iconBtn"),
                      icon: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: Colors.grey.withOpacity(0.10),
                        ),
                        child: Icon(
                          Icons.close,
                          size: 18,
                          color: Colors.grey,
                          key: Key("backIcon"),
                        ),
                      ),
                      color: context.black,
                      onPressed: () {
                        Get.back();
                      },
                    ),
                  ],
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  description ?? "",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: context.black,
                  ),
                ),
                const SizedBox(
                  height: 40,
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

void showInfoBottomSheeLeaseToOwn(
    BuildContext context, String? title, String? description,
    [String? buttonTitle]) {
  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return Container(
        decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: 10.w),
                IconButton(
                  padding: EdgeInsetsDirectional.zero,
                  onPressed: () {
                    Get.back();
                  },
                  icon: SvgPicture.asset(
                    Assets.thrivvePhotosIconCloseSoon,
                    width: 16.w,
                    height: 16.w,
                    colorFilter:
                        ColorFilter.mode(context.black, BlendMode.srcIn),
                    key: Key("backIcon"),
                  ),
                )
              ],
            ),
            _divider(4, context),
            SizedBox(height: 40.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomTextWidget(
                  title: title ?? '',
                  color: context.black,
                  size: 17,
                  paddingStart: 20.w,
                  paddingEnd: 20.w,
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
            SizedBox(height: 20.h),
            CustomTextWidget(
              title: description ?? "",
              size: 12,
              fontWeight: FontWeight.w400,
              paddingStart: 20.w,
              paddingEnd: 20.w,
              textAlign: TextAlign.start,
              color: context.black,
            ),
            SizedBox(height: 40.h),
            CustomButton(
              margin: EdgeInsetsDirectional.only(
                start: 20.w,
                end: 20.w,
              ),
              enabled: true,
              text: (buttonTitle ?? 'got_it').tr,
              onPressed: () {
                Get.back();
              },
              colorText: Colors.white,
            ),
            SizedBox(
                height:
                    MediaQuery.of(context).padding.bottom > 0 ? 20.h : 40.h),
          ],
        ),
      );
    },
  );
}
