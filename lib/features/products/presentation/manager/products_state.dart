part of 'products_bloc.dart';

enum ProductsStatuses { initial, loading, success, failure, pageLoad }

class ProductsState extends Equatable {
  ProductsState(
      {this.getListOfProductsStatuses = ProductsStatuses.initial,
      this.errorMessage,
      this.listOfProducts,
      this.page = 1,
      this.viewMore = true,
      this.perpage = 10});

  final List<Product>? listOfProducts;
  int page;
  int perpage;
  bool viewMore;

  final ProductsStatuses getListOfProductsStatuses;
  final String? errorMessage;

  ProductsState copyWith({
    ProductsStatuses? getListOfProductsStatuses,
    String? errorMessage,
    List<Product>? listOfProducts,
    bool viewMore = true,
    int page = 1,
  }) {
    return ProductsState(
      getListOfProductsStatuses:
          getListOfProductsStatuses ?? this.getListOfProductsStatuses,
      errorMessage: errorMessage ?? this.errorMessage,
      listOfProducts: listOfProducts ?? this.listOfProducts,
      viewMore: viewMore,
      page: page,
    );
  }

  @override
  List<Object?> get props => [
        getListOfProductsStatuses,
        viewMore,
        errorMessage,
        listOfProducts,
        page,
        perpage
      ];
}
