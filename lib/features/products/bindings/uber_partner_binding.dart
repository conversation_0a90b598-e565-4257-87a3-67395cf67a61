import 'package:get/get.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/data_sources/i_uber_partner_data_source.dart';
import 'package:thrivve/features/uber_flow/uber_partner/data/data_sources/uber_partner_data_source_impl.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/repositories/i_uber_partner_repository.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/repositories/uber_partner_repository_impl.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/use_cases/create_work_with_uber_use_case.dart';
import 'package:thrivve/features/uber_flow/uber_partner/domain/use_cases/get_uber_benefits_and_term_use_case.dart';
import 'package:thrivve/features/uber_flow/uber_partner/presentation/manager/uber_partner_controller.dart';

class UberPartnerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<IUberPartnerDataSource>(
      () => UberPartnerDataSourceImpl(
        getIt(),
      ),
    );

    // Repositories
    Get.lazyPut<IUberPartnerRepository>(
      () => UberPartnerRepositoryImpl(
        Get.find<IUberPartnerDataSource>(),
        getIt(),
      ),
    );

    // Use cases
    Get.lazyPut<GetUberBenefitsAndTermUseCase>(
      () => GetUberBenefitsAndTermUseCase(
        iUberPartnerRepository: Get.find<IUberPartnerRepository>(),
      ),
    );
    Get.lazyPut<CreateWorkWithUberUseCase>(
      () => CreateWorkWithUberUseCase(
        repository: Get.find<IUberPartnerRepository>(),
      ),
    );
    // Controllers
    Get.lazyPut<UberPartnerController>(
      () => UberPartnerController(
        createWorkWithUberUseCase: Get.find<CreateWorkWithUberUseCase>(),
        getUberBenefitsAndTermUseCase:
            Get.find<GetUberBenefitsAndTermUseCase>(),
        iPageLoadingDialog: Get.find<IPageLoadingDialog>(),
      ),
    );
  }
}
