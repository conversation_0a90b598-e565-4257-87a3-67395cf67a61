import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/helper.dart';
import '../../../../core/widget/button.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/city.dart';
import '../manager/personal_info_bloc.dart';
import '../widgets/camera_gallery_bottom_sheet.dart';
import '../widgets/profile_image_widge.dart';

class PersonalInfoPage extends StatefulWidget {
  const PersonalInfoPage({super.key});

  @override
  State<PersonalInfoPage> createState() => _PersonalInfoPageState();
}

class _PersonalInfoPageState extends State<PersonalInfoPage> {
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  late PersonalInfoBloc personalInfoBloc;

  @override
  void dispose() {
    fullNameController.dispose();
    addressController.dispose();
    super.dispose();
  }

  void _onTextFieldChanged() {
    personalInfoBloc.add(TextFieldChangedEvent(
      name: fullNameController.text,
      city: personalInfoBloc.state.selectedCity,
      address: addressController.text,
      image: personalInfoBloc.state.selectedImageUrl,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) {
            personalInfoBloc = getIt<PersonalInfoBloc>();
            personalInfoBloc.add(const GetUserInfoEvent());
            return personalInfoBloc;
          },
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<PersonalInfoBloc, PersonalInfoState>(
            listenWhen: (previous, current) =>
                previous.submitDataStatus != current.submitDataStatus,
            listener: (context, state) {
              switch (state.submitDataStatus) {
                case AppStatus.success:
                  dismissLoaderDialog(context);
                  showGeneralBottomSheet(
                    title: "successfully_submitted".tr,
                    description: state.personalInfoUpdateMsj.toString(),
                    firstBtnText: "got_it".tr,
                    firstBtnOnClick: () {
                      Get.back();
                      Get.back(result: true);
                    },
                    context: context,
                    icon: Assets.thrivvePhotosSuccess,
                  );
                  break;
                case AppStatus.failure:
                  dismissLoaderDialog(context);
                  errorSnackBar(
                      context: context,
                      title: "err".tr,
                      message: state.errorMessage.toString());
                  break;
                default:
                  break;
              }
            },
          ),
          BlocListener<PersonalInfoBloc, PersonalInfoState>(
            listenWhen: (previous, current) =>
                previous.uploadImageStatus != current.uploadImageStatus,
            listener: (context, state) {
              switch (state.uploadImageStatus) {
                case AppStatus.loading:
                  break;
                case AppStatus.success:
                  _onTextFieldChanged();
                  break;
                case AppStatus.failure:
                  break;
                default:
                  break;
              }
            },
          ),
          BlocListener<PersonalInfoBloc, PersonalInfoState>(
            listenWhen: (previous, current) =>
                previous.personalInfoData != current.personalInfoData,
            listener: (context, state) {
              setInitData(state);
            },
          ),
        ],
        child: BlocBuilder<PersonalInfoBloc, PersonalInfoState>(
          builder: (context, state) {
            return Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                surfaceTintColor: Colors.transparent,
                leadingWidth: 66.w,
                leading: Center(
                  child: Container(
                    height: 40.h,
                    width: 40.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: context.appBackgroundColor,
                    ),
                    child: IconButton(
                        onPressed: () {
                          if (state.submitDataStatus == AppStatus.success) {
                            Get.back(result: true);
                          } else {
                            Get.back();
                          }
                        },
                        icon: Icon(
                          Icons.arrow_back_rounded,
                          color: context.black,
                          size: 20.w,
                        )),
                  ),
                ),
              ),
              body: BlocBuilder<PersonalInfoBloc, PersonalInfoState>(
                builder: (context, state) {
                  switch (state.status) {
                    case AppStatus.loading:
                      return buildShimmerLoadingState();
                    case AppStatus.success:
                      return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: SingleChildScrollView(
                                child: Padding(
                                  padding: EdgeInsets.all(16.w),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Text(
                                        'personal_information'.tr,
                                        key: ValueKey("shady_key"),
                                        style: TextStyle(
                                          fontSize: 22.sp,
                                          fontWeight: FontWeight.w600,
                                          color: context.black,
                                        ),
                                      ),
                                      SizedBox(height: 28.h),
                                      Center(
                                          child: ProfileImageWithShimmer(
                                        onFileUploaded: (filePath) {
                                          context.read<PersonalInfoBloc>().add(
                                                UploadImageEvent(
                                                    filePath: filePath ?? ''),
                                              );
                                        },
                                        imageUrl: state.selectedImageUrl,
                                        onClearImage: () {
                                          context
                                              .read<PersonalInfoBloc>()
                                              .add(ClearImageEvent());
                                        },
                                      )),
                                      SizedBox(height: 16.h),
                                      Text(
                                        'nick_name'.tr,
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w400,
                                          color: context.black,
                                        ),
                                      ),
                                      SizedBox(height: 8.h),
                                      TextFormField(
                                        key: ValueKey("id_name_hint"),
                                        style: TextStyle(color: context.black),
                                        controller: fullNameController,
                                        textInputAction: TextInputAction.next,
                                        decoration: InputDecoration(
                                            floatingLabelBehavior:
                                                FloatingLabelBehavior.never,
                                            labelText: 'id_name_hint'.tr,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 16.w,
                                                    vertical: 14.h),
                                            labelStyle: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.w400,
                                              color: context.lightBlack,
                                            ),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.r),
                                              borderSide: BorderSide(
                                                  color: context.black!),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10.r),
                                              borderSide:
                                                  BorderSide(color: Colors.red),
                                            ),
                                            error: state.nameErrorMessage !=
                                                    null
                                                ? Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Icon(
                                                        Icons
                                                            .error_outline_rounded,
                                                        color: Colors.red,
                                                        size: 16.w,
                                                      ),
                                                      SizedBox(width: 4.w),
                                                      Expanded(
                                                        child: Text(
                                                          state.nameErrorMessage ??
                                                              "",
                                                          style: TextStyle(
                                                            color: Colors.red,
                                                            fontSize: 11.sp,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )
                                                : null),
                                      ),
                                      SizedBox(height: 16.h),
                                      Text(
                                        "city_of_residence".tr,
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w400,
                                            color: context.black),
                                      ),
                                      SizedBox(height: 8.h),
                                      Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: context.lightBlack!,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(6.r),
                                        ),
                                        child: DropdownButtonFormField<City>(
                                          key: ValueKey("id_city_hint"),
                                          value: state.selectedCity,
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 16.w,
                                                    vertical: 14.h),
                                            hintText: 'id_city_hint'.tr,
                                            hintStyle: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.w400,
                                              color: context.lightBlack,
                                            ),
                                          ),
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w400,
                                            color: context.black,
                                          ),
                                          items: state.listOfCites
                                              ?.map((City value) {
                                            return DropdownMenuItem<City>(
                                              value: value,
                                              child: Text(
                                                  Get.locale?.languageCode ==
                                                          "en"
                                                      ? value.nameEn ?? ""
                                                      : value.nameAr ?? "",
                                                  style: TextStyle(
                                                    fontSize: 14.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: context.black,
                                                  )),
                                            );
                                          }).toList(),
                                          onChanged: (City? value) {
                                            personalInfoBloc
                                                .add(TextFieldChangedEvent(
                                              name: fullNameController.text,
                                              city: value,
                                              address: addressController.text,
                                              image: personalInfoBloc
                                                  .state.selectedImageUrl,
                                            ));
                                          },

                                          // change the background of the dropdown and make the menu scrollable
                                          dropdownColor: context.whiteColor,
                                          isExpanded: true,
                                        ),
                                      ),
                                      SizedBox(height: 16.h),
                                      Text(
                                        'address'.tr,
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w400,
                                          color: context.black,
                                        ),
                                      ),
                                      SizedBox(height: 8.h),
                                      TextField(
                                        key: ValueKey("id_address_hint"),
                                        style: TextStyle(color: context.black),
                                        controller: addressController,
                                        decoration: InputDecoration(
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 16.w, vertical: 14.h),
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.never,
                                          labelText: 'id_address_hint'.tr,
                                          labelStyle: TextStyle(
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w400,
                                            color: context.lightBlack,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10.r),
                                            borderSide:
                                                BorderSide(color: Colors.grey),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 16.h),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Divider(
                                  color: context.borderLeaseColor,
                                  thickness: 0.4.h,
                                  height: 0,
                                ),
                                SizedBox(height: 16.h),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: Button(
                                    key: ValueKey("save_changes_btn"),
                                    widget: state.submitDataStatus ==
                                            AppStatus.loading
                                        ? SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2,
                                            ))
                                        : null,
                                    enable: state.isDataSubmitReady,
                                    text: "save_changes_btn".tr,
                                    onTab: () {
                                      context
                                          .read<PersonalInfoBloc>()
                                          .add(SavePersonalInfoEvent(
                                            imageUrl: state.selectedImageUrl,
                                            fullName: fullNameController.text,
                                            city: state.selectedCity?.id
                                                .toString(),
                                            address: addressController.text,
                                          ));
                                    },
                                    height: 36.h,
                                  ),
                                ),
                                SizedBox(height: 26.h),
                              ],
                            ),
                          ]);
                    case AppStatus.failure:
                      return Center(
                        child: Text(state.errorMessage.toString()),
                      );

                    default:
                      return Container();
                  }
                },
              ),
            );
          },
        ),
      ),
    );
  }

  Widget buildShimmerTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: context.borderAddBranch!,
          highlightColor: context.appBackgroundColor!,
          child: Container(
            height: 14.sp,
            width: 100.w,
            color: Colors.grey[300],
          ),
        ),
        SizedBox(height: 8.h),
        Shimmer.fromColors(
          baseColor: context.borderAddBranch!,
          highlightColor: context.appBackgroundColor!,
          child: Container(
            height: 50.h,
            width: double.infinity,
            color: Colors.grey[300],
          ),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }

  Widget buildShimmerButton() {
    return Shimmer.fromColors(
      baseColor: context.borderAddBranch!,
      highlightColor: context.appBackgroundColor!,
      child: Container(
        height: 36.h,
        width: double.infinity,
        color: Colors.grey[300],
      ),
    );
  }

  Widget buildShimmerLoadingState() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildShimmerProfileSection(),
          SizedBox(height: 28.h),
          buildShimmerTextField(),
          SizedBox(height: 28.h),
          buildShimmerTextField(),
          SizedBox(height: 28.h),
          buildShimmerTextField(),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget buildShimmerProfileSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 28.h),
        Center(
          child: Shimmer.fromColors(
            baseColor: context.borderAddBranch!,
            highlightColor: context.appBackgroundColor!,
            child: Container(
              height: 80.h,
              width: 80.w,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void setInitData(PersonalInfoState state) {
    fullNameController.text = state.personalInfoData?.fullName ?? "";
    addressController.text = state.personalInfoData?.address ?? "";
    fullNameController.addListener(_onTextFieldChanged);
    addressController.addListener(_onTextFieldChanged);
  }

  showSelectOptionBottomSheet(BuildContext context) {
    final personalInfoBloc = BlocProvider.of<PersonalInfoBloc>(context);

    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return BlocProvider.value(
          value: personalInfoBloc,
          child: BlocBuilder<PersonalInfoBloc, PersonalInfoState>(
            builder: (context, state) {
              return CameraGalleryFileBottomSheet(
                onFileSelected: (filePath) async {
                  if (filePath?.isNotEmpty == true) {
                    context
                        .read<PersonalInfoBloc>()
                        .add(UploadImageEvent(filePath: filePath));
                  }
                },
              );
            },
          ),
        );
      },
    );
  }
}
