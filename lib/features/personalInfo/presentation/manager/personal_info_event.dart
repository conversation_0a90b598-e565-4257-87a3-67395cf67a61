part of 'personal_info_bloc.dart';

@immutable
abstract class PersonalInfoEvent extends Equatable {
  const PersonalInfoEvent();

  @override
  List<Object?> get props => [];
}

class SavePersonalInfoEvent extends PersonalInfoEvent {
  final String? imageUrl;
  final String? fullName;
  final String? city;
  final String? address;

  const SavePersonalInfoEvent({
    this.imageUrl,
    this.fullName,
    this.city,
    this.address,
  });

  @override
  List<Object?> get props => [
        imageUrl,
        fullName,
        city,
        address,
      ];
}

class ClearImageEvent extends PersonalInfoEvent {
  const ClearImageEvent();

  @override
  List<Object?> get props => [];
}

class SelectCityEvent extends PersonalInfoEvent {
  final City? selectedCity;

  const SelectCityEvent({this.selectedCity});

  @override
  List<Object?> get props => [selectedCity];
}

class TextFieldChangedEvent extends PersonalInfoEvent {
  final String? name;
  final City? city;
  final String? address;
  final String? image;

  const TextFieldChangedEvent({
    required this.name,
    required this.city,
    required this.address,
    required this.image,
  });
}


class UploadImageEvent extends PersonalInfoEvent {
  final String? filePath;

  const UploadImageEvent({this.filePath});

  @override
  List<Object?> get props => [filePath];
}

class GetUserInfoEvent extends PersonalInfoEvent {
  const GetUserInfoEvent();

  @override
  List<Object?> get props => [];
}
