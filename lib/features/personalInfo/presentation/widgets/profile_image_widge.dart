import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/upload_image/custom_file_upload_widget.dart';
import 'package:thrivve/core/upload_image/file_upload_controller.dart';

import '../../../../generated/assets.dart';

class ProfileImageWithShimmer extends StatelessWidget {
  final String? imageUrl;
  final VoidCallback? onClearImage;
  final Function(String? filePath) onFileUploaded;

  const ProfileImageWithShimmer({
    super.key,
    this.imageUrl,
    this.onClearImage,
    required this.onFileUploaded,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: Alignment.center, children: [
      CustomFileUploadWidget(
        controllerId: 'profileImageWidget',
        onFileSelected: (filePath) {
          Get.back();
          if (filePath?.isNotEmpty == true) {
            if (filePath != null) {
              Get.find<FileUploadController>(tag: 'profileImageWidget')
                  .uploadFile(filePath);
            }
          }
        },
        onFileUploaded: onFileUploaded,
        showPreview: false,
        width: 80.w,
        height: 80.h,
        title: '',
        description: '',
        onFileDeleted: () {
          onClearImage?.call();
        },
        initialFileUrl: imageUrl,
        backgroundColor: Colors.transparent,
        isFile: false,
        borderRadius: 50,
        showDeleteButtonAsOverlay: true,
        deleteButtonSize: 20.w,
        deleteButtonPosition: Positioned(
          bottom: -2,
          right: -2,
          child: GestureDetector(
            onTap: () {
              Get.find<FileUploadController>(tag: 'profileImageWidget')
                  .cancelUpload();
              Get.find<FileUploadController>(tag: 'profileImageWidget')
                  .deleteFile();
              onClearImage?.call();
            },
            child: Center(
              child: Container(
                height: 20.h,
                width: 20.w,
                decoration: BoxDecoration(
                  color: context.borderLeaseColor,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: context.whiteColor,
                    width: 2.w,
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.close,
                    color: context.black,
                    size: 12.w,
                  ),
                ),
              ),
            ),
          ),
        ),
        customWidget: buildPlaceholder(context),
      ),
    ]);
  }

// Helper method to build the placeholder container
}

Widget buildPlaceholder(BuildContext context) {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Container(
        height: 78.h,
        width: 78.w,
        decoration: BoxDecoration(
          color: context.appBackgroundColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Image.asset(
            Assets.thrivvePhotosPerson,
            height: 40.h,
            width: 40.w,
            color: context.black,
            fit: BoxFit.cover,
          ),
        ),
      ),
    ],
  );
}
