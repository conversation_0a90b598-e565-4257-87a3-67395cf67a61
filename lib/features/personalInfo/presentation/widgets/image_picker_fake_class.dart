import 'dart:developer';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
class FakeImagePicker extends ImagePicker {
  @override
  Future<XFile?> pickImage({
    required ImageSource source,
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    CameraDevice preferredCameraDevice = CameraDevice.rear,
    bool requestFullMetadata = true,
  }) async {
    final tempDir = await getTemporaryDirectory();
    final tempFilePath = path.join(tempDir.path, 'visa.png');
    final tempFile = File(tempFilePath);

    if (!await tempFile.exists()) {
      final assetData = await rootBundle.load('assets/thrivvePhotos/visa.png');
      await tempFile.writeAsBytes(assetData.buffer.asUint8List());
    }

    log('FakeImagePicker: Using project asset, copied to $tempFilePath');
    return XFile(tempFilePath);
  }
}