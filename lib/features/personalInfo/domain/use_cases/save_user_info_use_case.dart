import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';

import '../../../../core/user_cases/user_case.dart';
import '../../data/models/personal_info_result_model.dart';
import '../repositories/personal_info_repository.dart';

class SaveUserInfoUseCase
    implements UseCase<PersonalInfoResultModel?, SaveUserInfoParams> {
  final PersonalInfoRepository? _saveUserInfoRepository;

  SaveUserInfoUseCase(this._saveUserInfoRepository);

  @override
  Future<Either<Failure, PersonalInfoResultModel?>?> call(
      SaveUserInfoParams params) async {
    return await _saveUserInfoRepository?.saveUserInfo(
      fullName: params.fullName,
      imageUrl: params.imageUrl,
      city: params.city,
      address: params.address,
    );
  }
}

class SaveUserInfoParams {
  String? fullName;
  String? imageUrl;
  String? city;
  String? address;

  SaveUserInfoParams(
      {this.fullName,
      this.imageUrl,
      this.city,
      this.address});
}
