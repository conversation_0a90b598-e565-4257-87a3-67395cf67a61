import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:thrivve/core/app_client/app_client.dart';

import '../../../../core/api/api_settings.dart';
import '../../../../core/error/exceptions.dart';
import '../../../identityInfo/data/models/identity_info_model.dart';
import '../../../identityInfo/data/models/identity_info_result_model.dart';
import '../../../identityInfo/domain/entities/identity_info_result.dart';
import '../../domain/entities/city.dart';
import '../models/city_model.dart';
import '../models/file_result_model.dart';
import '../models/personal_info_model.dart';
import '../models/personal_info_result_model.dart';

abstract class PersonalAndIdentityInfoRemoteDataSource {
  /// Call the {{sdd_base_url}}/api/v1/report/captain/earning/pdf?captain_id=1555&from_date=2022-01-01&to_date=2024-10-11 endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<PersonalInfoModel?>? getPersonalInfo();

  /// Call the {{sdd_base_url}}/api/v1/report/captain/earning/pdf?captain_id=1555&from_date=2022-01-01&to_date=2024-10-11 endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<List<City>?>? getListOfCitiesByCountryId({required int? countryId});

  /// Call the {{sdd_base_url}}/api/v1/report/captain/earning/details/pdf?captain_id=1555&from_date=2022-01-01&to_date=2024-10-11 endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<PersonalInfoResultModel?>? setPersonalInfo({
    String? image,
    String? address,
    String? fullName,
    String? cityId,
  });

  /// Call the {{sdd_base_url}}/api/v1/report/lOVs endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<IdentityInfoResult?>? getIdentityInfo();

  /// Call the {{sdd_base_url}}/api/v1/report/captain/vehicle/rent/pdf?
  /// captain_id=1555&vehicle_id=71&destination_rent=1&advance_payment=244&management_fees=353&total_price=66 endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<IdentityInfoResultModel?>? setIdentityInfo({
    required String? identityFrontAttachment,
  });

  /// Call the {{sdd_base_url}}/api/v1/report/captain/vehicle/rent/pdf?
  /// captain_id=1555&vehicle_id=71&destination_rent=1&advance_payment=244&management_fees=353&total_price=66 endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<String?>? uploadImage({
    FormData? filePath,
    CancelToken? cancelToken,
    void Function(int, int)? onProgress,
  });
}

class PersonalAndIdentityInfoRemoteDataSourceImpl
    implements PersonalAndIdentityInfoRemoteDataSource {
  final ApiClient apiClient;

  PersonalAndIdentityInfoRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<IdentityInfoResult?>? getIdentityInfo() async {
    final response = await apiClient.request<IdentityInfoResult>(
      endpoint: ApiSettings.thrivveGetIdentityInfo,
      fromJson: (json) => IdentityInfoModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<PersonalInfoModel?>? getPersonalInfo() async {
    final response = await apiClient.request<PersonalInfoModel>(
      endpoint: ApiSettings.thrivvePersonalInfo,
      fromJson: (json) => PersonalInfoModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<PersonalInfoResultModel?>? setPersonalInfo({
    String? image,
    String? address,
    String? fullName,
    String? cityId,
  }) async {
    final response = await apiClient.request<PersonalInfoResultModel>(
      endpoint: ApiSettings.thrivvePersonalInfoV2,
      method: RequestType.post,
      data: {
        "image": image,
        "address": address,
        "full_name": fullName,
        "city_id": cityId,
      },
      fromJson: (json) => PersonalInfoResultModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<IdentityInfoResultModel?>? setIdentityInfo({
    required String? identityFrontAttachment,
  }) async {
    final response = await apiClient.request<IdentityInfoResultModel>(
      endpoint: ApiSettings.thrivveEditIdentityInfo,
      method: RequestType.post,
      data: jsonEncode({
        "identity_front_attachment": identityFrontAttachment,
      }),
      fromJson: (json) => IdentityInfoResultModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<String?>? uploadImage({
    FormData? filePath,
    CancelToken? cancelToken,
    void Function(int, int)? onProgress,
  }) async {
    final response = await apiClient.request<FileResultResultModel>(
      endpoint: ApiSettings.thrivveUploadFile,
      method: RequestType.post,
      data: filePath,
      onSendProgress: onProgress,
      cancelToken: cancelToken,
      fromJson: (json) => FileResultResultModel.fromJson(json),
    );
    return response.data?.fileUrl;
  }

  @override
  Future<List<City>?>? getListOfCitiesByCountryId({
    required int? countryId,
  }) async {
    final response = await apiClient.request<CityListModel>(
      endpoint: ApiSettings.getCitiesByCountry,
      queryParameters: {"country_id": countryId},
      fromJson: (json) => CityListModel.fromJson(json),
    );
    return response.data?.cities;
  }
}
