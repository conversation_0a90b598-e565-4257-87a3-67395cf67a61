import '../../domain/entities/car_details.dart';
import 'list_of_car_model.dart';

class CarModel extends CarDetails {
  @override
  final String? carName;
  final String? carImage;
  final String? carModelYear;
  @override
  final int? carId;

  CarModel({
    this.carName,
    this.carImage,
    this.carModelYear,
    this.carId,
  }) : super(carId, carName, carModelYear, carImage, '', '', '', null);

  factory CarModel.fromJson(Map<String, dynamic> jsonMap) {
    return CarModel(
        carName: jsonMap["manufacturer_model"]["manufacturer_model_name_en"],
        carImage: jsonMap["main_image"]["url"],
        carId: jsonMap["id"],
        carModelYear: jsonMap["production_year"].toString());
  }

  factory CarModel.fromObject(Bike object) {
    return CarModel(
      carName: object.vehicle?.manufacturerModel?.manufacturerModelNameEn,
      carImage: object.vehicle?.mainImage?.url,
      carModelYear: object.vehicle?.productionYear.toString(),
      carId: object.vehicleId,
    );
  }
}
