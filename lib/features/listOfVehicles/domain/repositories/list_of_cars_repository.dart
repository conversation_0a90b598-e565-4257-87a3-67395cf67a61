import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/filter.dart';
import '../entities/vehicle_list_object.dart';
import '../../../vehicleDetails/domain/entities/link.dart';
import '../../../vehicleDetails/domain/entities/vehicle.dart';
import '../entities/city.dart';
import '../entities/country.dart';

abstract class ListOfCarsRepository {


  Future<Either<Failure, List<VehicleListObject>?>?>? getListOfVehicles({
    int? brandId,
    String? searchText,
    int? page,
    int? perPage,
  });


  Future<Either<Failure, Vehicle?>?>? getVehicleDetails(int? carId);

  Future<Either<Failure, String?>?>? setDriveToOwnRequest({int? driveWithUs});

  Future<Either<Failure, String?>?>? applyAsALead({
    String? mobileNumber,
    String? productId,
    String? appVersion,
    bool? isAuth,
    int? itemId,
  });

  Future<Either<Failure, List<Country>?>?>? getListOfCountry();

  Future<Either<Failure, List<City>?>?>? getCitiesOfCountry(int? countryId);

  // getListOfFilter
  Future<Either<Failure, List<Filter?>?>?>? getListOfFilter();

  //getContactUSLinks
  Future<Either<Failure, Link?>?>? getContactUSLinks();
}
