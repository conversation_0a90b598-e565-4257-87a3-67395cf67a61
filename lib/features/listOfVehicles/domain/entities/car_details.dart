import 'package:equatable/equatable.dart';

import 'vehicle_feature.dart';

class CarDetails extends Equatable {
  int? carId;
  String? carName;
  String? carModel;
  String? carMainImage;
  String? carGearType;
  String? carFuel;
  String? carType;
  List<VehicleFeature>? carFeature;

  CarDetails(
    this.carId,
    this.carName,
    this.carModel,
    this.carMainImage,
    this.carGearType,
    this.carFuel,
    this.carType,
    this.carFeature,
  );

  CarDetails.dummyData() {
    carId = 3;
    carName = "Hyundai Accent";
    carModel = "2018";
    carMainImage =
        "https://www.freepnglogos.com/uploads/car-png/car-png-large-images-40.png";
    carGearType = "Automatic";
    carFuel = "1.4L";
    carType = "Sedan";
  }

  @override
  List<Object?> get props => [
        carId,
        carName,
        carModel,
        carMainImage,
        carGearType,
        carFuel,
        carType,
        carFeature
      ];
}
