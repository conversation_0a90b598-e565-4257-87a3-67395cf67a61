import 'package:equatable/equatable.dart';

class Filter extends Equatable {
  final int? id;
  final String? title;
  final String? icon;
  final bool isSelected;

  const Filter({
    required this.id,
    this.title,
    this.icon,
    this.isSelected = false,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        isSelected,
        icon,
      ];

  Filter copyWith({
    String? title,
    int? id,
    bool? isSelected,
    String? icon,
  }) {
    return Filter(
      id: id ?? this.id,
      title: title ?? this.title,
      isSelected: isSelected ?? this.isSelected,
      icon: icon ?? this.icon,
    );
  }
}
