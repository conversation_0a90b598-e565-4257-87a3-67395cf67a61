part of 'list_of_vehicles_bloc.dart';

class ListOfVehiclesState extends Equatable {
  const ListOfVehiclesState({
    this.getListOfProductsStatuses = AppStatus.initial,
    this.getListOfFilterStatuses = AppStatus.initial,
    this.errorMessage,
    this.listOfVehicles,
    this.filters,
    this.selectFilter,
    this.showLargeCells,
  });

  final List<VehicleListObject>? listOfVehicles;
  final List<Filter?>? filters;
  final Filter? selectFilter;

  final AppStatus getListOfProductsStatuses;
  final AppStatus getListOfFilterStatuses;
  final String? errorMessage;
  final bool? showLargeCells;

  ListOfVehiclesState copyWith({
    AppStatus? getListOfProductsStatuses,
    AppStatus? getListOfFilterStatuses,
    String? errorMessage,
    bool? Function()? showLargeCells,
    List<VehicleListObject>? Function()? listOfVehicles,
    List<Filter?>? filters,
    Filter? selectFilter,
  }) {
    return ListOfVehiclesState(
      getListOfProductsStatuses:
          getListOfProductsStatuses ?? this.getListOfProductsStatuses,
      showLargeCells:
          showLargeCells != null ? showLargeCells() : this.showLargeCells,
      getListOfFilterStatuses:
          getListOfFilterStatuses ?? this.getListOfFilterStatuses,
      errorMessage: errorMessage ?? this.errorMessage,
      listOfVehicles:
          listOfVehicles != null ? listOfVehicles() : this.listOfVehicles,
      selectFilter: selectFilter ?? this.selectFilter,
      filters: filters ?? this.filters,
    );
  }

  @override
  List<Object?> get props => [
        getListOfProductsStatuses,
        getListOfFilterStatuses,
        showLargeCells,
        selectFilter,
        errorMessage,
        listOfVehicles,
        filters,
      ];
}
