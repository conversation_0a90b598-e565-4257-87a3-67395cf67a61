import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/listOfVehicles/domain/entities/vehicle_list_object.dart';

import '../../../../core/util/input_converter.dart';
import '../../../../generated/assets.dart';

class LargeVehicleWidget extends StatelessWidget {
  final Function() onTap;
  final Function() onLikeTap;
  final VehicleListObject vehicle;
  final bool hasMoreColors;

  const LargeVehicleWidget({
    super.key,
    required this.onTap,
    required this.onLikeTap,
    required this.vehicle,
    required this.hasMoreColors,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 12.h, bottom: 12.h),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                vehicle.isVerifiedByUber != null
                    ? vehicle.isVerifiedByUber == true
                        ? Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.r),
                              color:
                                  context?.confirmIconColor!.withOpacity(0.1),
                              border: Border.all(
                                  color: context.confirmIconColor!, width: 1.w),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.check_circle_rounded,
                                  color: Colors.green,
                                  size: 12.w,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  "uber_verified".tr,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w400,
                                    color: context.black,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(
                                horizontal: 12.w, vertical: 7.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.r),
                              border: Border.all(
                                  color: context.confirmIconColor!, width: 1.w),
                            ),
                            child: Text(
                              "new".tr,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w700,
                                color: context.black,
                              ),
                            ),
                          )
                    : Container(),
                InkWell(
                  onTap: () {
                    onLikeTap();
                  },
                  child: Icon(
                    vehicle.like ?? false
                        ? Icons.favorite
                        : Icons.favorite_border,
                    color: context.lightBlack,
                    size: 20.w,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Container(
              height: 155.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: Colors.grey[300],
              ),
              child: Image.network(
                vehicle.image ?? "",
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Image.asset(
                    Assets.thrivvePhotosImgNotAvailable,
                    fit: BoxFit.cover,
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
                },
              ),
            ),
            if (hasMoreColors) ...[
              SizedBox(height: 16.h),
              _buildColorOptions(vehicle.colors ?? []),
            ],
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    vehicle.name ?? "",
                    overflow: TextOverflow.fade,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.yellow,
                      size: 16.w,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      vehicle.rate?.toString() ?? "",
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w400,
                        color: context.lightBlack,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Divider(
              color: Colors.grey[300],
              height: 1.h,
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // _buildFeatureOptions(context, vehicle),
                CustomTextWidget(
                  title: vehicle.price ?? '',
                  size: 13,
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w600,
                  color: context.appPrimaryColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureOptions(BuildContext context, VehicleListObject vehicle) {
    return Row(
      children: [
        if (vehicle.carMotorPower != null) ...[
          Image.asset(
            Assets.thrivvePhotosMotorPower,
            color: context.lightBlack,
            height: 12.h,
            width: 12.w,
          ),
          SizedBox(width: 4.w),
          Text(
            vehicle.carMotorPower ?? "",
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w400,
              color: context.lightBlack,
            ),
          ),
          SizedBox(width: 8.w),
        ],
        if (vehicle.fuelType != null) ...[
          Image.asset(
            Assets.thrivvePhotosWatch,
            color: context.lightBlack,
            height: 12.h,
            width: 12.w,
          ),
          SizedBox(width: 4.w),
          Text(
            vehicle.fuelType ?? "",
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w400,
              color: context.lightBlack,
            ),
          ),
          SizedBox(width: 8.w),
        ],
        if (vehicle.gearType != null) ...[
          Image.asset(
            Assets.thrivvePhotosWatch,
            color: context.lightBlack,
            height: 12.h,
            width: 12.w,
          ),
          SizedBox(width: 4.w),
          Text(
            vehicle.gearType ?? "",
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w400,
              color: context.lightBlack,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildColorOptions(List<String?> colors) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: colors.map((color) {
        return Container(
          margin: EdgeInsets.only(right: 4.w),
          height: 20.h,
          width: 20.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: getColorFromName(Get.context!, color),
          ),
        );
      }).toList(),
    );
  }
}
