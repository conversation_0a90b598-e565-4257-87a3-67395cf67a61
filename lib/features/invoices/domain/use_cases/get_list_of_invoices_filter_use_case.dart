import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../dashboard/domain/repositories/dashboard_repository.dart';
import '../entities/invoice_filter.dart';

class GetListOfInvoicesFilterUseCase implements UseCase<List<InvoiceFilter?>?, NoParams> {
  final DashboardRepository? _dashboardRepository;

  GetListOfInvoicesFilterUseCase(this._dashboardRepository);

  @override
  Future<Either<Failure, List<InvoiceFilter?>?>?> call(NoParams params) async {
    return await _dashboardRepository?.getListOfFilter();
  }
}
