import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class InvoicesShimmerLoader extends StatelessWidget {
  const InvoicesShimmerLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Get.context!.borderAddBranch!,
      highlightColor: Get.context!.appBackgroundColor!,
      child: ListView.builder(
        itemCount: 9,
        itemBuilder: (context, index) {
          return ListTile(
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: context.appBackgroundColor,
                borderRadius: BorderRadius.circular(90),
              ),
            ),
            title: Container(
              width: 100,
              height: 20,
              color: context.appBackgroundColor,
            ),
            subtitle: Container(
              width: 100,
              height: 20,
              color: context.appBackgroundColor,
            ),
            trailing: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: context.appBackgroundColor,
                borderRadius: BorderRadius.circular(90),
              ),
            ),
          );
        },
      ),
    );
  }
}
