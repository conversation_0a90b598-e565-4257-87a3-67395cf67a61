import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; // Import flutter_screenutil
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/widget/button.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';

import '../../../../generated/assets.dart';

class NoInvoicesProductsFound extends StatelessWidget {
  final String image;
  final String? title;
  final String? subtitle;
  final String? buttonText;
  final bool? buttonVisibility;
  final Function? buttonOnPressed;
  final double imageWidth;
  final double imageHeight;

  const NoInvoicesProductsFound({
    super.key,
    this.image = Assets.thrivvePhotosNoProductFound,
    this.title,
    this.subtitle,
    this.buttonText,
    this.buttonVisibility = false,
    this.buttonOnPressed,
    this.imageWidth = 320,
    this.imageHeight = 242,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              image,
              width: imageWidth.w, // Use ScreenUtil to set width
              height: imageHeight.h, // Use ScreenUtil to set height
            ),
            SizedBox(height: 60.h),
            Text(
              title ?? "",
              style: TextStyle(
                color: context.black,
                fontSize: 20.sp, // Use ScreenUtil to set font size
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              // Use ScreenUtil to set horizontal padding
              child: Text(
                subtitle ?? "",
                style: TextStyle(
                  color: context.lightBlack,
                  fontSize: 12.sp, // Use ScreenUtil to set font size
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        Visibility(
          visible: buttonVisibility ?? false,
          child: Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
              child: CustomButton(
                  text: buttonText ?? "",
                  onPressed: () {
                    buttonOnPressed!();
                  }),
            ),
          ),
        ),
      ],
    );
  }
}
