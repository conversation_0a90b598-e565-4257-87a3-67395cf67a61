import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

import '../../../../core/util/const.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/widget/invoice_web_page_viewer_bottom_sheet.dart';
import '../../../../generated/assets.dart';
import '../../../myProductDetails/presentation/widgets/pdf_viewer_bottom_sheet.dart';
import '../../domain/entities/invoice.dart';

class InvoiceWidget extends StatelessWidget {
  final Invoice? invoice;
  final String currency;

  const InvoiceWidget({
    super.key,
    required this.invoice,
    this.currency = "SAR",
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 52.h,
          child: ListTile(
            horizontalTitleGap: 10.w,
            leading: Container(
              width: 46.w,
              height: 46.h,
              decoration: BoxDecoration(
                color: context.appBackgroundColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: CustomTextWidget(
                  title: invoice?.month ?? "",
                  color: context.black,
                  size: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            title: CustomTextWidget(
              title: "${invoice?.amount} $currency",
              color: context.black,
              fontWeight: FontWeight.w700,
              size: 12,
            ),
            subtitle: CustomTextWidget(
              title: buildStatus() ?? "",
              color: invoice?.status == withdrawTransactionsPendingStatus
                  ? Colors.orange
                  : Colors.green,
              fontWeight: FontWeight.w400,
              size: 12,
            ),
            trailing: invoice?.url != null
                ? IconButton(
                    icon: Image.asset(
                      Assets.thrivvePhotosDownload2,
                      width: 24.w,
                      height: 24.h,
                    ),
                    onPressed: () async {
                      openTheUriInWebPage(
                          downloadUrl: invoice?.url,
                          url: invoice?.url,
                          context: context);
                    },
                  )
                : const SizedBox(),
          ),
        ),
        SizedBox(height: 18.h)
      ],
    );
  }

  String? buildStatus() {
    return invoice?.status == withdrawTransactionsPendingStatus
        ? invoice?.status
        : "${invoice?.status} ${invoice?.date}";
  }

  showDocumentInPDF(BuildContext context, String? docPdfUrl, String? name) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return PdfViewerBottomSheet(
          isPDF: checkPDFFile(docPdfUrl ?? ""),
          url: docPdfUrl,
          title: "invoice".tr,
          showShareBtn: true,
        );
      },
    );
  }

  void openTheUriInWebPage(
      {String? url, required BuildContext context, String? downloadUrl}) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return Container(
          margin: const EdgeInsets.only(top: 40),
          child: InvoiceWebPageViewerBottomSheet(
            url: url,
            downloadUrl: downloadUrl,
          ),
        );
      },
    );
  }
}
