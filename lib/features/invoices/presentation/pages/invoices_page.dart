import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/widget/app_error_message_widget.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/widget/app_bar_title_widget.dart';
import '../../../listOfVehicles/presentation/widgets/list_of_filter_loading_widget.dart';
import '../../../mainHome/presentation/manager/main_home_bloc.dart';
import '../manager/invoices_bloc.dart';
import '../widgets/invoice_filter_list_widget.dart';
import '../widgets/invoice_v2_widget.dart';
import '../widgets/invoices_shimmer_loader.dart';
import '../widgets/no_invoices_products_found.dart';

class InvoicesPage extends StatelessWidget {
  const InvoicesPage({super.key});
  Widget _buildIOInvoicesBody(InvoicesState state) {
    switch (state.getListOfFilterStatuses) {
      case AppStatus.loading:
        return const ListOfFilterLoadingWidget();
      case AppStatus.failure:
        return const SizedBox();
      case AppStatus.success:
        return Visibility(
          visible: state.listOfInvoices != null &&
              (state.listOfInvoices ?? []).isNotEmpty,
          child: Padding(
            padding: EdgeInsets.only(
              left: 16.0.w,
              right: 16.0.w,
              top: 12.0.h,
              bottom: 36.0.h,
            ),
            child: InvoiceFilterListWidget(
              filter: state.filters ?? [],
              selectFilter: state.selectFilter,
              onFilterSelected: (filter) {
                Get.context!.read<InvoicesBloc>().add(
                      SelectFilterEvent(
                        filter: filter,
                      ),
                    );
              },
            ),
          ),
        );
      default:
        return const SizedBox();
    }
  }

  @override
  Widget build(BuildContext context) {
    String currency = context.watch<MainHomeBloc>().state.currency ?? "SAR";
    final personalInfo = context.watch<MainHomeBloc>().state.personalInfo;
    final canWithdraw =
        context.watch<MainHomeBloc>().state.isInstantPaymentIsEnable;
    return BlocProvider(
      create: (context) => getIt<InvoicesBloc>()
        ..add(const GetListOfInvoicesEvent())
        ..add(const GetListOfFilterEvent()),
      child: BlocBuilder<InvoicesBloc, InvoicesState>(
        builder: (context, state) {
          return Scaffold(
            appBar: state.listOfInvoices?.isNotEmpty == true
                ? AppBar(
                    title: state.listOfInvoices?.isNotEmpty == true
                        ? AppBarTitleWidget(
                            title: 'invoices'.tr,
                          )
                        : null,
                    elevation: 0.0,
                    surfaceTintColor: Colors.transparent,
                    centerTitle: false,
                  )
                : null,
            body: BlocBuilder<InvoicesBloc, InvoicesState>(
                builder: (context, state) {
              return Stack(
                children: [
                  if ((state.listOfInvoices?.isEmpty == true) &&
                      (state.getListOfFilterStatuses == AppStatus.success))
                    Container(
                      height: Get.height,
                      width: Get.width,
                      color: (Get.find<ThemeController>().isDarkMode() == true)
                          ? Colors.black
                          : null,
                      child: (Get.find<ThemeController>().isDarkMode() == true)
                          ? null
                          : Image.asset(
                              "assets/thrivvePhotos/icons/background.jpg",
                              fit: BoxFit.fill,
                              alignment: Alignment.topCenter,
                            ),
                    ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: MediaQuery.paddingOf(context).top),
                      _buildIOInvoicesBody(state),
                      Expanded(
                        child: BlocBuilder<InvoicesBloc, InvoicesState>(
                          builder: (context, state) {
                            switch (state.getListOfInvoicesStatuses) {
                              case AppStatus.loading:
                                return const InvoicesShimmerLoader();
                              case AppStatus.success:
                                return RefreshIndicator(
                                    onRefresh: () async {
                                      context
                                          .read<InvoicesBloc>()
                                          .add(const GetListOfInvoicesEvent());
                                    },
                                    child: state.listOfInvoices?.isEmpty ?? true
                                        ? NoInvoicesProductsFound(
                                            title: 'all_clear_here'.tr,
                                            subtitle:
                                                'all_clear_here_message'.tr,
                                            image:
                                                "assets/thrivvePhotos/icons/no_invoices.png",
                                            buttonOnPressed: () {
                                              Get.toNamed(
                                                  AppRoutes.productsPage,
                                                  arguments: {
                                                    "personalInfo":
                                                        personalInfo,
                                                    "canWithdraw": canWithdraw,
                                                  });
                                            },
                                            buttonText:
                                                'browse_our_products'.tr,
                                            buttonVisibility: true,
                                          )
                                        : ListView.builder(
                                            itemCount:
                                                state.listOfInvoices?.length,
                                            itemBuilder: (context, index) {
                                              var invoice =
                                                  state.listOfInvoices?[index];
                                              return Column(
                                                children: [
                                                  InvoiceV2Widget(
                                                    key: ValueKey(
                                                        '${invoice?.status ?? ''}-${invoice?.id ?? 0}'),
                                                    invoice: invoice,
                                                    currency: currency,
                                                  ),
                                                  SizedBox(height: 18.h)
                                                ],
                                              );
                                            },
                                          ));
                              case AppStatus.failure:
                                return AppErrorMessageWidget(
                                    error: state.errorMessage ?? "");
                              default:
                                return const SizedBox();
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              );
            }),
          );
        },
      ),
    );
  }
}
