import '../../domain/entities/link.dart';

class LinkModel extends Link {
  @override
  String urlType;
  @override
  String url;

  LinkModel({
    required this.urlType,
    required this.url,
  }) : super(
          urlType: urlType,
          url: url,
        );

  factory LinkModel.fromJson(Map<String, dynamic> json) {
    return LinkModel(
      urlType: json['url_type'],
      url: json['url'],
    );
  }
}
