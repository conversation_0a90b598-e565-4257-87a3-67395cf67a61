import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/onboarding/domain/entities/country.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/phone_number_input.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/terms_and_conditions_widget.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/top_bar_widget.dart';

import '../../../../core/widget/button.dart';
import '../../../onboarding/presentation/widgets/circle_avatar_widget.dart';

class SignUpInPage1Widget extends StatefulWidget {
  final Function(String) onPageChange;
  final String? image;
  final String? title;
  final String? desc;
  final String? buttonText;
  final bool? getCountriesLoading;
  final bool isLogin;
  final List<Country>? listOfCounties;

  const SignUpInPage1Widget({
    super.key,
    required this.onPageChange,
    required this.listOfCounties,
    this.image,
    this.title,
    this.desc,
    this.buttonText,
    this.getCountriesLoading,
    required this.isLogin,
  });

  @override
  State<SignUpInPage1Widget> createState() => _SignUpInPage1WidgetState();
}

class _SignUpInPage1WidgetState extends State<SignUpInPage1Widget> {
  bool _readyToSubmit = false;
  String _phoneNumber = "";

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const TopBar(),
        SizedBox(height: 16.h),
        CircleAvatarWidget(
          image: widget.image,
        ),
        SizedBox(height: 28.h),
        Text(
          widget.title ?? "",
          // widget.isLogin ? "login".tr : "signup".tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: context.black,
            fontWeight: FontWeight.w600,
            fontSize: 15.sp,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          // widget.isLogin ? "login_desc".tr : "signup_desc".tr,
          widget.desc ?? "",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: context.lightBlack,
            fontWeight: FontWeight.w400,
            fontSize: 11.sp,
          ),
        ),
        SizedBox(height: 32.h),
        PhoneNumberInput(
          getCountriesLoading: widget.getCountriesLoading,
          listOfCounties: widget.listOfCounties,
          readyToSubmit: (readyToSubmit, value) {
            setState(() {
              _readyToSubmit = readyToSubmit;
              _phoneNumber = value ?? "";
            });
          },
        ),
        SizedBox(height: 20.h),
        if (_readyToSubmit) ...[
          TermsAndConditionsWidget(
            isLogin: widget.isLogin,
          ),
          SizedBox(height: 20.h),
        ],
        Button(
          // text: widget.isLogin ? "login_btn".tr : "signup_btn".tr,
          text: widget.buttonText ?? "",
          onTab: () {
            widget.onPageChange(_phoneNumber);
          },
          enable: _readyToSubmit,
          height: 36,
        ),
        SizedBox(height: 10.h),
      ],
    );
  }
}
