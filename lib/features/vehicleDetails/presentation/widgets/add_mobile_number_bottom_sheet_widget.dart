import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/features/vehicleDetails/presentation/widgets/sign_up_in_page_1_widget.dart';

import '../../../../core/util/helper.dart';
import '../../../../generated/assets.dart';
import '../manager/vehicle_details_bloc.dart';

class AddMobileBottomSheetWidget extends StatefulWidget {
  const AddMobileBottomSheetWidget({super.key});

  @override
  State<AddMobileBottomSheetWidget> createState() =>
      _AddMobileBottomSheetWidgetState();
}

class _AddMobileBottomSheetWidgetState
    extends State<AddMobileBottomSheetWidget> {
  final controller = PageController(initialPage: 0);

  @override
  void initState() {
    super.initState();
    context.read<VehicleDetailsBloc>().add(GetCountriesEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VehicleDetailsBloc, VehicleDetailsState>(
      listenWhen: (previous, current) =>
          previous.sendMobileNumberStatus != current.sendMobileNumberStatus,
      listener: (context, state) {
        if (state.sendMobileNumberStatus == AppStatus.success) {
          Get.back();
          applyAsALeadSuccessfullyBottomSheet(
              context: context, message: state.message);
        } else if (state.sendMobileNumberStatus == AppStatus.failure) {
          errorSnackBar(
            context: context,
            message: state.errorMessage,
          );
        }
      },
      child: BlocBuilder<VehicleDetailsBloc, VehicleDetailsState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.viewInsetsOf(context)
                    .bottom, // Adjust for the keyboard
              ),
              child: Container(
                padding: EdgeInsets.all(16.h),
                decoration: BoxDecoration(
                  color: context.bottomsheetColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.0.r),
                    topRight: Radius.circular(20.0.r),
                  ),
                ),
                child: SignUpInPage1Widget(
                  onPageChange: (phoneNumber) {
                    context
                        .read<VehicleDetailsBloc>()
                        .add(SendMobileNumberEvent(mobileNum: phoneNumber));
                  },
                  image: Assets.thrivvePhotosMobile,
                  title: "phone_number_is_required".tr,
                  desc: 'phone_number_is_required_msj'.tr,
                  buttonText: "submit".tr,
                  listOfCounties: state.listOfCountries,
                  isLogin: true,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
