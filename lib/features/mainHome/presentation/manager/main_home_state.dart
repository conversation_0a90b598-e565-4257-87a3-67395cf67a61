part of 'main_home_bloc.dart';

class MainHomeState extends Equatable {
  const MainHomeState({
    this.infoSupplier,
    this.isPinEnabledForThisDevice,
    this.status = AppStatus.initial,
    this.getBalanceStatus = AppStatus.initial,
    this.changeLanguageStatus = AppStatus.initial,
    this.driveToOwnStatus = AppStatus.initial,
    this.getSupportUrlStatus = AppStatus.initial,
    this.uploadImageStatus = AppStatus.initial,
    this.logoutStatus = AppStatus.initial,
    this.updateBiometricSettingsStatus = AppStatus.initial,
    this.getPersonalInfoStatus = AppStatus.initial,
    this.supportUrl,
    this.errorMessage,
    this.isLogin,
    this.tabIndex = 0,
    this.withdrawBalance,
    this.paymentRequest,
    this.isBalanceVisible,
    this.language,
    this.currency,
    this.deleteUserStatus = AppStatus.initial,
    this.isNotificationNeedActions,
    this.unseenNotificationNum,
    this.personalInfo,
    this.driveToOwn,
    this.appVersion,
    this.isInstantPaymentIsEnable,
    this.isCardsIsEnable,
    this.isEarn150SarIsEnable,
    this.isPinSetupSuccessfully,
    this.isTopUpIsEnable,
    this.earningText,
    this.referralStatus,
    this.profileImage,
    this.tabMoreIndex = -1,
    this.isDeviceSupportBioMetric = false,
    this.isFaceId = false,
    this.isBioMetricEnabledForThisDevice = false,
    this.getLoginStatus = AppStatus.initial,
  });

  final String? earningText;
  final ReferralStatus? referralStatus;
  final ThrivveInfoEntity? infoSupplier;
  final bool? isInstantPaymentIsEnable;
  final bool? isCardsIsEnable;
  final bool? isEarn150SarIsEnable;
  final bool? isTopUpIsEnable;
  final bool? isDeviceSupportBioMetric;
  final bool? isFaceId;
  final bool? isLogin;
  final bool? isBioMetricEnabledForThisDevice;
  final bool? isPinEnabledForThisDevice;
  final bool? isNotificationNeedActions;
  final int? unseenNotificationNum;
  final String? appVersion;
  final int tabIndex;
  final int tabMoreIndex;
  final AppStatus deleteUserStatus;
  final AppStatus driveToOwnStatus;
  final AppStatus changeLanguageStatus;
  final AppStatus uploadImageStatus;
  final AppStatus logoutStatus;
  final AppStatus updateBiometricSettingsStatus;
  final AppStatus status;
  final AppStatus getPersonalInfoStatus;
  final String? errorMessage;
  final String? currency;
  final String? language;
  final WithdrawBalance? withdrawBalance;
  final AppStatus getBalanceStatus;
  final Transaction? paymentRequest;
  final bool? isBalanceVisible;
  final bool? isPinSetupSuccessfully;
  final String? supportUrl;
  final String? profileImage;
  final AppStatus getSupportUrlStatus;
  final PersonalInfoEntity? personalInfo;
  final DriveToOwn? driveToOwn;
  final AppStatus getLoginStatus;

  MainHomeState copyWith({
    ThrivveInfoEntity? Function()? infoThrivve,
    AppStatus Function()? status,
    AppStatus Function()? driveToOwnStatus,
    AppStatus Function()? deleteUserStatus,
    AppStatus Function()? logoutStatus,
    AppStatus Function()? uploadImageStatus,
    AppStatus Function()? updateBiometricSettingsStatus,
    AppStatus Function()? getPersonalInfoStatus,
    String? Function()? earningText,
    String? Function()? profileImage,
    ReferralStatus? Function()? referralStatus,
    DriveToOwn? Function()? driveToOwn,
    AppStatus Function()? changeLanguageStatus,
    Transaction? Function()? paymentRequest,
    String? Function()? errorMessage,
    String? Function()? currency,
    String? Function()? language,
    int Function()? tabIndex,
    int Function()? tabMoreIndex,
    WithdrawBalance? Function()? withdrawBalance,
    AppStatus Function()? getBalanceStatus,
    bool? Function()? isPinEnabledForThisDevice,
    bool? Function()? isPinSetupSuccessfully,
    bool? Function()? isInstantPaymentIsEnable,
    bool? Function()? isCardsIsEnable,
    bool? Function()? isDeviceSupportBioMetric,
    bool? Function()? isBioMetricEnabledForThisDevice,
    bool? Function()? isFaceId,
    bool? Function()? isEarn150SarIsEnable,
    bool? Function()? isTopUpIsEnable,
    bool? Function()? isBalanceVisible,
    bool? Function()? isNotificationNeedActions,
    bool? Function()? isLogin,
    int? Function()? unseenNotificationNum,
    PersonalInfoEntity? Function()? personalInfo,
    String? Function()? appVersion,
    AppStatus Function()? getSupportUrlStatus,
    String? Function()? supportUrl,
    AppStatus? getLoginStatus,
    bool? isLoggedIn,
  }) {
    return MainHomeState(
      isLogin: isLogin != null ? isLogin() : this.isLogin,
      isPinEnabledForThisDevice: isPinEnabledForThisDevice != null
          ? isPinEnabledForThisDevice()
          : this.isPinEnabledForThisDevice,
      infoSupplier: infoThrivve != null ? infoThrivve() : this.infoSupplier,
      isFaceId: isFaceId != null ? isFaceId() : this.isFaceId,
      isDeviceSupportBioMetric: isDeviceSupportBioMetric != null
          ? isDeviceSupportBioMetric()
          : this.isDeviceSupportBioMetric,
      isBioMetricEnabledForThisDevice: isBioMetricEnabledForThisDevice != null
          ? isBioMetricEnabledForThisDevice()
          : this.isBioMetricEnabledForThisDevice,
      supportUrl: supportUrl != null ? supportUrl() : this.supportUrl,
      profileImage: profileImage != null ? profileImage() : this.profileImage,
      uploadImageStatus: uploadImageStatus != null
          ? uploadImageStatus()
          : this.uploadImageStatus,
      updateBiometricSettingsStatus: updateBiometricSettingsStatus != null
          ? updateBiometricSettingsStatus()
          : this.updateBiometricSettingsStatus,
      getSupportUrlStatus: getSupportUrlStatus != null
          ? getSupportUrlStatus()
          : this.getSupportUrlStatus,
      getPersonalInfoStatus: getPersonalInfoStatus != null
          ? getPersonalInfoStatus()
          : this.getPersonalInfoStatus,
      referralStatus:
          referralStatus != null ? referralStatus() : this.referralStatus,
      earningText: earningText != null ? earningText() : this.earningText,
      isInstantPaymentIsEnable: isInstantPaymentIsEnable != null
          ? isInstantPaymentIsEnable()
          : this.isInstantPaymentIsEnable,
      isCardsIsEnable:
          isCardsIsEnable != null ? isCardsIsEnable() : this.isCardsIsEnable,
      isEarn150SarIsEnable: isEarn150SarIsEnable != null
          ? isEarn150SarIsEnable()
          : this.isEarn150SarIsEnable,
      isTopUpIsEnable:
          isTopUpIsEnable != null ? isTopUpIsEnable() : this.isTopUpIsEnable,
      tabIndex: tabIndex != null ? tabIndex() : this.tabIndex,
      driveToOwnStatus:
          driveToOwnStatus != null ? driveToOwnStatus() : this.driveToOwnStatus,
      driveToOwn: driveToOwn != null ? driveToOwn() : this.driveToOwn,
      appVersion: appVersion != null ? appVersion() : this.appVersion,
      isNotificationNeedActions: isNotificationNeedActions != null
          ? isNotificationNeedActions()
          : this.isNotificationNeedActions,
      unseenNotificationNum: unseenNotificationNum != null
          ? unseenNotificationNum()
          : this.unseenNotificationNum,
      deleteUserStatus:
          deleteUserStatus != null ? deleteUserStatus() : this.deleteUserStatus,
      changeLanguageStatus: changeLanguageStatus != null
          ? changeLanguageStatus()
          : this.changeLanguageStatus,
      isBalanceVisible:
          isBalanceVisible != null ? isBalanceVisible() : this.isBalanceVisible,
      language: language != null ? language() : this.language,
      currency: currency != null ? currency() : this.currency,
      paymentRequest:
          paymentRequest != null ? paymentRequest() : this.paymentRequest,
      getBalanceStatus:
          getBalanceStatus != null ? getBalanceStatus() : this.getBalanceStatus,
      status: status != null ? status() : this.status,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
      withdrawBalance:
          withdrawBalance != null ? withdrawBalance() : this.withdrawBalance,
      personalInfo: personalInfo != null ? personalInfo() : this.personalInfo,
      logoutStatus: logoutStatus != null ? logoutStatus() : this.logoutStatus,
      isPinSetupSuccessfully: isPinSetupSuccessfully != null
          ? isPinSetupSuccessfully()
          : this.isPinSetupSuccessfully,
      tabMoreIndex: tabMoreIndex != null ? tabMoreIndex() : this.tabMoreIndex,
      getLoginStatus: getLoginStatus ?? this.getLoginStatus,
    );
  }

  @override
  List<Object?> get props => [
        profileImage,
        uploadImageStatus,
        updateBiometricSettingsStatus,
        logoutStatus,
        driveToOwn,
        referralStatus,
        earningText,
        supportUrl,
        getSupportUrlStatus,
        getPersonalInfoStatus,
        isPinSetupSuccessfully,
        isInstantPaymentIsEnable,
        isCardsIsEnable,
        isEarn150SarIsEnable,
        isTopUpIsEnable,
        changeLanguageStatus,
        isNotificationNeedActions,
        personalInfo,
        unseenNotificationNum,
        appVersion,
        isBalanceVisible,
        deleteUserStatus,
        language,
        tabIndex,
        currency,
        driveToOwnStatus,
        paymentRequest,
        status,
        getBalanceStatus,
        errorMessage,
        withdrawBalance,
        isFaceId,
        isDeviceSupportBioMetric,
        isBioMetricEnabledForThisDevice,
        tabMoreIndex,
        infoSupplier,
        isPinEnabledForThisDevice,
        isLogin,
      ];
}
