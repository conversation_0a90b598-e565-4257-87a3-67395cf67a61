import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/controller/app_controller.dart';
import 'package:thrivve/core/controller/language_controller.dart';
import 'package:thrivve/core/util/const.dart';
import 'package:thrivve/features/dashboard/domain/entities/thrivve_info_entity.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_info_thrivve_use_case.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';
import 'package:thrivve/features/personalInfo/domain/use_cases/save_user_info_use_case.dart';
import 'package:thrivve/features/settings/domain/entities/biometric_settings_input.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/localDataSource/user_secure_data_source.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/app_status.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../dashboard/domain/entities/transaction.dart';
import '../../../dashboard/domain/entities/withdraw_balance.dart';
import '../../../dashboard/domain/use_cases/change_language_use_case.dart';
import '../../../dashboard/domain/use_cases/delete_user_account_use_case.dart';
import '../../../dashboard/domain/use_cases/get_personal_info_use_case.dart';
import '../../../dashboard/domain/use_cases/get_support_url_use_case.dart';
import '../../../dashboard/domain/use_cases/get_withdraw_balance_use_case.dart';
import '../../../myProductDetails/domain/entities/drive_to_own.dart';
import '../../../myProductDetails/domain/use_cases/get_drive_to_own_data_use_case.dart';
import '../../../notifications/domain/use_cases/get_unseen_notification_use_case.dart';
import '../../../onboarding/domain/entities/flag.dart';
import '../../../onboarding/domain/entities/flag_keys.dart';
import '../../../onboarding/domain/use_cases/get_flags_use_case.dart';
import '../../../referral/domain/entities/referral_status.dart';
import '../../../referral/domain/use_cases/get_referral_status_use_case.dart';
import '../../../settings/domain/use_cases/update_biometric_settings_use_case.dart';

part 'main_home_event.dart';
part 'main_home_state.dart';

class MainHomeBloc extends Bloc<MainHomeEvent, MainHomeState> {
  final GetWithdrawBalanceUseCase getWithdrawBalanceUseCase;
  final ChangeLanguageUseCase changeLanguageUseCase;
  final DeleteUserAccountUseCase deleteUserAccountUseCase;
  final GetUnSeenNotificationUseCase getUnSeenNotificationUseCase;
  final GetPersonalInfoUseCase getPersonalInfoUseCase;
  final GetDriveToOwnDataUseCase getDriveToOwnDataUseCase;
  final UserSecureDataSource userSecureDataSource;
  final GetFlagsUseCase getFlagsUseCase;
  final GetSupportUrlUseCase getSupportUrlUseCase;
  final GetReferralStatusUseCase getReferralStatusUseCase;
  final SaveUserInfoUseCase saveUserInfoUseCase;
  final UpdateBiometricSettingsUseCase updateBiometricSettingsUseCase;
  final GetInfoThrivveUseCase infoSupplierUseCase;
  final LocalAuthentication auth = LocalAuthentication();
  bool? isPinSetupSuccessfullyValue =
      Get.parameters[isPinSetupSuccessfully] == 'true' ? true : false;

  MainHomeBloc({
    required this.getFlagsUseCase,
    required this.deleteUserAccountUseCase,
    required this.getPersonalInfoUseCase,
    required this.getUnSeenNotificationUseCase,
    required this.getWithdrawBalanceUseCase,
    required this.changeLanguageUseCase,
    required this.getDriveToOwnDataUseCase,
    required this.userSecureDataSource,
    required this.getSupportUrlUseCase,
    required this.getReferralStatusUseCase,
    required this.saveUserInfoUseCase,
    required this.updateBiometricSettingsUseCase,
    required this.infoSupplierUseCase,
  }) : super(const MainHomeState()) {
    on<ChangeTabIndexEvent>(_onChangeTabIndexEvent);
    on<ChangeTabMoreIndexEvent>(_onChangeTabMoreIndexEvent);
    on<GetBalanceEvent>(_onGetBalanceEvent);
    on<SetBalanceVisibilityEvent>(_onSetBalanceVisibilityEventCalled);
    on<GetMainHomeStarterDataEvent>(_onGetMainHomeStarterDataEventCalled);
    on<ChangeLanguageEvent>(_onChangeLanguageEventCalled);
    on<DeleteUserAccountEvent>(_onDeleteUserAccountEventCalled);
    on<GetNotificationDataEvent>(_onGetNotificationDataEventCalled);
    on<GetPersonalInfoEvent>(_onGetPersonalInfoEventCalled);
    on<GetFlagsEvent>(_getFlagsEventOnClick);
    on<GetSupportUrlEvent>(_onGetSupportUrlCalled);
    on<FetchReferralStatusEvent>(_onFetchReferralStatus);
    on<UploadImageEvent>(_onUploadImageEventCalled);
    on<SaveLeadIdEvent>(_saveLeadIdEventOnClick);
    on<CheckIsDeviceSupportAndTypeBioMetricEvent>(
        _checkIsDeviceSupportAndTypeBioMetricEvent);
    on<UpdateBiometricSettingsEvent>(_onUpdateBiometricSettingsEvent);
    on<GetThrivveInfoEvent>(_onGetInfoSupplierInfoEvent);
    on<InitializeMainHomeEvent>(_onInitializeMainHome);
    on<CheckLogin>(_checkLogin);
  }
  bool? isUserLoggedIn;

  Future<void> _checkLogin(
    CheckLogin event,
    Emitter<MainHomeState> emit,
  ) async {
    final isUserLoggedIn = await getIt<UserSecureDataSource>().isLogin();
    emit(state.copyWith(
      isLogin: () => isUserLoggedIn,
    ));
  }

  Future<void> _onInitializeMainHome(
    InitializeMainHomeEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    isUserLoggedIn = await getIt<UserSecureDataSource>().isLogin();

    add(CheckLogin());
    add(const GetMainHomeStarterDataEvent());
    add(const GetFlagsEvent());
    if (isUserLoggedIn == true) {
      add(CheckIsDeviceSupportAndTypeBioMetricEvent());
      add(const GetPersonalInfoEvent());
      add(const GetNotificationDataEvent());
      add(const GetBalanceEvent());
      add(const GetThrivveInfoEvent());
    }
  }

  Future<void> _onGetInfoSupplierInfoEvent(
    GetThrivveInfoEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    final referralStatus = await infoSupplierUseCase(NoParams());
    referralStatus.fold(
      (failure) {
        emit(state.copyWith(
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (infoThrivve) {
        emit(state.copyWith(
          infoThrivve: () => infoThrivve,
        ));
      },
    );
  }

  Future<void> _checkIsDeviceSupportAndTypeBioMetricEvent(
    CheckIsDeviceSupportAndTypeBioMetricEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    bool isSupported = await auth.isDeviceSupported();
    if (isSupported) {
      final availableOptions = await auth.getAvailableBiometrics();
      final isFaceId = availableOptions.contains(BiometricType.face);
      final isBioMetricEnabledForThisDevice =
          await userSecureDataSource.isBioMetricEnabled();

      emit(
        state.copyWith(
          isDeviceSupportBioMetric: () => isSupported,
          isFaceId: () => isFaceId,
          isBioMetricEnabledForThisDevice: () =>
              isBioMetricEnabledForThisDevice,
        ),
      );
    }
    final isPinEnabledForThisDevice = await userSecureDataSource.isPinEnabled();
    log(isPinEnabledForThisDevice.toString());
    emit(
      state.copyWith(
        isPinEnabledForThisDevice: () => isPinEnabledForThisDevice,
      ),
    );
  }

  Future<void> _saveLeadIdEventOnClick(
    SaveLeadIdEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    if (event.leadId != null) {
      await userSecureDataSource.setLeadId(event.leadId);
      getIt<IAnalyticsLogger>()
          .logProperty(AnalyticsActions.leadId, event.leadId!);
    }
  }

  Future<void> _onUploadImageEventCalled(
    UploadImageEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    emit(state.copyWith(uploadImageStatus: () => AppStatus.loading));

    await saveUserInfoUseCase(SaveUserInfoParams(
      imageUrl: event.filePath,
    ));
    emit(state.copyWith(
      profileImage: () => event.filePath,
      uploadImageStatus: () => AppStatus.success,
    ));
  }

  Future<void> _onFetchReferralStatus(
      FetchReferralStatusEvent event, Emitter<MainHomeState> emit) async {
    if (event.isEarn150SarIsEnable == false) {
      return;
    }

    final referralStatus = await getReferralStatusUseCase();
    referralStatus.fold(
      (failure) {
        emit(state.copyWith(
          referralStatus: () => null,
        ));
      },
      (referralStatus) {
        emit(state.copyWith(
          referralStatus: () => referralStatus,
          earningText: () =>
              "${"earn".tr} ${referralStatus?.rewardValue} ${referralStatus?.rewardCurrency} ",
        ));
      },
    );
  }

  Future<void> _onGetSupportUrlCalled(
    GetSupportUrlEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    emit(state.copyWith(getSupportUrlStatus: () => AppStatus.loading));

    if (state.supportUrl != null) {
      emit(state.copyWith(getSupportUrlStatus: () => AppStatus.success));
      return;
    }

    final getSupportUrlUseCaseData = await getSupportUrlUseCase(NoParams());
    getSupportUrlUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                  getSupportUrlStatus: () => AppStatus.failure,
                  errorMessage: () => mapFailureToMessage(failure)),
            ), (data) {
      emit(state.copyWith(
        getSupportUrlStatus: () => AppStatus.success,
        supportUrl: () => data,
      ));
    });
  }

  Future<void> _getFlagsEventOnClick(
    GetFlagsEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    final getFlagsUseCaseData = await getFlagsUseCase(NoParams());
    getFlagsUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) {
      updateStateWithFlags(data, emit);
    });
  }

  void updateStateWithFlags(List<Flag?>? data, emit) {
    final enabledFlags = findAllEnabledFlags(data);
    var isInstantPaymentIsEnable =
        getFlagStatus(enabledFlags, FlagKeys.instant_payment);
    var isCardsIsEnable = getFlagStatus(enabledFlags, FlagKeys.cards);
    var isTopUpIsEnable = getFlagStatus(enabledFlags, FlagKeys.top_up);
    var isEarn150SarIsEnable =
        getFlagStatus(enabledFlags, FlagKeys.earn_150_sar);
    emit(state.copyWith(
      isInstantPaymentIsEnable: () => isInstantPaymentIsEnable,
      isCardsIsEnable: () => isCardsIsEnable,
      isTopUpIsEnable: () => isTopUpIsEnable,
      isEarn150SarIsEnable: () => isEarn150SarIsEnable,
    ));
    if (isUserLoggedIn == true) {
      add(FetchReferralStatusEvent(isEarn150SarIsEnable: isEarn150SarIsEnable));
    }
  }

  bool getFlagStatus(List<Flag?> enabledFlags, FlagKeys flagKey) {
    return enabledFlags.any(
      (flag) => flag?.flagKey != null
          ? stringToEnum(flag?.flagKey) == flagKey
          : false,
    );
  }

  Future<void> _onGetPersonalInfoEventCalled(
    GetPersonalInfoEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    emit(state.copyWith(getPersonalInfoStatus: () => AppStatus.loading));

    // get user info
    final getPersonalInfoUserCaseData =
        await getPersonalInfoUseCase(NoParams());
    await getPersonalInfoUserCaseData?.fold(
        (failure) async => emit(
              state.copyWith(
                getPersonalInfoStatus: () => AppStatus.failure,
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) async {
      await getIt<UserSecureDataSource>()
          .setPinEnabled(data?.securitySettings?.hasPinCode ?? false);
      await getIt<UserSecureDataSource>().setBioMetricEnabled(
          data?.securitySettings?.isBiometricEnabled ?? false);

      emit(
        state.copyWith(
          getPersonalInfoStatus: () => AppStatus.success,
          personalInfo: () => data,
          profileImage: () => data?.image,
        ),
      );
    });
  }

  Future<void> _onGetNotificationDataEventCalled(
    GetNotificationDataEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    // get notification
    final getUnSeenNotificationUseCaseData =
        await getUnSeenNotificationUseCase(NoParams());
    getUnSeenNotificationUseCaseData?.fold((failure) => {}, (data) {
      emit(state.copyWith(
        unseenNotificationNum: () => data?.numberOfUnseen,
        isNotificationNeedActions: () => data?.isNeedAction,
      ));
    });
  }

  Future<void> _onDeleteUserAccountEventCalled(
    DeleteUserAccountEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    emit(state.copyWith(deleteUserStatus: () => AppStatus.loading));

    final deleteUserAccountUseCaseData =
        await deleteUserAccountUseCase(NoParams());
    deleteUserAccountUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                  deleteUserStatus: () => AppStatus.failure,
                  errorMessage: () => mapFailureToMessage(failure)),
            ), (data) async {
      emit(state.copyWith(
        deleteUserStatus: () => AppStatus.success,
      ));
      getIt<IAnalyticsLogger>()
          .logEvent(AnalyticsActions.submitDeleteAccountRequest);
      Get.find<AppController>().logOut();
    });
  }

  Future<void> _onChangeLanguageEventCalled(
    ChangeLanguageEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    print('state.language ${state.language}');
    if (state.language == event.language) {
      return;
    }

    emit(state.copyWith(changeLanguageStatus: () => AppStatus.loading));

    final changeLanguageUseCaseData =
        await changeLanguageUseCase(ChangeLangParams(language: event.language));
    await changeLanguageUseCaseData?.fold(
        (failure) async => emit(
              state.copyWith(
                  changeLanguageStatus: () => AppStatus.failure,
                  errorMessage: () => mapFailureToMessage(failure)),
            ), (data) async {
      userSecureDataSource.saveTokens(
          data?.accessToken ?? '', data?.refreshToken ?? '');
      await changeLanguage(event);
      emit(state.copyWith(
        changeLanguageStatus: () => AppStatus.success,
        language: () => event.language,
      ));
    });
  }

  Future<void> changeLanguage(ChangeLanguageEvent event) async {
    Get.find<LanguageController>().changeLanguage(event.language ?? "ar");
  }

  Future<void> _onGetMainHomeStarterDataEventCalled(
    GetMainHomeStarterDataEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    print('isPinSetupSuccessfullyValue $isPinSetupSuccessfullyValue ');
    var versionPlatform = await PackageInfo.fromPlatform();
    var appVersion = versionPlatform.version;

    var isBalanceVisible =
        await userSecureDataSource.getBalanceVisible() ?? true;
    var language = await userSecureDataSource.getLanguage();
    var thrivveUser = await userSecureDataSource.getUserData();

    await saveUserToLocalStorage(
      thrivveUser,
      userSecureDataSource,
      saveTokens: false,
    );
    emit(state.copyWith(
      isBalanceVisible: () => isBalanceVisible,
      language: () => language,
      currency: () => thrivveUser?.currency ?? 'sar'.tr,
      appVersion: () => appVersion,
      isPinSetupSuccessfully: () => isPinSetupSuccessfullyValue,
    ));
  }

  Future<void> _onSetBalanceVisibilityEventCalled(
    SetBalanceVisibilityEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    await userSecureDataSource.setBalanceVisible(event.visible);
    emit(state.copyWith(isBalanceVisible: () => event.visible));
  }

  Future<void> _onGetBalanceEvent(
      GetBalanceEvent event, Emitter<MainHomeState> emit) async {
    emit(state.copyWith(getBalanceStatus: () => AppStatus.loading));

    final getWithdrawBalanceUseCaseData =
        await getWithdrawBalanceUseCase(NoParams());
    getWithdrawBalanceUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                getBalanceStatus: () => AppStatus.failure,
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) {
      emit(
        state.copyWith(
            getBalanceStatus: () => AppStatus.success,
            withdrawBalance: () => data!),
      );
    });
  }

  void _onChangeTabMoreIndexEvent(
      ChangeTabMoreIndexEvent event, Emitter<MainHomeState> emit) {
    emit(
      state.copyWith(
        tabMoreIndex: () => event.tab,
      ),
    );
    // add event to log the tab click
    switch (event.tab) {
      case 0:
        getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.visitProfileClick);
        break;
      case 1:
        getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.cardsClick);
        break;
      case 2:
        getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.referralPageVisit);
        break;
      default:
    }
  }

  void _onChangeTabIndexEvent(
      ChangeTabIndexEvent event, Emitter<MainHomeState> emit) {
    emit(state.copyWith(tabIndex: () => event.tab));
    // add event to log the tab click
    switch (event.tab) {
      case 1:
        getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.myProductsClick);
        break;
      case 2:
        getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.invoicesClick);
        break;
      case 3:
        getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.cardsClick);
        break;
      default:
    }
  }

  Future<void> _onUpdateBiometricSettingsEvent(
    UpdateBiometricSettingsEvent event,
    Emitter<MainHomeState> emit,
  ) async {
    emit(
        state.copyWith(updateBiometricSettingsStatus: () => AppStatus.loading));

    final result =
        await updateBiometricSettingsUseCase(event.biometricSettingsInput);
    userSecureDataSource
        .setBioMetricEnabled(event.biometricSettingsInput.isBiometricEnabled);
    result.fold(
      (failure) {
        emit(state.copyWith(
          updateBiometricSettingsStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (biometricSettings) {
        emit(state.copyWith(
          isBioMetricEnabledForThisDevice: () =>
              event.biometricSettingsInput.isBiometricEnabled,
          updateBiometricSettingsStatus: () => AppStatus.success,
        ));
      },
    );
  }
}
