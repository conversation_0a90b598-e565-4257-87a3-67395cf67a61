import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../repositories/bank_repository.dart';

class DeleteBankUseCase implements UseCase<String?, DeleteBankParams> {
  BankRepository? bankRepository;

  DeleteBankUseCase({this.bankRepository});

  @override
  Future<Either<Failure, String?>?> call(DeleteBankParams params) async {
    return await bankRepository?.deleteBank(bankId: params.bankId);
  }
}

class DeleteBankParams {
  final int? bankId;

  DeleteBankParams({this.bankId});
}
