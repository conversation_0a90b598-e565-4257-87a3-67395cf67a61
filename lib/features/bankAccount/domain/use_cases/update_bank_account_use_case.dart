import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/add_bank.dart';
import '../repositories/bank_repository.dart';

class UpdateBankAccountUseCase implements UseCase<AddBankSuccessfully?, UpdateBankParams> {
  BankRepository? bankRepository;

  UpdateBankAccountUseCase({this.bankRepository});

  @override
  Future<Either<Failure, AddBankSuccessfully?>?> call(UpdateBankParams params) async {
    return await bankRepository?.updateBankDetails(
      paymentType: params.paymentType,
      bankId: params.bankId,
      beneficiaryIban: params.beneficiaryIban,
      beneficiaryName: params.beneficiaryName,
      paymentMethodId: params.paymentMethodId,
    );
  }
}

class UpdateBankParams {
  int? paymentMethodId;
  String? paymentType;
  int? bankId;
  String? beneficiaryIban;
  String? beneficiaryName;

  UpdateBankParams(
      {required this.paymentType,
      required this.paymentMethodId,
      required this.bankId,
      required this.beneficiaryIban,
      required this.beneficiaryName});
}
