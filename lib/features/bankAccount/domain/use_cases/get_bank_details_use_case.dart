import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/bank_details.dart';
import '../repositories/bank_repository.dart';

class GetBankDetailsUseCase
    implements UseCase<BankDetails?, GetBankDetailsParams> {
  BankRepository? bankRepository;

  GetBankDetailsUseCase({this.bankRepository});

  @override
  Future<Either<Failure, BankDetails?>?> call(
      GetBankDetailsParams params) async {
    return await bankRepository?.getBankDetails(bankId: params.bankId);
  }
}

class GetBankDetailsParams {
  final int? bankId;

  GetBankDetailsParams({this.bankId});
}
