import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/add_bank.dart';
import '../entities/bank.dart';
import '../entities/bank_details.dart';

abstract class BankRepository {
  Future<Either<Failure, AddBankSuccessfully?>?>? addBank({
    required String? paymentType,
    required int? bankId,
    required String? beneficiaryIban,
    required String? beneficiaryName,
  });

  Future<Either<Failure, AddBankSuccessfully?>?>? updateBankDetails({
    required int? paymentMethodId,
    required String? paymentType,
    required int? bankId,
    required String? beneficiaryIban,
    required String? beneficiaryName,
  });

  Future<Either<Failure, List<Bank?>?>?>? getBankList();

  Future<Either<Failure, String?>?>? deleteBank({required int? bankId});

  Future<Either<Failure, BankDetails?>?>? getBankDetails(
      {required int? bankId});
}
