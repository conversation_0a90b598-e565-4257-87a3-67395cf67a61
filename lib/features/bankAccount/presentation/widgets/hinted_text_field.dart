import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import '../../../onboarding/presentation/widgets/suffix_icon.dart';

class HintedTextField extends StatefulWidget {
  final Function() _onTextFieldChanged;
  final String? hintText;
  final String? initialValue;
  final TextEditingController controller;

  const HintedTextField({
    super.key,
    required Function() onTextFieldChanged,
    required this.hintText,
    this.initialValue,
    required this.controller,
  }) : _onTextFieldChanged = onTextFieldChanged;

  @override
  State<HintedTextField> createState() => _HintedTextFieldState();
}

class _HintedTextFieldState extends State<HintedTextField> {
  late FocusNode focusNode;
  late bool _isActive = false;

  @override
  void initState() {
    focusNode = FocusNode();
    focusNode.addListener(() {
      setState(() {
        _isActive = focusNode.hasFocus;
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      style: TextStyle(color: context.black),
      onChanged: (value) {
        setState(() {});
        widget._onTextFieldChanged();
      },
      controller: widget.controller,
      focusNode: focusNode,
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.never,
        suffixIcon: widget.controller.text.isNotEmpty && _isActive
            ? SuffixIcon(
                onTap: () {
                  widget.controller.clear();
                  widget._onTextFieldChanged();
                },
              )
            : null,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        labelText: widget.hintText,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
          color: context.lightBlack,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.r),
          borderSide: BorderSide(color: Colors.grey),
        ),
      ),
    );
  }
}
