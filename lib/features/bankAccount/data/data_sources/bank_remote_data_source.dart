import 'package:thrivve/core/app_client/app_client.dart';

import '../../../../core/api/api_settings.dart';
import '../../domain/entities/add_bank.dart';
import '../../domain/entities/bank.dart';
import '../../domain/entities/bank_details.dart';
import '../models/add_bank_model.dart';
import '../models/bank_details_model.dart';
import '../models/bank_model.dart';

abstract class BankRemoteDataSource {
  /// Call the https://fintech.services.wedeliverapp.com/api/v1/mobile/public/vehicles endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<AddBankSuccessfully?>? addBank(
      {required String? paymentType,
      required int? bankId,
      required String? beneficiaryIban,
      required String? beneficiaryName});

  /// Call the https://fintech.services.wedeliverapp.com/api/v1/mobile/public/vehicles endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<AddBankSuccessfully?>? updateBankDetails(
      {required String? paymentType,
      required int? paymentMethodId,
      required int? bankId,
      required String? beneficiaryIban,
      required String? beneficiaryName});

  /// Call the https://fintech.services.wedeliverapp.com/api/v1/mobile/public/vehicles endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<List<Bank?>?>? getBanks();

  /// Call the https://fintech.services.wedeliverapp.com/api/v1/mobile/public/vehicles endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<String?>? deleteBank({required int? bankId});

  /// Call the https://fintech.services.wedeliverapp.com/api/v1/mobile/public/vehicles endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<BankDetails?>? getBankDetails({required int? bankId});
}

class BankRemoteDataSourceImpl implements BankRemoteDataSource {
  final ApiClient apiClient;

  BankRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<AddBankSuccessfully?> addBank({
    required String? paymentType,
    required int? bankId,
    required String? beneficiaryIban,
    required String? beneficiaryName,
  }) async {
    final response = await apiClient.request<AddBankSuccessfully?>(
      endpoint: ApiSettings.thrivvePaymentMethod,
      method: RequestType.post,
      data: {
        "payment_type": paymentType,
        "bank_id": bankId,
        "beneficiary_iban": beneficiaryIban,
        "beneficiary_name": beneficiaryName,
      },
      fromJson: (json) => AddBankModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<AddBankSuccessfully?> updateBankDetails({
    required String? paymentType,
    required int? paymentMethodId,
    required int? bankId,
    required String? beneficiaryIban,
    required String? beneficiaryName,
  }) async {
    final response = await apiClient.request<AddBankSuccessfully?>(
      endpoint: "${ApiSettings.thrivvePaymentMethod}/$paymentMethodId",
      method: RequestType.post,
      data: {
        "payment_type": paymentType,
        "bank_id": bankId,
        "beneficiary_iban": beneficiaryIban,
        "beneficiary_name": beneficiaryName,
      },
      fromJson: (json) => AddBankModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<List<Bank?>?> getBanks() async {
    final response = await apiClient.request<List<Bank?>?>(
      endpoint: ApiSettings.thrivveBanks,
      fromJson: (json) =>
          (json as List).map((x) => BankModel.fromJson(x)).toList(),
    );
    return response.data;
  }

  @override
  Future<String?> deleteBank({required int? bankId}) async {
    final response = await apiClient.request<Map<String, dynamic>>(
      endpoint: "${ApiSettings.thrivvePaymentMethod}/$bankId/delete",
      method: RequestType.post,
      fromJson: (json) => json,
    );
    return response.data?["message"];
  }

  @override
  Future<BankDetails?> getBankDetails({required int? bankId}) async {
    final response = await apiClient.request<BankDetails?>(
      endpoint: "${ApiSettings.thrivvePaymentMethod}/$bankId",
      fromJson: (json) => BankDetailsModel.fromJson(json),
    );
    return response.data;
  }
}
