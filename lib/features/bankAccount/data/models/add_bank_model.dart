// To parse this JSON data, do
//
//     final addBankModel = addBankModelFromJson(jsonString);

import '../../domain/entities/add_bank.dart';

class AddBankModel extends AddBankSuccessfully {
  @override
  final String? message;
  @override
  final String? title;
  @override
  final String? description;

  const AddBankModel({
    required this.message,
    this.title,
    this.description,
  }) : super(message: message, title: title, description: description);

  factory AddBankModel.fromJson(Map<String, dynamic> json) => AddBankModel(
        message: json["message"],
        title: json["title"],
        description: json["description"],
      );
}
