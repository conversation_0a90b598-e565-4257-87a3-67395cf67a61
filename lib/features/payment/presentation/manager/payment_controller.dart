import 'package:get/get.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/iterable_compact_map.dart';
import 'package:thrivve/features/payment/domain/entities/saved_card_entity.dart';
import 'package:thrivve/features/payment/domain/use_cases/delete_saved_card_use_case.dart';
import 'package:thrivve/features/payment/domain/use_cases/list_saved_cards_use_case.dart';

class PaymentController extends GetxController {
  final ListSavedCardsUseCase listSavedCardsUseCase;
  final DeleteSavedCardUseCase deleteSavedCardUseCase;
  final IPageLoadingDialog iPageLoadingDialog;
  PaymentController({
    required this.listSavedCardsUseCase,
    required this.deleteSavedCardUseCase,
    required this.iPageLoadingDialog,
  });

  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  final RxList<SavedCardEntity> savedCards = <SavedCardEntity>[].obs;
  final RxBool isLoadingSavedCards = false.obs;
  final RxString errorSavedCards = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchSavedCards();
  }

  Future<void> deleteCard(int? id) async {
    if (id == null) return;
    final loader = iPageLoadingDialog.showLoadingDialog();
    final result = await deleteSavedCardUseCase.call(id);
    loader.hide();
    result?.fold(
      (failure) {
        final error = mapFailureToMessage(failure);
        errorSnackBar(context: Get.context!, title: "err".tr, message: error);
      },
      (result) {
        showSuccessSnackBar('', result ?? '');
        savedCards.removeWhere((e) => e.id == id);
      }, // Return the actual list on success
    );
  }

  Future<void> fetchSavedCards() async {
    isLoadingSavedCards.value = true;
    errorSavedCards.value = '';

    final result = await listSavedCardsUseCase.call(NoParams());
    isLoadingSavedCards.value = false;
    result?.fold(
      (failure) {
        final error = mapFailureToMessage(failure);
        errorSavedCards.value = error;
      },
      (list) {
        savedCards.assignAll(list?.noneNullList() ?? []);
        return list;
      }, // Return the actual list on success
    );
  }
}
