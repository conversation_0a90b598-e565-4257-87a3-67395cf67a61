import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/payment/domain/entities/saved_card_entity.dart';
import 'package:thrivve/features/payment/presentation/manager/payment_controller.dart';
import 'package:thrivve/features/payment/presentation/widgets/saved_card_widget.dart';
import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/generated/assets.dart';

class SavedCardsPage extends GetView<PaymentController> {
  const SavedCardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.bottomsheetColor,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Shrink-wrap the column
        children: [
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildCloseButton(context),
              _header(context),
              SizedBox(width: 40.w),
            ],
          ),
          SizedBox(height: 24.h),
          _body(context),
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 16.w),
      child: Container(
        height: 40.h,
        width: 40.w,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: context.appBackgroundColor,
        ),
        child: InkWell(
          onTap: () => Get.back(),
          child: Icon(
            Icons.close,
            color: context.black,
            size: 20.w,
          ),
        ),
      ),
    );
  }

  Widget _header(BuildContext context) {
    return CustomTextWidget(
      title: 'saved_cards'.tr.orDefault,
      fontWeight: FontWeight.w600,
      size: 15,
      textAlign: TextAlign.center,
      color: context.black,
    );
  }

  Widget _body(BuildContext context) {
    return Obx(() {
      if (controller.isLoadingSavedCards.value) {
        return _loadingWidget(context);
      }

      if (controller.errorSavedCards.value.isNotEmpty) {
        return _errorWidget(context);
      }

      if (controller.savedCards.isEmpty) {
        return _emptyWidget(context);
      }

      return ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: 800.h,
          minHeight: 300.h,
        ),
        child: ListView.builder(
          shrinkWrap: true,
          physics: const ClampingScrollPhysics(),
          padding: EdgeInsets.all(16.w),
          itemCount: controller.savedCards.length,
          itemBuilder: (context, index) {
            final item = controller.savedCards[index];
            return Slidable(
              key: ValueKey(item.id),
              endActionPane: ActionPane(
                motion: const ScrollMotion(), // Smooth scroll effect
                extentRatio: 0.25, // Show only 25% of the width for the action
                children: [
                  SlidableAction(
                    onPressed: (context) async {
                      bool? confirm = await _confirmDismissSheet(context);
                      if (confirm == true) {
                        controller.deleteCard(item.id);
                      }
                    },
                    foregroundColor: Colors.red,
                    backgroundColor: Colors.transparent,
                    icon: Icons.delete,
                    label: 'delete'.tr,
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                ],
              ),
              child: SavedCardWidget(
                cardEntity: item,
                onTap: () {},
              ),
            );
          },
        ),
      );
    });
  }

  Widget _errorWidget(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: 300.h,
        minHeight: 300.h,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomTextWidget(
              title: 'error_loading_saved_cards'.tr,
              size: 14,
              textAlign: TextAlign.center,
              color: context.black,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: controller.fetchSavedCards,
              child: Text('retry'.tr),
            ),
          ],
        ),
      ),
    );
  }

  Widget _emptyWidget(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
          maxHeight: 300.h, minHeight: 300.h // Upper limit for loading state
          ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: CustomTextWidget(
          size: 14,
          title: 'no_available_saved_cards'.tr,
          fontWeight: FontWeight.w600,
          textAlign: TextAlign.center,
          color: context.black,
        ),
      ),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: 400.h,
        minHeight: 300.h,
        // Upper limit for loading state
      ),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsetsDirectional.only(
          start: 16.w,
          end: 16.w,
        ),
        itemCount: 2,
        itemBuilder: (context, index) {
          return Shimmer.fromColors(
            baseColor: context.borderAddBranch,
            highlightColor: context.appBackgroundColor,
            enabled: true,
            child: SavedCardWidget(
              cardEntity: SavedCardEntity(
                id: 1,
                cardTypeEnum: CardTypeEnum.migs,
                maskedPan: '**********',
              ),
              onTap: () {},
            ),
          );
        },
      ),
    );
  }

  Future<bool> _confirmDismissSheet(BuildContext context) async {
    final result = await showGeneralBottomSheet<bool>(
      context: context,
      title: "delete_card".tr,
      description: "delete_card_description".tr,
      firstBtnText: "yes_delete_it".tr,
      secondBtnText: "no_delete_it".tr,
      icon: Assets.thrivvePhotosDeleteAccount,
      firstBtnOnClick: () {
        Get.back(result: true);
      },
      secondBtnOnClick: () {
        Get.back(result: false);
      },
    );
    return result ?? false;
  }
}
