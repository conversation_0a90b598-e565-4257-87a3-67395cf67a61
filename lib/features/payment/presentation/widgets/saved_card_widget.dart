import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/payment/domain/entities/saved_card_entity.dart';
import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/generated/assets.dart';

class SavedCardWidget extends StatelessWidget {
  final SavedCardEntity cardEntity;
  final VoidCallback onTap;

  const SavedCardWidget({
    super.key,
    required this.cardEntity,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      color: isDarkMode ? context.secondaryColor : null,
      margin: EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Image.asset(
                isDarkMode
                    ? getDarkImage(cardEntity.cardTypeEnum)
                    : cardEntity.image ?? Assets.paymentMethodVisaMaster,
                height: 32.h,
                width: (cardEntity.cardTypeEnum == CardTypeEnum.migs)
                    ? 105.w
                    : null,
                alignment: AlignmentDirectional.centerEnd,
                fit: BoxFit.fitWidth,
              ),
              SizedBox(width: 16),
              Expanded(
                child: Text(
                  cardEntity.maskedPan ?? '',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: context.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String getDarkImage(CardTypeEnum? cardType) {
    switch (cardType) {
      case CardTypeEnum.migs:
        return Assets.paymentMethodVisaMasterWhite;
      case CardTypeEnum.apple_pay:
        return Assets.paymentMethodApplePayWhite;
      case CardTypeEnum.google_pay:
        return Assets.paymentMethodGooglePayWhite;
      case CardTypeEnum.Bank:
        return Assets.paymentMethodBankWhite;
      default:
        return Assets.paymentMethodVisaMasterWhite;
    }
  }
}
