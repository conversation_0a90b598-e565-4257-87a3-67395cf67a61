import 'dart:convert';

/// message : "Card deleted successfully"

DeleteCardModel deleteCardModelFromJson(String str) =>
    DeleteCardModel.fromJson(json.decode(str));
String deleteCardModelToJson(DeleteCardModel data) =>
    json.encode(data.toJson());

class DeleteCardModel {
  DeleteCardModel({
    String? message,
  }) {
    _message = message;
  }

  DeleteCardModel.fromJson(dynamic json) {
    _message = json['message'];
  }
  String? _message;
  DeleteCardModel copyWith({
    String? message,
  }) =>
      DeleteCardModel(
        message: message ?? _message,
      );
  String? get message => _message;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['message'] = _message;
    return map;
  }
}
