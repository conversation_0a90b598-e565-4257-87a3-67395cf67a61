import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/payment/data/data_sources/i_payment_data_source.dart';
import 'package:thrivve/features/payment/data/models/delete_card_model.dart';
import 'package:thrivve/features/payment/data/models/saved_card_model.dart';

class PaymentDataSourceImpl extends IPaymentDataSource {
  final ApiClient apiClient;

  PaymentDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<List<SavedCardModel>?>? listSavedCards() async {
    final response = await apiClient.request<List<SavedCardModel>>(
      endpoint: ApiSettings.savedCardsPayment,
      fromJson: (json) => listSavedCardModelFromJson(json),
    );
    return response.data;
  }

  @override
  Future<DeleteCardModel?>? deleteCard(int id) async {
    final response = await apiClient.request<DeleteCardModel?>(
      endpoint: ApiSettings.deleteSavedCardPayment(id),
      method: RequestType.post,
      fromJson: (json) => DeleteCardModel.fromJson(json),
    );
    return response.data;
  }
}
