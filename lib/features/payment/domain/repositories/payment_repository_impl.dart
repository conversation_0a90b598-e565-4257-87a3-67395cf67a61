import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/payment/data/data_sources/i_payment_data_source.dart';
import 'package:thrivve/features/payment/data/mapper/saved_card_extention.dart';
import 'package:thrivve/features/payment/domain/entities/saved_card_entity.dart';
import 'package:thrivve/features/payment/domain/repositories/i_repository_payment.dart';

class PaymentRepositoryImpl extends IPaymentRepository {
  final IPaymentDataSource paymentDataSource;
  final NetworkInfo networkInfo;

  PaymentRepositoryImpl({
    required this.paymentDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<SavedCardEntity?>?>>? listSavedCards() async {
    if ((await networkInfo.isConnected) ?? false) {
      try {
        final result = await paymentDataSource.listSavedCards();
        return Right(result?.map((e) => e.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String?>>? deleteCard(int id) async {
    if ((await networkInfo.isConnected) ?? false) {
      try {
        final result = await paymentDataSource.deleteCard(id);
        return Right(result?.message ?? '');
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }
}
