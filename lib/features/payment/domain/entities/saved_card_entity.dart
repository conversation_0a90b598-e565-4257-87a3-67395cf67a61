import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';

class SavedCardEntity {
  final CardTypeEnum? cardTypeEnum;
  final String? maskedPan;
  final bool? isActive;
  final int? id;
  final bool? isDefault;
  final String? token;
  final String? image;
  final String? realType;

  SavedCardEntity({
    this.cardTypeEnum,
    this.maskedPan,
    this.isActive,
    this.id,
    this.isDefault,
    this.token,
    this.image,
    this.realType,
  });
}
