// lib/presentation/bloc/faq_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:thrivve/core/util/iterable_compact_map.dart';

import '../../../../core/util/app_status.dart';
import '../../../../core/util/general_helper.dart';
import '../../domain/use_cases/get_faq_by_id_usecase.dart';
import '../../domain/use_cases/get_faqs_usecase.dart';
import 'faq_event.dart';
import 'faq_state.dart';

class FaqBloc extends Bloc<FaqEvent, FaqState> {
  final GetFaqsUseCase getFaqsUseCase;
  final GetFaqByIdUseCase getFaqByIdUseCase;

  FaqBloc({
    required this.getFaqsUseCase,
    required this.getFaqByIdUseCase,
  }) : super(const FaqState()) {
    on<FetchFaqs>(_onFetchFaqs);
    on<FetchFaqById>(_onFetchFaqById);
  }

  Future<void> _onFetchFaqs(FetchFaqs event, Emitter<FaqState> emit) async {
    // if chars are less than 3, don't search unitl 3 chars are entered
    if (event.searchText?.isNotEmpty == true && event.searchText!.length < 3) {
      return;
    }

    emit(state.copyWith(status: () => AppStatus.loading));

    final faqs =
        await getFaqsUseCase(GetFlagParams(searchText: event.searchText));
    faqs.fold(
      (failure) {
        emit(state.copyWith(
          status: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (faqs) {
        emit(state.copyWith(
          faqs: () => faqs?.noneNullList() ?? [],
          status: () => AppStatus.success,
        ));
      },
    );
  }

  Future<void> _onFetchFaqById(
      FetchFaqById event, Emitter<FaqState> emit) async {
    emit(state.copyWith(getFaqsStatus: () => AppStatus.loading));

    final faq = await getFaqByIdUseCase(event.id);
    faq.fold(
      (failure) {
        emit(state.copyWith(
          getFaqsStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ));
      },
      (faq) {
        emit(state.copyWith(
          faqById: () => faq,
          getFaqsStatus: () => AppStatus.success,
        ));
      },
    );
  }
}
