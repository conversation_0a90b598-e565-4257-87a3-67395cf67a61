// lib/presentation/bloc/faq_state.dart
import 'package:equatable/equatable.dart';
import '../../../../core/util/app_status.dart';
import '../../domain/entities/faq.dart';

class FaqState extends Equatable {
  final AppStatus status;
  final AppStatus getFaqsStatus;
  final List<Faq>? faqs;
  final Faq? faqById;
  final String? errorMessage;

  const FaqState({
    this.status = AppStatus.initial,
    this.getFaqsStatus = AppStatus.initial,
    this.faqs,
    this.faqById,
    this.errorMessage,
  });

  FaqState copyWith({
    AppStatus Function()? status,
    AppStatus Function()? getFaqsStatus,
    List<Faq>? Function()? faqs,
    Faq? Function()? faqById,
    String? Function()? errorMessage,
  }) {
    return FaqState(
      status: status != null ? status() : this.status,
      getFaqsStatus:
          getFaqsStatus != null ? getFaqsStatus() : this.getFaqsStatus,
      faqs: faqs != null ? faqs() : this.faqs,
      faqById: faqById != null ? faqById() : this.faqById,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        faqs,
        faqById,
        errorMessage,
        getFaqsStatus,
      ];
}
