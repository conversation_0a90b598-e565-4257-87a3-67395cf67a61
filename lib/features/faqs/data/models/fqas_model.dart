// lib/data/models/faq_model.dart
import 'package:thrivve/core/util/const.dart';

import '../../domain/entities/faq.dart';

class FaqModel extends Faq {
  final String? questionEn;
  final String? questionAr;
  final String? answerEn;
  final String? answerAr;
  final int? idModel;
  final int? categoryIdModel;
  final String? language;
  final String? statusModel;
  @override
  final String? videoUrl;
  @override
  final List<String?>? imagesUrls;
  @override
  final String? attachmentType;
  final String? lang;
  final CategoryModel? categoryModel;

  FaqModel({
    required this.idModel,
    required this.questionEn,
    required this.questionAr,
    required this.answerEn,
    required this.answerAr,
    required this.videoUrl,
    required this.categoryIdModel,
    required this.language,
    required this.statusModel,
    required this.categoryModel,
    required this.lang,
    this.imagesUrls,
    this.attachmentType,
  }) : super(
          answer: lang == valueArLanguage ? answerAr ?? "" : answerEn ?? "",
          question:
              lang == valueArLanguage ? questionAr ?? "" : questionEn ?? "",
          status: statusModel ?? "",
          category: categoryModel,
          videoUrl: videoUrl,
          id: idModel ?? 0,
          imagesUrls: imagesUrls,
          attachmentType: attachmentType,
        );

  factory FaqModel.fromJson(Map<String, dynamic> json, String? lang) {
    return FaqModel(
      lang: lang,
      idModel: json['id'],
      questionEn: json['question_en'],
      questionAr: json['question_ar'],
      answerEn: json['answer_en'],
      answerAr: json['answer_ar'],
      categoryIdModel: json['category_id'],
      language: json['language'],
      statusModel: json['status'],
      videoUrl: json['video_url'],
      imagesUrls: List<String?>.from(json['images_urls'] ?? []),
      attachmentType: json['attachment_type'],
      categoryModel: CategoryModel.fromJson(json['category'], lang),
    );
  }
}

class CategoryModel extends Category {
  final String? textArModel;
  final int? idModel;
  final String? textEnModel;
  final String? lang;

  CategoryModel({
    required this.textArModel,
    required this.idModel,
    required this.textEnModel,
    required this.lang,
  }) : super(
          id: idModel ?? 0,
          text: lang == valueArLanguage ? textArModel ?? "" : textEnModel ?? "",
        );

  factory CategoryModel.fromJson(Map<String, dynamic> json, String? lang) {
    return CategoryModel(
      lang: lang,
      textArModel: json['text_ar'],
      idModel: json['id'],
      textEnModel: json['text_en'],
    );
  }
}
