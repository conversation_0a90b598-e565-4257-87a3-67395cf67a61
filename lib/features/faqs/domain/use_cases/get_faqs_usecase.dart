// lib/domain/usecases/get_faqs_usecase.dart
import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/faq.dart';
import '../repositories/faq_repository.dart';

class GetFaqsUseCase implements UseCase<List<Faq?>?, GetFlagParams> {
  final FaqRepository repository;

  GetFaqsUseCase({required this.repository});

  @override
  Future<Either<Failure, List<Faq?>?>> call(GetFlagParams params) async {
    return await repository.getFaqs(searchText: params.searchText);
  }
}

class GetFlagParams {
  String? searchText;

  GetFlagParams({
    required this.searchText,
  });
}
