// lib/domain/entities/faq.dart
class Faq {
  final int id;
  final String answer;
  final String question;
  final String status;
  final Category? category;
  final String? videoUrl;
  final List<String?>? imagesUrls;
  final String? attachmentType;

  // final Language language;

  Faq({
    required this.id,
    required this.answer,
    required this.question,
    required this.status,
    required this.category,
    this.videoUrl,
    this.imagesUrls,
    this.attachmentType,
    // required this.language,
  });
}

class Category {
  final String text;
  final int id;

  Category({
    required this.text,
    required this.id,
  });
}
