import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:thrivve/core/flavor/flavor_config.dart';

import 'analytics_logger.dart';

class FirebaseAnalyticsLogger implements IAnalyticsLogger {
  final FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.instance;
  @override
  Future<void> logEvent(
    String name, {
    Map<String, Object>? parameters = const {},
  }) async {
    debugPrint('log event => name $name  parms : $parameters');

    // Validate and convert parameters if needed
    final validatedParameters = parameters != null
        ? _validateParameters(parameters)
        : <String, Object>{};

    await firebaseAnalytics.logEvent(
      name: name,
      parameters: validatedParameters,
    );
  }

  /// Validates parameters ensuring they are all strings or numbers
  /// Converts other types to strings and ensures keys are strings
  Map<String, Object> _validateParameters(Map<String, Object>? parameters) {
    if (parameters == null) return <String, Object>{};

    final result = <String, Object>{};

    parameters.forEach((key, value) {
      final String stringKey = key.toString();

      // Only keep as is if already a String or num
      if (value is String || value is num) {
        result[stringKey] = value;
      } else {
        // Convert other types to String
        result[stringKey] = value.toString();
      }
    });

    return result;
  }

  @override
  Future<void> logProperty(String name, String value) async {
    debugPrint('logProperty => name $name  value : $value');
    await firebaseAnalytics.setUserProperty(
      name: name,
      value: value,
    );
  }

  @override
  List<Flavor> get supportedEnvironments => [
        Flavor.prod,
        Flavor.stg,
        Flavor.dev,
      ];

  @override
  Future<void> sendUserId(String id) async {
    await firebaseAnalytics.setUserId(id: id);
  }

  @override
  Future<void> logScreenView({String? screenClass, String? screenName}) async {
    await firebaseAnalytics.logScreenView(
        screenClass: screenClass, screenName: screenName);
  }
}
