import 'package:equatable/equatable.dart';

class Failure extends Equatable {
  @override
  List<Object?> get props => [];
}

class ServerFailure extends Failure {
  final String? msj;
  final int? code;

  ServerFailure({
    this.msj,
    this.code,
  });

  @override
  List<Object?> get props => [
        msj,
        code,
      ];
}

class ServerFailureWithListOfMessage extends Failure {
  final List<String?>? listOfErrorMessages;

  ServerFailureWithListOfMessage({this.listOfErrorMessages});

  @override
  List<Object?> get props => [listOfErrorMessages];
}

class CasheFailure extends Failure {
  final String? msj;

  CasheFailure({this.msj});

  @override
  List<Object?> get props => [msj];
}

class NetworkFailure extends Failure {
  final String? msj;
  final int? errorCode;

  NetworkFailure({this.msj, this.errorCode});

  @override
  List<Object?> get props => [msj, errorCode];
}
