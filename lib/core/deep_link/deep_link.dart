import 'dart:async';
import 'dart:convert';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:thrivve/app_lifecycle_managment.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/features/dashboard/domain/entities/message_entity.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/products/presentation/manager/products_bloc.dart';

import '../../features/dashboard/domain/entities/product.dart';
import '../app_routes.dart';
import '../localDataSource/user_secure_data_source.dart';

class DeepLink {
  final UserSecureDataSource _userSecureDataSource;
  final IAnalyticsLogger _analyticsLogger;

  DeepLink({
    required UserSecureDataSource userSecureDataSource,
    required IAnalyticsLogger analyticsLogger,
  })  : _userSecureDataSource = userSecureDataSource,
        _analyticsLogger = analyticsLogger;

  Future<StreamSubscription<Map<dynamic, dynamic>>> listenDynamicLinks() async {
    return FlutterBranchSdk.listSession().listen(
      (data) async {
        if (kDebugMode) {
          print('listenDynamicLinks - DeepLink Data: $data');
        }
        if (AppLifecycleHandler.instance.backgroundTime != null &&
            DateTime.now()
                    .difference(AppLifecycleHandler.instance.backgroundTime!) >=
                AppLifecycleHandler.lockDuration) {
          // Store the pending route to navigate after PIN verification
          AppLifecycleHandler.pendingRoute = 'dynamicLink';
          AppLifecycleHandler.arguments = data;
          // Redirect to PIN first
          await AppLifecycleHandler.instance.navigateToPinCode();
        } else {
          await handleDynamicLink(data);
        }
      },
      onError: (error) {
        if (kDebugMode) {
          print('listSession error: ${error.toString()}');
        }
        FirebaseCrashlytics.instance.recordError(error, null);
      },
    );
  }

  Future<void> handleDynamicLink(Map<dynamic, dynamic> data) async {
    try {
      if (data.isNotEmpty) {
        _logBreadcrumb(
          'Starting handleDynamicLink',
          'navigation',
          data: {'data': data},
        );
      }

      final appScreen = _extractAppScreen(data);
      final appScreenParameters = _extractAppScreenParameters(data);
      final processedData = _processData(data, appScreenParameters);

      await _logAnalyticsEvent(processedData);

      await _handleNavigation(appScreen, processedData);
    } catch (e, stackTrace) {
      _handleDynamicLinkError(e, stackTrace);
    }
  }

  String? _extractAppScreen(Map<dynamic, dynamic> data) {
    return data["\$app_screen"] ?? data["\$marketing_title"];
  }

  String? _extractAppScreenParameters(Map<dynamic, dynamic> data) {
    return data["\$app_screen_parameters"];
  }

  Map<String, dynamic> _processData(
    Map<dynamic, dynamic> data,
    String? appScreenParameters,
  ) {
    final processedData = Map<String, dynamic>.from(data);

    if (appScreenParameters != null) {
      try {
        final decodedParams = jsonDecode(appScreenParameters);
        processedData.addAll(Map<String, dynamic>.from(decodedParams));
      } catch (e) {
        _logBreadcrumb(
          'Error decoding app screen parameters',
          'error',
          level: SentryLevel.error,
          data: {'error': e.toString(), 'parameters': appScreenParameters},
        );
      }
    }

    return processedData;
  }

  Future<void> _logAnalyticsEvent(Map<String, dynamic> data) async {
    _analyticsLogger.logEvent(
      'deep_link_click_event',
      parameters: Map<String, Object>.from(data),
    );
  }

  Future<void> _handleNavigation(
    String? appScreen,
    Map<String, dynamic> data,
  ) async {
    if (appScreen == null) {
      return;
    }

    switch (appScreen) {
      case 'Uber Registration':
      case 'Rent Car':
        await _goToProductId(dlParams: data);
        break;
      case 'Multi Use':
        await _goTopProducts(data);
        break;
      default:
        _logBreadcrumb(
          'Unknown app screen type',
          'error',
          level: SentryLevel.warning,
          data: {'appScreen': appScreen},
        );
    }
  }

  Future<void> _goTopProducts(Map<String, dynamic> map) async {
    try {
      _logBreadcrumb(
        'Starting _goTopProducts',
        'navigation',
        data: {'map': map},
      );

      final isLogin = await _userSecureDataSource.isLogin();
      final mainHomeBloc = getIt<MainHomeBloc>();

      _navigateToProductsPage(mainHomeBloc, isLogin);
      await _handleCampAndProductId(map);
    } catch (e, stackTrace) {
      _handleError(e, stackTrace, 'Error in _goTopProducts');
    }
  }

  Future<void> _handleCampAndProductId(Map<String, dynamic> map) async {
    if (map.containsKey('thr_cam')) {
      await _saveThriveCamp(map['thr_cam']);
    }

    if (map.containsKey('thr_prod_id')) {
      await _handleProductId(map);
    }
  }

  Future<void> _saveThriveCamp(String camp) async {
    try {
      await _userSecureDataSource.setThrriveCamp(camp);
      _logBreadcrumb(
        'Saved Thrive camp',
        'data',
        data: {'camp': camp},
      );
    } catch (e) {
      _logBreadcrumb(
        'Error saving Thrive camp',
        'error',
        level: SentryLevel.error,
        data: {'error': e.toString(), 'camp': camp},
      );
    }
  }

  Future<void> _handleProductId(Map<String, dynamic> map) async {
    final thrParams = _extractThrParams(map);
    final data = {
      'product_id': map['thr_prod_id'],
      ...thrParams,
    };

    await _goToProductId(dlParams: data);
  }

  Map<String, dynamic> _extractThrParams(Map<String, dynamic> map) {
    return {
      for (var entry in map.entries)
        if (entry.key.startsWith('thr')) entry.key: entry.value
    };
  }

  Future<void> _goToProductId({
    Map<String, dynamic>? dlParams,
  }) async {
    try {
      _logBreadcrumb(
        'Starting _goToProductId',
        'navigation',
        data: {'dlParams': dlParams},
      );

      if (!_validateProductId(dlParams)) return;

      final isLogin = await _checkLoginStatus();
      final mainHomeBloc = getIt<MainHomeBloc>();
      final productBloc = getIt<ProductsBloc>();
      final dashBoardBloc = getIt<DashboardBloc>();

      final products = await _fetchProducts(productBloc, isLogin);
      if (products == null) return;

      _navigateToProductsPage(mainHomeBloc, isLogin);
      // this delayed for make sure the products page start loading

      await Future.delayed(Duration(seconds: 2));
      final product = _findAndValidateProduct(products, dlParams ?? {});
      if (product == null) return;

      _handleProductNavigation(
        product,
        isLogin,
        mainHomeBloc,
        dashBoardBloc,
        dlParams ?? {},
      );
    } catch (e, stackTrace) {
      _handleError(e, stackTrace, 'Error in _goToProductId');
    }
  }

  bool _validateProductId(Map<String, dynamic>? dlParams) {
    if (dlParams?['product_id'] == null) {
      _logBreadcrumb(
        'Product ID is missing in dlParams',
        'validation',
        level: SentryLevel.warning,
      );
      debugPrint('Product ID is missing in dlParams');
      return false;
    }
    return true;
  }

  Future<bool> _checkLoginStatus() async {
    final isLogin = await _userSecureDataSource.isLogin();
    _logBreadcrumb(
      'User login status checked',
      'auth',
      data: {'isLogin': isLogin},
    );
    return isLogin;
  }

  Future<dynamic> _fetchProducts(ProductsBloc productBloc, bool isLogin) async {
    _logBreadcrumb('Fetching products', 'api', data: {'isPublic': isLogin});

    final result = await productBloc.getProduct(
      isPublic: isLogin,
      page: 1,
    );

    if (result == null) {
      _logBreadcrumb(
        'Failed to get products - result is null',
        'error',
        level: SentryLevel.error,
      );
      debugPrint('Failed to get products');
      return null;
    }

    return result.fold(
      (error) {
        _logBreadcrumb(
          'Error getting products',
          'error',
          level: SentryLevel.error,
          data: {'error': error.toString()},
        );
        debugPrint('Error getting products: $error');
        return null;
      },
      (products) {
        if (products?.products == null || products!.products!.isEmpty) {
          _logBreadcrumb(
            'No products found',
            'data',
            level: SentryLevel.warning,
          );
          debugPrint('No products found');
          return null;
        }
        return products;
      },
    );
  }

  void _navigateToProductsPage(MainHomeBloc mainHomeBloc, bool isLogin) {
    _logBreadcrumb(
      'Navigating to products page',
      'navigation',
      data: {
        'isPublic': isLogin.inverted,
        'hasPersonalInfo': mainHomeBloc.state.personalInfo != null,
      },
    );

    Get.toNamed(AppRoutes.productsPage, arguments: {
      "personalInfo": mainHomeBloc.state.personalInfo,
      "canWithdraw": mainHomeBloc.state.isInstantPaymentIsEnable,
      "isPublic": isLogin.inverted,
    });
  }

  Product? _findAndValidateProduct(
    dynamic products,
    Map<String, dynamic> dlParams,
  ) {
    final productId = int.tryParse(dlParams['product_id'].toString());
    if (productId == null) {
      _logBreadcrumb(
        'Invalid product ID format',
        'validation',
        level: SentryLevel.error,
        data: {'productId': dlParams['product_id']},
      );
      debugPrint('Invalid product ID format');
      return null;
    }

    final product = findProductById(products.productsList ?? [], productId);
    if (product == null) {
      _logBreadcrumb(
        'Product not found',
        'data',
        level: SentryLevel.error,
        data: {'productId': productId},
      );
      debugPrint('Product not found with ID: $productId');
      return null;
    }

    return product;
  }

  Future<void> _handleProductNavigation(
    Product product,
    bool isLogin,
    MainHomeBloc mainHomeBloc,
    DashboardBloc dashBoardBloc,
    Map<String, dynamic> dlParams,
  ) async {
    if (isLogin) {
      _handleLoggedInUserNavigation(
        product,
        mainHomeBloc,
        dashBoardBloc,
        dlParams,
      );
    } else {
      _navigateToProductDetails(
        product,
        mainHomeBloc,
        dlParams,
        isLoggedIn: false,
      );
    }
  }

  void _handleLoggedInUserNavigation(
    Product product,
    MainHomeBloc mainHomeBloc,
    DashboardBloc dashBoardBloc,
    Map<String, dynamic> dlParams,
  ) async {
    _logBreadcrumb(
      'Getting home message for logged in user',
      'api',
    );

    final homeMessageResult = await dashBoardBloc.getHomeMessage();
    homeMessageResult?.fold((error) {
      _logBreadcrumb(
        'Error getting home message',
        'error',
        level: SentryLevel.error,
        data: {'error': error.toString()},
      );
      debugPrint('Error getting home message: $error');
    }, (homeMessage) async {
      _navigateToProductDetails(
        product,
        mainHomeBloc,
        dlParams,
        isLoggedIn: true,
        homeMessage: homeMessage,
      );
    });
  }

  // void _handleUberNavigation(
  //   Product product,
  //   MainHomeBloc mainHomeBloc,
  //   DashboardBloc dashBoardBloc,
  //   Map<String, dynamic> dlParams,
  //   MessageEntity? homeMessage,
  // ) async {
  //   final workWithUberResult = await dashBoardBloc.getWorkWithUberRequest();
  //   workWithUberResult?.fold(
  //     (err) {
  //       _logBreadcrumb(
  //         'Error getting workWithUber message',
  //         'error',
  //         level: SentryLevel.error,
  //         data: {'error': err.toString()},
  //       );
  //       debugPrint('Error getting workWithUber message: $err');
  //     },
  //     (workWithUber) {
  //       _navigateToProductDetails(
  //         product,
  //         mainHomeBloc,
  //         dlParams,
  //         isLoggedIn: true,
  //         homeMessage: homeMessage,
  //       );
  //     },
  //   );
  // }

  void _navigateToProductDetails(
    Product product,
    MainHomeBloc mainHomeBloc,
    Map<String, dynamic> dlParams, {
    required bool isLoggedIn,
    MessageEntity? homeMessage,
  }) {
    _logBreadcrumb(
      'Navigating to product details${isLoggedIn ? ' for logged in user' : ' for non-logged in user'}',
      'navigation',
      data: {
        'productId': product.id,
        'productName': product.title,
      },
    );

    navigateTo(
      Get.context!,
      product,
      mainHomeBloc.state.personalInfo,
      mainHomeBloc.state.isInstantPaymentIsEnable,
      dlParams: dlParams,
      message: homeMessage,
    );
  }

  void _handleDynamicLinkError(dynamic e, StackTrace stackTrace) {
    _handleError(e, stackTrace, 'Error in handleDynamicLink');
  }

  void _handleError(dynamic e, StackTrace stackTrace, String context) {
    _logBreadcrumb(
      context,
      'error',
      level: SentryLevel.error,
      data: {'error': e.toString()},
    );
    debugPrint('$context: $e');
    debugPrint('Stack trace: $stackTrace');
    FirebaseCrashlytics.instance.recordError(e, stackTrace);
    SentryService.instance.captureException(
      e,
      stackTrace: stackTrace,
    );
  }

  void _logBreadcrumb(
    String message,
    String category, {
    Map<String, dynamic>? data,
    SentryLevel level = SentryLevel.info,
  }) {
    SentryService.instance.addBreadcrumb(
      message: message,
      category: category,
      data: data,
    );
  }

  Product? findProductById(List<Product> products, int? id) {
    try {
      return products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }
}
