import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class CustomTextWidget extends StatelessWidget {
  const CustomTextWidget(
      {Key? key,
      this.title,
      this.color,
      this.size = 14,
      this.fontWeight = FontWeight.w400,
      this.paddingBottom = 0,
      this.decoration = TextDecoration.none,
      this.textOverflow,
      this.paddingStart = 0,
      this.spOff = false,
      this.paddingTop = 0,
      this.decorationThickness,
      this.height,
      this.colorDecoration,
      this.fontFamily,
      this.maxLine,
      this.paddingEnd = 0,
      this.textAlign = TextAlign.start,
      this.listShadow,
      this.textDirection,
      this.isRequired = false,
      this.iconSize,
      this.paddingTextBottom,
      this.paddingTextTop,
      this.showCurrencySymbol = true,
      this.debugMode = false,
      this.splitter = ""})
      : super(key: key);

  final String? title;
  final Color? color;
  final double size;
  final double? iconSize;
  final double paddingStart;
  final double paddingEnd;
  final double paddingTop;
  final double? paddingTextBottom;
  final double? paddingTextTop;
  final Color? colorDecoration;
  final double paddingBottom;
  final List<Shadow>? listShadow;
  final double? height;
  final bool spOff;
  final double? decorationThickness;
  final TextDecoration decoration;
  final TextOverflow? textOverflow;
  final FontWeight fontWeight;
  final String? fontFamily;
  final String splitter;
  final TextAlign textAlign;
  final int? maxLine;
  final TextDirection? textDirection;
  final bool isRequired;
  final bool showCurrencySymbol;
  final bool debugMode;

  // SVG string for the SAR icon

  // Comprehensive list of all possible SAR patterns
  static const List<String> sarPatterns = [
    "ر.س.", // Arabic with dots
    ".ر.س", // Reversed with dots
    "ر.س", // Arabic with one dot
    "ريال سعودي", // Saudi Riyal full phrase
    "SAR", // English acronym
    "sar", // English acronym lowercase
  ];

  Widget _buildCurrencyIcon(BuildContext context, Color textColor) {
    return Padding(
      padding: EdgeInsetsDirectional.only(
          top: Get.locale?.languageCode == 'en'
              ? (Platform.isAndroid ? 2.h : 1.h)
              : paddingTextTop ?? (Platform.isAndroid ? 0.h : 1.h),
          bottom:
              Get.locale?.languageCode == 'en' ? paddingTextBottom ?? 0 : 0),
      child: Text(
        '﷼',
        style: TextStyle(
          color: color ?? context.black,
          height: height,
          shadows: listShadow,
          decorationThickness: decorationThickness,
          decorationColor: colorDecoration,
          fontFamily: "SaudiRiyalFont",
          decoration: decoration,
          fontWeight: fontWeight,
          fontSize: spOff ? (size * 0.75) : (size * 0.75).sp,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final baseStyle = TextStyle(
      color: color ?? context.theme.textTheme.bodyMedium?.color,
      height: height,
      shadows: listShadow,
      decorationThickness: decorationThickness,
      decorationColor: colorDecoration,
      fontFamily: fontFamily ??
          (Get.locale?.languageCode == 'en' ? "NotoSans" : "NotoSansArabic"),
      decoration: decoration,
      fontWeight: fontWeight,
      fontSize: spOff ? size : size.sp,
    );

    // Determine the text direction based on locale or provided direction
    final effectiveTextDirection = textDirection ??
        (Get.locale?.languageCode == 'ar'
            ? TextDirection.rtl
            : TextDirection.ltr);

    // If currency symbols are disabled or no title, just return the plain text
    if (!showCurrencySymbol) {
      return Container(
        padding: EdgeInsetsDirectional.only(
          top: paddingTop,
          start: paddingStart,
          bottom: paddingBottom,
          end: paddingEnd,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                title ?? '',
                style: baseStyle,
                textDirection: effectiveTextDirection,
                overflow: textOverflow ?? TextOverflow.clip,
                textAlign: textAlign,
                maxLines: maxLine,
              ),
            ),
            if (isRequired)
              Text(
                '*',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: context.theme.primaryColor,
                ),
              ),
          ],
        ),
      );
    }

    final String text = title!;

    // Normalize the text for currency patterns
    String normalizedText = text;
    if (showCurrencySymbol) {
      // Replace 'ريال سعودي' (with optional spaces) and 'ريال' with 'ر.س'
      normalizedText =
          normalizedText.replaceAll(RegExp(r'ريال\s*سعودي'), 'ر.س');
    }

    // Use normalizedText instead of text below
    // CASE 1: Check if the text is EXACTLY a currency pattern with nothing else
    bool isExactCurrencyPattern = false;

    for (final pattern in sarPatterns) {
      if (normalizedText.trim() == pattern) {
        isExactCurrencyPattern = true;
        break;
      }
    }

    // If the text is exactly a currency pattern, display just the icon
    if (isExactCurrencyPattern) {
      return Container(
        padding: EdgeInsetsDirectional.only(
          top: paddingTop,
          start: paddingStart,
          bottom: paddingBottom,
          end: paddingEnd,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildCurrencyIcon(context, baseStyle.color ?? Colors.black),
            if (isRequired)
              Text(
                '*',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: context.theme.primaryColor,
                ),
              ),
          ],
        ),
      );
    }

    // CASE 2: Check if we have numbers with currency patterns
    // Define regex for numbers
    final RegExp numberRegex = RegExp(r'\d+(?:,\d+)*(?:\.\d+)?', unicode: true);
    final List<RegExpMatch> numberMatches =
        numberRegex.allMatches(normalizedText).toList();

    // If no numbers, just return the text as is (we already checked for exact patterns)
    if (numberMatches.isEmpty) {
      return Container(
        padding: EdgeInsetsDirectional.only(
          top: paddingTop,
          start: paddingStart,
          bottom: paddingBottom,
          end: paddingEnd,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                normalizedText,
                style: baseStyle,
                textDirection: effectiveTextDirection,
                overflow: textOverflow ?? TextOverflow.clip,
                textAlign: textAlign,
                maxLines: maxLine,
              ),
            ),
            if (isRequired)
              Text(
                '*',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: context.theme.primaryColor,
                ),
              ),
          ],
        ),
      );
    }

    // We have numbers, check for currency patterns near each number
    List<Map<String, dynamic>> currencyNumberPairs = [];

    for (final match in numberMatches) {
      final int start = match.start;
      final int end = match.end;
      final String number = match.group(0) ?? '';

      // Look for currency patterns near this number (within 5 characters)
      String? nearestPattern;
      int patternStart = -1;
      int patternEnd = -1;

      // Check in a window around the number
      final int contextStart = math.max(0, start - 5);
      final int contextEnd = math.min(normalizedText.length, end + 5);
      final String context = normalizedText.substring(contextStart, contextEnd);

      for (final pattern in sarPatterns) {
        if (context.contains(pattern)) {
          final int idx = context.indexOf(pattern);
          final int actualStart = contextStart + idx;

          nearestPattern = pattern;
          patternStart = actualStart;
          patternEnd = actualStart + pattern.length;
          break;
        }
      }

      // If we found a pattern near this number, add it to our list
      if (nearestPattern != null) {
        currencyNumberPairs.add({
          'number': number,
          'numberStart': start,
          'numberEnd': end,
          'pattern': nearestPattern,
          'patternStart': patternStart,
          'patternEnd': patternEnd,
        });
      }
    }

    // If we didn't find any currency-number pairs, return the text as is
    if (currencyNumberPairs.isEmpty) {
      return Container(
        padding: EdgeInsetsDirectional.only(
          top: paddingTop,
          start: paddingStart,
          bottom: paddingBottom,
          end: paddingEnd,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                normalizedText,
                style: baseStyle,
                textDirection: effectiveTextDirection,
                overflow: textOverflow ?? TextOverflow.clip,
                textAlign: textAlign,
                maxLines: maxLine,
              ),
            ),
            if (isRequired)
              Text(
                '*',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: context.theme.primaryColor,
                ),
              ),
          ],
        ),
      );
    }

    // Sort the pairs by position
    currencyNumberPairs
        .sort((a, b) => a['numberStart'].compareTo(b['numberStart']));

    // Debug logging if enabled
    if (debugMode) {
      print("Found currency-number pairs:");
      for (var pair in currencyNumberPairs) {
        print(pair);
      }
    }

    // Now build the segments
    List<Map<String, dynamic>> segments = [];
    int lastIndex = 0;

    for (var pair in currencyNumberPairs) {
      // Determine the start and end of this region
      final int regionStart =
          math.min(pair['numberStart'], pair['patternStart']);
      final int regionEnd = math.max(pair['numberEnd'], pair['patternEnd']);

      // Add text before this region
      if (regionStart > lastIndex) {
        segments.add({
          'type': 'text',
          'content': normalizedText.substring(lastIndex, regionStart),
        });
      }

      // Add the currency-number pair
      segments.add({
        'type': 'currency-number',
        'number': pair['number'],
      });

      // Update lastIndex
      lastIndex = regionEnd;
    }

    // Add any remaining text
    if (lastIndex < normalizedText.length) {
      segments.add({
        'type': 'text',
        'content': normalizedText.substring(lastIndex),
      });
    }

    // Debug the segments
    if (debugMode) {
      print("Segments:");
      for (var segment in segments) {
        print(
            "${segment['type']}: ${segment['type'] == 'text' ? segment['content'] : segment['number']}");
      }
    }

    // Build the UI
    return Container(
      padding: EdgeInsetsDirectional.only(
        top: paddingTop,
        start: paddingStart,
        bottom: paddingBottom,
        end: paddingEnd,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Flexible(
            child: Wrap(
              alignment: textAlign == TextAlign.center
                  ? WrapAlignment.center
                  : (textAlign == TextAlign.end
                      ? WrapAlignment.end
                      : WrapAlignment.start),
              children: [
                for (final segment in segments)
                  segment['type'] == 'text'
                      ? Text(
                          segment['content'],
                          style: baseStyle,
                          textDirection: effectiveTextDirection,
                        )
                      : Builder(
                          builder: (context) {
                            return Directionality(
                              textDirection: TextDirection.ltr,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _buildCurrencyIcon(
                                      context, baseStyle.color ?? Colors.black),
                                  SizedBox(width: 2.w),
                                  Text(
                                    segment['number'],
                                    style: baseStyle,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
              ],
            ),
          ),
          if (isRequired)
            Text(
              '*',
              style: TextStyle(
                fontSize: 12.sp,
                color: context.theme.primaryColor,
              ),
            ),
        ],
      ),
    );
  }
}
