import 'package:flutter/material.dart';
import 'package:thrivve/core/widget/direction_aware_builder.dart';
import 'package:thrivve/core/widget/flip_conditional.dart';

class FlipIfLTR extends StatelessWidget {
  const FlipIfLTR({
    Key? key,
    required this.builder,
  }) : super(key: key);
  final Widget Function(BuildContext context) builder;
  @override
  Widget build(BuildContext context) {
    return DirectionAwareBuilder(
      builder: (context, textDirection) {
        return FlipConditional(
          builder: builder,
          condition: (_) => textDirection == TextDirection.ltr,
        );
      },
    );
  }
}
