import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../features/onboarding/presentation/widgets/circle_avatar_widget.dart';
import '../../features/onboarding/presentation/widgets/top_bar_widget.dart';
import 'border_button.dart';
import 'button.dart';

class ContainerGeneralBottomSheetWidget extends StatelessWidget {
  final String? title;
  final String? icon;
  final String? description;
  final String? firstBtnText;
  final String? secondBtnText;
  final String? thirdBtnText;
  final Function()? firstBtnOnClick;
  final Function()? secondBtnOnClick;
  final Function()? thirdBtnOnClick;
  final Function()? onCloseClick;
  Color? buttonColor;
  Color? iconBackgroundColor;
  Color? iconColor;
  final double? imageBackgroundWidth;
  final double? imageBackgroundHeight;
  final double? imageWidth;
  final double? imageHeight;
  final bool showCloseBtn;
  final bool isLoading;
  final bool swapButtons; // New flag to swap first and second buttons
  final TextDirection? descTextDirection;
  final Widget? iconWidget;
  final bool isSvg;
  ContainerGeneralBottomSheetWidget({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.firstBtnText,
    required this.secondBtnText,
    required this.firstBtnOnClick,
    required this.secondBtnOnClick,
    this.thirdBtnText,
    this.iconWidget,
    this.thirdBtnOnClick,
    this.buttonColor,
    this.imageBackgroundWidth,
    this.imageBackgroundHeight,
    this.imageWidth,
    this.imageHeight,
    this.iconColor,
    this.iconBackgroundColor,
    this.showCloseBtn = true,
    this.isLoading = false,
    this.swapButtons = false, // Default is false to maintain original behavior
    this.descTextDirection,
    this.onCloseClick,
    this.isSvg = false,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0.r),
              topRight: Radius.circular(20.0.r),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Visibility(
                  visible: showCloseBtn, child: TopBar(onClick: onCloseClick)),
              if (icon != null || iconWidget != null) ...[
                SizedBox(height: 16.h),
                iconWidget ??
                    CircleAvatarWidget(
                      isSvg: isSvg,
                      image: icon,
                      imageBackgroundWidth: imageBackgroundWidth,
                      imageBackgroundHeight: imageBackgroundHeight,
                      imageWidth: imageWidth,
                      imageHeight: imageHeight,
                      iconColor: iconColor,
                      iconBackgroundColor: iconBackgroundColor,
                    ),
              ],
              SizedBox(height: 28.h),
              Text(
                title ?? "",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: context.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 15.sp,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                description ?? "",
                textAlign: TextAlign.center,
                textDirection: descTextDirection,
                style: TextStyle(
                  color: context.lightBlack,
                  fontWeight: FontWeight.w400,
                  fontSize: 11.sp,
                ),
              ),
              SizedBox(height: 32.h),

              // Button rendering logic based on swapButtons flag
              if (!swapButtons) ...[
                // Original order - first button then second button
                if (firstBtnText != null) ...[
                  Button(
                    widget: isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ))
                        : null,
                    text: firstBtnText ?? "",
                    onTab: isLoading ? () {} : firstBtnOnClick!,
                    enable: true,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                    height: 36.h,
                    buttonColor: buttonColor ?? context.appPrimaryColor,
                  ),
                ],
                if (secondBtnText != null) ...[
                  SizedBox(height: 16.h),
                  BorderButton(
                    text: secondBtnText ?? "",
                    fontWeight: FontWeight.w600,
                    fontSize: 11.sp,
                    onTab: secondBtnOnClick!,
                  ),
                ],
              ] else ...[
                // Swapped order - second button then first button
                if (secondBtnText != null) ...[
                  BorderButton(
                    text: secondBtnText ?? "",
                    fontWeight: FontWeight.w600,
                    fontSize: 11.sp,
                    onTab: secondBtnOnClick!,
                  ),
                ],
                if (firstBtnText != null) ...[
                  SizedBox(height: 16.h),
                  Button(
                    key: ValueKey('yesButton'),
                    widget: isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ))
                        : null,
                    text: firstBtnText ?? "",
                    onTab: isLoading ? () {} : firstBtnOnClick!,
                    enable: true,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                    height: 36.h,
                    buttonColor: buttonColor ?? context.appPrimaryColor,
                  ),
                ],
              ],

              // Third button remains the same regardless of swapButtons value
              if (thirdBtnText != null) ...[
                SizedBox(height: 16.h),
                InkWell(
                  onTap: () {
                    thirdBtnOnClick!();
                  },
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: context!.appPrimaryColor!,
                          width: 1.0,
                        ),
                      ),
                    ),
                    child: Text(
                      thirdBtnText ?? "",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: context.outlineButtonColor,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        height: 0,
                      ),
                    ),
                  ),
                ),
              ],
              SizedBox(height: 32.h),
            ],
          )),
    );
  }
}
