import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BorderedIconButton extends StatelessWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;
  final Color borderColor;
  final bool haveBorder;
  final Color backgroundColor;
  final double borderWidth;
  final double borderRadius;

  const BorderedIconButton({
    Key? key,
    required this.iconPath,
    required this.onPressed,
    this.size = 48.0,
    this.haveBorder = false,
    this.borderColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: haveBorder
            ? Border.all(
                color: borderColor,
                width: borderWidth,
              )
            : null,
        borderRadius: BorderRadius.circular(borderRadius),
        color: backgroundColor,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(borderRadius),
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SvgPicture.asset(
            iconPath,
            width: size,
            height: size,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}

// Example usage:
// BorderedIconButton(
//   iconPath: 'assets/images/back_arrow.png',
//   onPressed: () {
//     Navigator.pop(context);
//   },
//   size: 24.0,
//   borderColor: Colors.grey,
//   borderRadius: 12.0,
// )
