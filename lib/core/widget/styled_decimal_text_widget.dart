import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class StyledDecimalText extends StatelessWidget {
  final String decimalNumber; // Accepts the decimal number as a string.
  final TextStyle? beforeDecimalStyle;
  final TextStyle? afterDecimalStyle;

  const StyledDecimalText({
    super.key,
    required this.decimalNumber,
    this.beforeDecimalStyle,
    this.afterDecimalStyle,
  });

  @override
  Widget build(BuildContext context) {
    // Split the number into parts before and after the decimal point.
    final parts = decimalNumber.split('.');

    return RichText(
      text: TextSpan(
        children: [
          // Style for the part before the decimal point.
          TextSpan(
            text: parts[0],
            style: beforeDecimalStyle ??
                TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w600,
                    color: context.black),
          ),
          // Style for the decimal point and the part after it.
          if (parts.length > 1) // Ensure there's a fractional part.
            TextSpan(
              text: '.${parts[1]}',
              style: afterDecimalStyle ??
                  TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: context.lightBlack,
                  ),
            ),
        ],
      ),
    );
  }
}
