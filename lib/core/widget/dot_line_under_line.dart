import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class DottedUnderlineText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final Color? dotsColor;

  const DottedUnderlineText(
      {required this.text, required this.textStyle, this.dotsColor});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: textStyle.copyWith(
        decoration: TextDecoration.underline,
        decorationStyle: TextDecorationStyle.dotted,
        decorationColor: dotsColor,
        decorationThickness: 2.sp,
        color: dotsColor,
      ),
    );
  }
}

// Custom painter for the dotted line
class DottedLinePainter extends CustomPainter {
  Color? color;
  DottedLinePainter([this.color]);
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color ?? Colors.black
      ..strokeWidth = 1
      ..strokeCap = StrokeCap.round;

    const double dashWidth = 2;
    const double dashSpace = 2;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
