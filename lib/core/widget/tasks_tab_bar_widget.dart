import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class TasksTabBarWidget extends StatefulWidget {
  final Function(bool active) onActive;
  final String? firstTapText;
  final String? secondTapText;

  const TasksTabBarWidget(this.onActive,
      {super.key, this.firstTapText, this.secondTapText});

  @override
  _TasksTabBarWidgetState createState() => _TasksTabBarWidgetState();
}

class _TasksTabBarWidgetState extends State<TasksTabBarWidget> {
  bool activeTabSelected = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.containerColor,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(9),
          color: context.tabBackgroundColor,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: InkWell(
                onTap: () {
                  widget.onActive(true);
                  setState(() {
                    activeTabSelected = true;
                  });
                },
                child: Container(
                    margin: const EdgeInsets.all(2),
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(9),
                        color:
                            activeTabSelected ? context.containerColor : null),
                    child: Text(
                      widget.firstTapText ?? "current".tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: context?.black),
                    )),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () {
                  widget.onActive(false);
                  setState(() {
                    activeTabSelected = false;
                  });
                },
                child: Container(
                    margin: const EdgeInsets.all(2),
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(9),
                      color: activeTabSelected ? null : context?.containerColor,
                    ),
                    child: Text(
                      widget.secondTapText ?? "history".tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: context?.black),
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
