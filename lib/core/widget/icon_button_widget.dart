import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class IconButtonWidget extends StatelessWidget {
  final IconData icon;
  final int size;

  final Function()? onTap;

  const IconButtonWidget(
      {super.key, this.icon = Icons.close, this.size = 24, this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ??
          () {
            Get.back();
          },
      child: Container(
        width: 40.w,
        height: 40.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: context.iconAndBtnBackground,
        ),
        child: Icon(
          icon,
          color: context.black,
          size: size.w,
        ),
      ),
    );
  }
}
