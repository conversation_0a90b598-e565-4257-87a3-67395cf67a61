import 'package:flutter/material.dart';

class FlipConditional extends StatelessWidget {
  const FlipConditional({
    Key? key,
    required this.builder,
    required this.condition,
  }) : super(key: key);
  final Widget Function(BuildContext context) builder;
  final bool Function(BuildContext context) condition;
  @override
  Widget build(BuildContext context) {
    final child = builder(context);
    if (condition(context)) {
      return Transform(
        transform: Matrix4.diagonal3Values(-1.0, 1.0, 1.0),
        alignment: Alignment.center,
        child: child,
      );
    }
    return child;
  }
}
