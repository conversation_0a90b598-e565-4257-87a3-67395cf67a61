import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class MySearchBar extends StatefulWidget {
  final Function(String)? onChange;

  const MySearchBar({super.key, required this.onChange});

  @override
  _MySearchBarState createState() => _MySearchBarState();
}

class _MySearchBarState extends State<MySearchBar> {
  TextEditingController textEditingController = TextEditingController();

  @override
  void dispose() {
    textEditingController.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
      child: TextField(
        style: TextStyle(color: context.black),
        controller: textEditingController,
        onChanged: widget.onChange,
        decoration: InputDecoration(
            fillColor: context.iconAndBtnBackground,
            filled: true,
            border: InputBorder.none,
            prefixIcon: Icon(
              Icons.search,
              size: 24,
              color: context.iconSearchBar!.withOpacity(0.4),
            ),
            hintText: "search".tr,
            hintStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                fontFamily: 'cairo',
                color: context.iconSearchBar!.withOpacity(0.40))),
      ),
    );
  }
}
