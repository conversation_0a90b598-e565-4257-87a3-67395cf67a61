import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/settings/domain/entities/biometric_settings_input.dart';
import 'package:thrivve/features/settings/domain/enum_biometric_type.dart';

class BiometricAuthUtility {
  final LocalAuthentication auth = LocalAuthentication();
  final UserSecureDataSource userSecureDataSource;
  final MainHomeBloc mainHomeBloc;

  BiometricAuthUtility({
    required this.userSecureDataSource,
    required this.mainHomeBloc,
  });

  Future<bool> authenticate() async {
    try {
      return await auth.authenticate(
        localizedReason: 'Please authenticate to continue',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
    } catch (e) {
      return false;
    }
  }

  Future<void> handleBiometricAuth({bool isFaceId = false}) async {
    if (isFaceId) {
      setFaceIdBottomSheet(
        context: Get.context!,
        firstBtnOnClick: () async {
          Get.back();
          await _handleBiometricAuth(isFaceId);
        },
      );
    } else {
      setTouchIdEnableBottomSheet(
        context: Get.context!,
        firstBtnOnClick: () async {
          Get.back();
          await _handleBiometricAuth(isFaceId);
        },
      );
    }
  }

  Future<void> _handleBiometricAuth(bool isFaceId) async {
    final isAuthenticated = await authenticate();
    if (isAuthenticated) {
      mainHomeBloc.add(UpdateBiometricSettingsEvent(
        biometricSettingsInput: BiometricSettingsInput(
          isBiometricEnabled: true,
          biometricType: isFaceId
              ? BioMetricTypeInput.face
              : BioMetricTypeInput.fingerprint,
        ),
      ));

      if (isFaceId) {
        setFaceIdSuccessfullyBottomSheet(context: Get.context!);
      } else {
        touchIdEnableSuccessfullyBottomSheet(context: Get.context!);
      }
    } else {
      mainHomeBloc.add(UpdateBiometricSettingsEvent(
        biometricSettingsInput: BiometricSettingsInput(
          isBiometricEnabled: false,
          biometricType: isFaceId
              ? BioMetricTypeInput.face
              : BioMetricTypeInput.fingerprint,
        ),
      ));
    }
  }

  Future<void> handleDisableBiometric({bool isFaceId = false}) async {
    warningDisableFaceIdOrTouchIdBottomSheet(
      context: Get.context!,
      onYesClick: () async {
        Get.back();
        final isAuthenticated = await authenticate();
        if (isAuthenticated) {
          mainHomeBloc.add(UpdateBiometricSettingsEvent(
            biometricSettingsInput: BiometricSettingsInput(
              isBiometricEnabled: false,
              biometricType: isFaceId
                  ? BioMetricTypeInput.face
                  : BioMetricTypeInput.fingerprint,
            ),
          ));
          await userSecureDataSource.setBioMetricEnabled(false);
          disableFaceIdSuccessfullyBottomSheet(
            isFaceId: isFaceId,
            context: Get.context!,
          );
        }
      },
      onNoClick: () {
        Get.back();
      },
      isFaceId: isFaceId,
    );
  }

  Future<bool> shouldShowBiometricAuth() async {
    final isLogin = await userSecureDataSource.isLogin();
    final isFaceIdEnabled = await userSecureDataSource.isBioMetricEnabled();
    final isSupported = await auth.isDeviceSupported();

    return isLogin && isFaceIdEnabled && isSupported;
  }
}
