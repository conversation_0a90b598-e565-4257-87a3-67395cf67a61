import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/util/static_var.dart';

import '../../features/pin/domain/entities/thrivve_user.dart';

class UserSecureDataSourceImpl implements UserSecureDataSource {
  static const _keyThrivveCamp = "thrivve_camp";
  static const _keyNewLanguage = "new_language";
  static const _keyFavouriteVehicles = "favourite_vehicles";
  static const _countryCode = "country_code";
  static const _keyFCMToken = "fcm_token";
  static const _keyToken = "token";
  static const _keyTempToken = "tempToken";
  static const _keyRefreshToken = "refresh_token";
  static const _keyDeviceFingerprint = "device_fingerprint";
  static const _keyIsFirstTimeInstallApp = "isFirstTimeInstallApp";
  static const valueArLanguage = "ar";
  static const valueEnLanguage = "en";
  static const _keyThrivveUser = "thrive_user";
  static const _keyIsBalanceVisible = "isBalanceVisible";
  static const _keyIsAppleAsLead = "isAppleAsLead";
  static const _keyMobileNumber = "mobile_number";
  static const _keyIsTransactionOnboarding = "isTransactionOnboarding";
  static const _keyIsPaymentMethodOnboarding = "isPaymentMethodOnboarding";
  static const _keyIsWithdrawScreenOnboarding = "isWithdrawScreenOnboarding";
  static const _keyIsProfileOnboarding = "isProfileOnboarding";
  static const _keyLeadId = "lead_id";
  static const _keyDeviceId = "device_Id";
  static const _keytheme = "theme";
  static const _keyVehicleSelections = "vehicle_selections";
  static const _seenInstruction = "_seenInstruction";
  static const _seenApplicationInstruction = "_seenApplicationInstruction";
  static const _keyIsPinEnabled = "is_pin_enabled";
  static const _keyIsFaceIdEnabled = "is_face_id_enabled";
  static const _keyDlParams = "dl_params";

  final FlutterSecureStorage? secureStorage;

  UserSecureDataSourceImpl({required this.secureStorage});

  Future<String?> _getValue(String key) async {
    return await secureStorage?.read(key: key);
  }

  Future<void> _setValue(String key, String? value) async {
    try {
      // First delete the existing value
      await secureStorage?.delete(key: key);
      // Then write the new value
      await secureStorage?.write(key: key, value: value);
    } catch (e) {
      // If there's an error, try to write directly
      await secureStorage?.write(key: key, value: value);
    }
  }

  @override
  Future<bool> getBalanceVisible() async {
    String? isVisible = await _getValue(_keyIsBalanceVisible);
    return isVisible == 'true' || isVisible == null ? true : false;
  }

  @override
  Future<String> getCountryCode() async {
    return await _getValue(_countryCode) ?? "sa";
  }

  @override
  Future<String?> getFcmToken() async {
    return await _getValue(_keyFCMToken);
  }

  @override
  Future<String?> getLanguage() async {
    return await _getValue(_keyNewLanguage);
  }

  @override
  Future<String?> getAccessToken() async {
    return await _getValue(_keyToken);
  }

  @override
  Future<ThrivveUser?> getUserData() async {
    try {
      final bool isUserLogin = await isLogin();
      if (!isUserLogin) return null;
      final String? userData = await _getValue(_keyThrivveUser);
      if (userData == null || userData.isEmpty) {
        return null;
      }
      return ThrivveUser.fromJson(jsonDecode(userData));
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> isLogin() async {
    return await _getValue(_keyToken) != null;
  }

  @override
  Future<void> setBalanceVisible(bool? isVisible) async {
    await _setValue(_keyIsBalanceVisible, isVisible == true ? 'true' : 'false');
  }

  @override
  Future<void> setCountryCode(String? countryCode) async {
    await _setValue(_countryCode, countryCode);
  }

  @override
  Future<void> setFcmToken(String? fcmToken) async {
    await _setValue(_keyFCMToken, fcmToken);
  }

  @override
  Future<void> setLanguage(String? language) async {
    await _setValue(_keyNewLanguage, language);
  }

  @override
  Future<void> setAccessToken(String? token) async {
    await _setValue(_keyToken, token);
  }

  @override
  Future<void> setUserData(ThrivveUser? thrivveUser) async {
    await _setValue(_keyThrivveUser, jsonEncode(thrivveUser?.toJson()));
  }

  @override
  Future<void> clearToken() async {
    await _setValue(_keyToken, null);
    await _setValue(_keyTempToken, null);
  }

  @override
  Future<bool> isFirstTimeInstallApp() async {
    return await _getValue(_keyIsFirstTimeInstallApp) == null;
  }

  @override
  Future<void> setFirstTimeInstallApp() async {
    await _setValue(_keyIsFirstTimeInstallApp, 'false');
  }

  @override
  Future<void> clearAll() async {
    String? language = await _getValue(_keyNewLanguage);
    final darkEnabled = await _getValue(_keytheme);
    await secureStorage?.deleteAll();
    StaticVar.isUserLoggedIn = false;
    await clearToken();
    await _setValue(_keyTempToken, null);
    await _setValue(_keyDeviceFingerprint, null);
    await _setValue(_keyRefreshToken, null);
    await _setValue(_keyIsFirstTimeInstallApp, 'false');
    if (language != null) await _setValue(_keyNewLanguage, language);
    if (darkEnabled != null) await _setValue(_keytheme, darkEnabled);
  }

  @override
  Future<String?> getMobileNumber() async {
    return await _getValue(_keyMobileNumber);
  }

  @override
  Future<void> setMobileNumber(String? mobileNumber) async {
    await _setValue(_keyMobileNumber, mobileNumber);
  }

  @override
  Future<bool> canAppleAsLead() async {
    String? canAppleAsLead = await _getValue(_keyIsAppleAsLead);
    return canAppleAsLead == 'true' || canAppleAsLead == null ? true : false;
  }

  @override
  Future<void> setAppleAsLead(bool? canAppleAsLead) async {
    await _setValue(
        _keyIsAppleAsLead, canAppleAsLead == true ? 'true' : 'false');
  }

  @override
  Future<List<int>> getFavouriteCars() async {
    String? favouriteCars = await _getValue(_keyFavouriteVehicles);
    if (favouriteCars == null || favouriteCars.isEmpty) {
      return [];
    }
    return List<int>.from(jsonDecode(favouriteCars));
  }

  @override
  Future<bool> isFavouriteCar(int carId) async {
    return (await getFavouriteCars()).contains(carId);
  }

  @override
  Future<void> addFavouriteCar(int carId) async {
    List<int> favouriteCars = await getFavouriteCars();
    favouriteCars.add(carId);
    await _setValue(_keyFavouriteVehicles, jsonEncode(favouriteCars));
  }

  @override
  Future<void> removeFavouriteCar(int carId) async {
    List<int> favouriteCars = await getFavouriteCars();
    favouriteCars.remove(carId);
    await _setValue(_keyFavouriteVehicles, jsonEncode(favouriteCars));
  }

  @override
  Future<bool> isPaymentMethodOnboardingForFirstTime() async {
    return await _getValue(_keyIsPaymentMethodOnboarding) == 'true';
  }

  @override
  Future<bool> isTransactionOnboardingForFirstTime() async {
    return await _getValue(_keyIsTransactionOnboarding) == 'true';
  }

  @override
  Future<bool> isWithdrawScreenOnboardingForFirstTime() async {
    return await _getValue(_keyIsWithdrawScreenOnboarding) == 'true';
  }

  @override
  Future<void> setPaymentMethodOnboardingForFirstTimeDone() async {
    await _setValue(_keyIsPaymentMethodOnboarding, 'false');
  }

  @override
  Future<void> setTransactionOnboardingForFirstTimeDone() async {
    await _setValue(_keyIsTransactionOnboarding, 'false');
  }

  @override
  Future<void> setWithdrawScreenOnboardingForFirstTimeDone() async {
    await _setValue(_keyIsWithdrawScreenOnboarding, 'false');
  }

  @override
  Future<bool> isProfileOnboardingForFirstTime() async {
    return await _getValue(_keyIsProfileOnboarding) == 'true';
  }

  @override
  Future<void> setProfileOnboardingForFirstTimeDone() async {
    await _setValue(_keyIsProfileOnboarding, 'false');
  }

  @override
  Future<String?> getLeadId() async {
    return await _getValue(_keyLeadId);
  }

  @override
  Future<void> setLeadId(String? leadId) async {
    await _setValue(_keyLeadId, leadId);
  }

  @override
  Future<bool?> getDarkMode() async {
    String? value = await _getValue(_keytheme);
    if (value == null) {
      return null;
    } else {
      return value == 'true';
    }
  }

  @override
  Future<void> setDarkMode(bool isDark) async {
    await _setValue(_keytheme, isDark.toString());
  }

  @override
  Future<void> remvoveMode() async {
    await _setValue(_keytheme, null);
  }

  @override
  Future<String> getDeviceId() async {
    return await _getValue(_keyDeviceId) ?? 'Unknown';
  }

  @override
  Future<void> setDeviceId(String? deviceId) async {
    await _setValue(_keyDeviceId, deviceId);
  }

  @override
  Future<String> getThrriveCamp() async {
    return await _getValue(_keyThrivveCamp) ?? '';
  }

  @override
  Future<void> setThrriveCamp(String? camp) async {
    await _setValue(_keyThrivveCamp, camp);
  }

  @override
  Future<bool?> isSeenInstruction() async {
    String? value = await _getValue(_seenInstruction);
    if (value == null) {
      return null;
    } else {
      return value == 'true';
    }
  }

  @override
  Future<void> setIsSeenInstruction(bool value) async {
    await _setValue(_seenInstruction, value ? 'true' : 'false');
  }

  @override
  Future<bool?> isSeenInstructionApplication() async {
    String? value = await _getValue(_seenApplicationInstruction);
    if (value == null) {
      return null;
    } else {
      return value == 'true';
    }
  }

  @override
  Future<void> setIsSeenInstructionApplication(bool value) async {
    await _setValue(_seenApplicationInstruction, value ? 'true' : 'false');
  }

  @override
  Future<String?> getRefreshToken() async {
    return await _getValue(_keyRefreshToken);
  }

  @override
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    if (accessToken.isNotEmpty) {
      await _setValue(_keyTempToken, null);
      await _setValue(_keyToken, accessToken);
    }
    if (refreshToken.isNotEmpty) {
      await _setValue(_keyRefreshToken, refreshToken);
    }
  }

  @override
  Future<String?> getDeviceFingerprint() async {
    return await _getValue(_keyDeviceFingerprint);
  }

  @override
  Future<void> setDeviceFingerprint(String? fingerprint) async {
    await _setValue(_keyDeviceFingerprint, fingerprint);
  }

  @override
  Future<bool> isPinEnabled() async {
    return await _getValue(_keyIsPinEnabled) == 'true';
  }

  @override
  Future<void> setPinEnabled(bool enabled) async {
    await _setValue(_keyIsPinEnabled, enabled ? 'true' : 'false');
  }

  @override
  Future<bool> isBioMetricEnabled() async {
    return await _getValue(_keyIsFaceIdEnabled) == 'true';
  }

  @override
  Future<void> setBioMetricEnabled(bool enabled) async {
    await _setValue(_keyIsFaceIdEnabled, enabled ? 'true' : 'false');
  }

  @override
  Future<void> saveVehicleSelections(String selectionsJson) async {
    await _setValue(_keyVehicleSelections, selectionsJson);
  }

  @override
  Future<String?> getVehicleSelections() async {
    return await _getValue(_keyVehicleSelections);
  }

  @override
  Future<void> clearVehicleSelections() async {
    await _setValue(_keyVehicleSelections, null);
  }

  @override
  Future<Map<String, dynamic>?> getDlParams() async {
    final String? paramsJson = await _getValue(_keyDlParams);
    if (paramsJson == null || paramsJson.isEmpty) {
      return null;
    }
    try {
      return jsonDecode(paramsJson) as Map<String, dynamic>;
    } catch (e) {
      print("Error decoding DL params: $e");
      return null;
    }
  }

  @override
  Future<void> setDlParams(Map<String, dynamic>? dlParams) async {
    if (dlParams == null) {
      await _setValue(_keyDlParams, null);
      return;
    }
    try {
      final String paramsJson = jsonEncode(dlParams);
      await _setValue(_keyDlParams, paramsJson);
    } catch (e) {
      print("Error encoding DL params: $e");
    }
  }

  @override
  Future<void> setTempToken(String? token) async {
    await _setValue(_keyTempToken, token);
  }

  @override
  Future<String?> getTempToken() async {
    return await _getValue(_keyTempToken);
  }
}
