import '../../features/pin/domain/entities/thrivve_user.dart';

abstract class UserSecureDataSource {
  Future<bool?> getDarkMode();
  Future<void> setDarkMode(bool isDark);
  Future<void> remvoveMode();
  Future<ThrivveUser?> getUserData();

  Future<String?> getAccessToken();
  Future<String?> getLeadId();
  Future<bool> isPinEnabled();
  Future<void> setPinEnabled(bool enabled);
  Future<bool> isBioMetricEnabled();
  Future<void> setBioMetricEnabled(bool enabled);
  Future<bool> canAppleAsLead();

  Future<bool> getBalanceVisible();

  Future<void> setIsSeenInstruction(bool value);
  Future<bool?> isSeenInstruction();

  Future<void> setIsSeenInstructionApplication(bool value);
  Future<bool?> isSeenInstructionApplication();

  Future<bool> isFirstTimeInstallApp();

  Future<String?> getLanguage();

  Future<String?> getFcmToken();

  Future<String> getCountryCode();

  Future<String?> getMobileNumber();

  Future<void> addFavouriteCar(int carId);

  Future<void> removeFavouriteCar(int carId);

  Future<bool> isLogin();

  // is favourite car
  Future<bool> isFavouriteCar(int carId);

  // get favourite cars
  Future<List<int>> getFavouriteCars();

  // onboarding for withdraw screen
  Future<bool> isWithdrawScreenOnboardingForFirstTime();

  // is payment method onboarding for first time
  Future<bool> isPaymentMethodOnboardingForFirstTime();

  // is transaction onboarding for first time
  Future<bool> isTransactionOnboardingForFirstTime();

  Future<bool> isProfileOnboardingForFirstTime();

  ////////////////////////////////////////////////////////////////////////////

  // set onboarding for withdraw screen
  Future<void> setWithdrawScreenOnboardingForFirstTimeDone();

  Future<void> setProfileOnboardingForFirstTimeDone();

  // set payment method onboarding for first time
  Future<void> setPaymentMethodOnboardingForFirstTimeDone();

  // set transaction onboarding for first time
  Future<void> setTransactionOnboardingForFirstTimeDone();

  Future<void> setUserData(ThrivveUser? thrivveUser);

  Future<void> setMobileNumber(String? mobileNumber);
  Future<void> setLeadId(String? leadId);

  Future<void> setFirstTimeInstallApp();

  Future<void> setAccessToken(String? token);
  Future<void> setTempToken(String? token);
  Future<String?> getTempToken();

  Future<void> setBalanceVisible(bool? isVisible);

  Future<void> setAppleAsLead(bool? canAppleAsLead);

  Future<void> setLanguage(String? language);

  Future<void> setFcmToken(String? fcmToken);

  Future<void> setDeviceId(String? deviceId);
  Future<String> getDeviceId();

  Future<void> setCountryCode(String? countryCode);

  Future<void> setThrriveCamp(String? camp);
  Future<String> getThrriveCamp();

  // Token management
  Future<String?> getRefreshToken();
  Future<void> saveTokens(String accessToken, String refreshToken);

  // Device fingerprint
  Future<String?> getDeviceFingerprint();
  Future<void> setDeviceFingerprint(String? fingerprint);

  Future<void> clearToken();

  Future<void> clearAll();

  // Vehicle selections storage
  Future<void> saveVehicleSelections(String selectionsJson);
  Future<String?> getVehicleSelections();
  Future<void> clearVehicleSelections();

  // DL Params storage
  Future<Map<String, dynamic>?> getDlParams();
  Future<void> setDlParams(Map<String, dynamic>? dlParams);
}
