import 'package:get/get.dart';
import 'package:thrivve/core/ui/fullScreenImage/full_screen_image_view.dart';
import 'package:thrivve/features/payment/binding/payment_binding.dart';
import 'package:thrivve/features/payment/presentation/pages/saved_cards_page.dart';
import 'package:thrivve/features/pin/presentation/pages/change_pincode.dart';
import 'package:thrivve/features/products/presentation/pages/html_privew.dart';
import 'package:thrivve/features/products/presentation/pages/products_page.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/bindings/rent_vehicle_details_bindings.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/pages/vehicle_details_page.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/data/bindings/find_car_binding.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/presentation/pages/find_your_car.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/bindings/rent_application_progress_binding.dart';
import 'package:thrivve/features/rent_a_car/rent_application_progress/presentation/pages/rent_application_progress_page.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/binding/rent_car_checkout_binding.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/pages/rent_calender_page.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/pages/rent_car_checkout_page.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/pages/rent_payment_page.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/pages/rent_personal_details_page.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/pages/rent_successfully_page.dart';
import 'package:thrivve/features/rent_a_car/rent_info_supplier/presentation/pages/rent_info_supplier_page.dart';
import 'package:thrivve/features/rent_a_car/rent_need_help/rent_need_help_binding.dart';
import 'package:thrivve/features/rent_a_car/rent_need_help/rent_need_help_page.dart';
import 'package:thrivve/features/settings/presentation/pages/privacy_and_security_screen.dart';
import 'package:thrivve/features/uber_flow/info_supplier/data/binding/info_supplier_binding.dart';
import 'package:thrivve/features/uber_flow/info_supplier/presentation/pages/info_supplier_page.dart';
import 'package:thrivve/features/uber_flow/instructions_uber_flow/binidng/instructions_uber_flow_binding.dart';
import 'package:thrivve/features/uber_flow/instructions_uber_flow/presentation/pages/instructions_uber_flow_page.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/binding/upload_uper_documents_bindings.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/pages/kyc_page.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/bindings/link_uber_with_thrivve_binding.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/presentation/pages/link_uber_account_page.dart';
import 'package:thrivve/features/uber_flow/progress_step_uber_flow/binding/progress_step_uber_flow_binding.dart';
import 'package:thrivve/features/uber_flow/progress_step_uber_flow/presentation/pages/progress_application_page.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/bindings/review_uper_request_bindings.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/presentation/pages/uper_application_review_page.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/binding/select_vehicles_binding.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/pages/select_vehicles_uber_flow_page.dart';
import 'package:thrivve/features/uber_flow/uber_partner/binding/uber_partner_binding.dart';
import 'package:thrivve/features/uber_flow/uber_partner/presentation/pages/uber_partner_page.dart';
import 'package:thrivve/features/under_processing/presentation/pages/under_processing_page.dart';
import 'package:thrivve/features/vehicleDetails/presentation/pages/vehicle_details_page.dart';

import '../features/bankAccount/presentation/pages/add_bank_account_page.dart';
import '../features/bankAccount/presentation/pages/bank_account_list_page.dart';
import '../features/faqs/presentation/pages/faq_screen.dart';
import '../features/identityInfo/presentation/pages/identity_info_page.dart';
import '../features/listOfVehicles/presentation/pages/list_of_vehicles_page.dart';
import '../features/mainHome/presentation/bindings/main_home_binding.dart';
import '../features/mainHome/presentation/pages/main_home_page.dart';
import '../features/myProductDetails/presentation/pages/my_product_details_screen.dart';
import '../features/notifications/presentation/pages/notification_screen.dart';
import '../features/onboarding/presentation/pages/onboarding_page.dart';
import '../features/personalInfo/presentation/pages/personal_info_page.dart';
import '../features/pin/presentation/pages/pin_page.dart';
import '../features/referral/presentation/pages/referral_screen.dart';
import '../features/settings/presentation/pages/personal_details_screen.dart';
import '../features/settings/presentation/pages/profile_screen.dart';
import '../features/settings/presentation/pages/setting_screen.dart';
import '../features/settings/presentation/pages/update_mobile_screen.dart';
import '../features/splash/presentation/pages/splash_page.dart';
import '../features/topUp/presentation/pages/top_up_add_receipt_page.dart';
import '../features/topUp/presentation/pages/top_up_bank_account_page.dart';
import '../features/topUp/presentation/pages/topup_instructions_page.dart';
import '../features/topUp/presentation/pages/topup_page.dart';
import '../features/transactionDetails/presentation/pages/pending_transaction_details_page.dart';
import '../features/transactionDetails/presentation/pages/transaction_details_screen.dart';
import '../features/transactions/presentation/pages/transactions_page.dart';
import '../features/withdraw/presentation/pages/payment_request_done_page.dart';
import '../features/withdraw/presentation/pages/withdraw_confirmation_page.dart';
import '../features/withdraw/presentation/pages/withdraw_page.dart';

class AppRoutes {
  // static const rentLoginPage = Routes.rentLoginPage;
  static const rentCheckoutPage = Routes.rentCheckoutPage;
  static const rentPersonalDetails = Routes.rentPersonalDetails;
  static const findYourCarPage = Routes.findYourCarPage;
  static const savedCardsPage = Routes.savedCardsPage;
  static const htmlPage = Routes.htmlProductPage;
  static const uberPartnerPage = Routes.uberPartnerPage;
  static const linkUberWithThrivvePage = Routes.linkUberAccountPage;
  static const KYCUberPage = Routes.KYCUberPage;
  static const infoSupplierPage = Routes.infoSupplierPage;
  static const progressApplicationPage = Routes.progressApplicationPage;
  static const selectVehiclesUberFlowPage = Routes.selectVehiclesUberFlowPage;
  static const instructionsUberFlowPage = Routes.instructionsUberFlowPage;
  static const addAccountPage = Routes.addAccountPage;
  static const notification = Routes.notification;
  static const splash = Routes.splash;
  static const onboardingPage = Routes.onboardingPage;
  static const homePage = Routes.homePage;
  static const bankAccountListPage = Routes.bankAccountListPage;
  static const pinScreen = Routes.pinScreen;
  static const personalInfo = Routes.personalInfo;
  static const identityIInfo = Routes.identityIInfo;
  static const driveToOwn = Routes.driveToOwn;
  static const productsPage = Routes.productsPage;
  static const transactionsPage = Routes.transactionsPage;
  static const vehicleDetailsPage = Routes.vehicleDetailsPage;
  static const referralScreen = Routes.referralScreen;
  static const faqScreen = Routes.faqScreen;
  static const fullScreenImageView = Routes.fullScreenImageView;
  static const withdrawPage = Routes.withdrawPage;
  static const profileScreen = Routes.profileScreen;
  static const personalDetailsScreen = Routes.personalDetailsScreen;
  static const withdrawConfirmationScreen = Routes.withdrawConfirmationScreen;
  static const paymentRequestDonePage = Routes.paymentRequestDonePage;
  static const transactionDetailsPage = Routes.transactionDetailsPage;
  static const pendingTransactionDetailsPage =
      Routes.pendingTransactionDetailsPage;
  static const privacyAndSecurity = Routes.privacyAndSecurity;
  static const settingScreen = Routes.settingScreen;
  static const updateMobileScreen = Routes.updateMobileScreen;
  static const topUpInstructionsPage = Routes.topUpInstructionsPage;
  static const topUpPage = Routes.topUpPage;
  static const topUpBankAccountPage = Routes.topUpBankAccountPage;
  static const topUpAddReceiptPage = Routes.topUpAddReceiptPage;
  static const underProcessingPage = Routes.underProcessingPage;
  static const reviewUperApplicationPage = Routes.reviewUperApplicationPage;
  static const rentInfoSupplierPage = Routes.rentInfoSupplierPage;
  static const rentNeedHelpPage = Routes.rentNeedHelpPage;
  static const rentCalendarPage = Routes.rentCalendarPage;
  static const rentPaymentPage = Routes.rentPaymentPage;
  static const rentVehicleDetailsPage = Routes.rentVehicleDetailsPage;
  static const rentApplicationStatusPage = Routes.rentApplicationStatusPage;
  static final routes = [
    /* ✅ */ GetPage(
      name: Routes.findYourCarPage, // no arguments needed
      page: () => FindCarScreen(),
      binding: FindCarBinding(),
    ),
    /* ✅ */ GetPage(
        name: Routes.htmlProductPage, // no arguments needed
        page: () => HtmlPrivew(),
        binding: UberPartnerBinding()),
    /* ✅ */ GetPage(
      name: Routes.underProcessingPage, // no arguments needed
      page: () => UnderProcessingPage(),
    ),
    /* ✅ */ GetPage(
      name: Routes.instructionsUberFlowPage, // no arguments needed
      page: () => InstructionsUberFlowPage(),
      binding: InstructionsUberFlowBinding(),
    ),
    GetPage(
      name: Routes.selectVehiclesUberFlowPage, // no arguments needed
      page: () => SelectVehiclesUberFlowPage(),
      binding: SelectVehiclesBinding(),
    ),
    GetPage(
      name: Routes.progressApplicationPage, // no arguments needed
      page: () => ProgressApplicationPage(),
      binding: ProgressStepUberFlowBinding(),
    ),
    GetPage(
      name: Routes.uberPartnerPage, // no arguments needed
      page: () => UberPartnerPage(),
      binding: UberPartnerBinding(),
    ),
    GetPage(
      name: Routes.savedCardsPage, // no arguments needed
      page: () => SavedCardsPage(),
      fullscreenDialog: true,
      preventDuplicates: false,
      opaque: false,
      binding: PaymentBinding(),
    ),

    GetPage(
      name: Routes.infoSupplierPage, // no arguments needed
      page: () => InfoSupplierPage(),
      binding: InfoSupplierBinding(),
      fullscreenDialog: true,
      preventDuplicates: false,
      opaque: false,
    ),

    /* ✅ */ GetPage(
      name: Routes
          .paymentRequestDonePage, //{message as string,title as string} argument as map
      page: () => PaymentRequestDonePage(),
    ),
    // cant change because touobloc needs TopUp()
    /* ⛔*/ GetPage(
      name:
          Routes.topUpInstructionsPage, //dont include this in the documentation
      page: () => TopUpInstructionsPage(),
    ),
/* ✅ */ GetPage(
        name: Routes.withdrawPage,
        page: () => WithdrawPage()), // // no arguments needed
    /* ✅ */ GetPage(
        name:
            Routes.pendingTransactionDetailsPage, // required argument as string
        page: () => PendingTransactionDetailsScreen()),
    /* ✅ */ GetPage(
        name: Routes.transactionDetailsPage, // required argument as string
        page: () => TransactionDetailsScreen()),
    GetPage(
        name: Routes
            .withdrawConfirmationScreen, //dont include this in the documentation
        page: () => WithdrawConfirmationScreen()),
    /* ✅ */ GetPage(
        name: Routes.bankAccountListPage, // no arguments needed
        page: () => const BankAccountListPage()),
    /* ✅ */ GetPage(
        name: Routes.faqScreen,
        page: () => const FaqScreen()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.personalInfo,
        page: () => PersonalInfoPage()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.listOfCarsPage,
        page: () => ListOfVehiclesPage()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes
            .vehicleDetailsPage, // {carId: int?, productId: string?} args as map
        page: () => const VehicleDetailsPage()),
    /* ✅ */ GetPage(
        name: Routes.identityIInfo,
        page: () => IdentityInfoPage()), //{notificationIdArg:int?} args as map

    /* ⛔ */ GetPage(
        name: Routes.addAccountPage,
        page: () =>
            const AddBankAccountPage()), //{isAddNewAccountArguments:bool,bankNameArguments:PaymentMethod?,bloc:BankAccountBloc} dont include this in the documentation

    /* ✅ */ GetPage(
        name: Routes.pinScreen,
        page: () => const PinPage()), //{from:string?} argument as map
    /* ✅ */ GetPage(
        name: Routes.changePinScreen,
        page: () => const ChangePinPage()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.notification,
        page: () => const NotificationScreen()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.splash,
        page: () => const SplashPage()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.homePage,
        page: () => MainHomePage(),
        binding: MainHomeBinding()), // no arguments needed
    //MainHomeBloc should be created for all app
    /* ⛔ */ GetPage(
        name: Routes.driveToOwn,
        page: () =>
            MyProductDetailsScreen()), //{contractId:string?} dont include in the documentation
    /* ✅ */ GetPage(
        name: Routes.productsPage,
        page: () =>
            ProductsPage()), //{personalInfo:PersonalInfo?,canWithdraw:bool?} argument as map

    /* ✅ */ GetPage(
        name: Routes.onboardingPage,
        page: () => const OnboardingPage()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.transactionsPage,
        page: () =>
            const TransactionsPage()), //{  "currency": String?,"isAmountVisible": bool?} argument as map,

    /* ✅ */ GetPage(
      name: Routes.referralScreen, // no arguments needed
      page: () => const ReferralScreen(showBack: true),
    ),
    /* ✅ */ GetPage(
        name: Routes.fullScreenImageView,
        page: () => FullScreenImageView()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.profileScreen,
        page: () => ProfileScreen()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.personalDetailsScreen, // no arguments needed
        page: () => PersonalDetailsScreen()),
    /* ✅ */ GetPage(
        name: Routes.updateMobileScreen,
        page: () => UpdateMobileScreen()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.privacyAndSecurity,
        page: () => PrivacyAndSecurityScreen()), // no arguments needed
    /* ✅ */ GetPage(
        name: Routes.settingScreen,
        page: () => SettingScreen()), // no arguments needed
    /* ⛔ */ GetPage(
        name: Routes.topUpPage, page: () => TopUpPage()), // no arguments needed
    /* ⛔ */ GetPage(
        name: Routes.topUpBankAccountPage,
        page: () =>
            TopUpBankAccountPage()), //dont include this in the documentation
    /* ⛔ */ GetPage(
        name: Routes.topUpAddReceiptPage, page: () => TopUpAddReceiptPage()),

    /* ⛔ */ GetPage(
        name: Routes.KYCUberPage,
        page: () => KYCPage(),
        binding: KYCBindings()),
    /* ⛔ */ GetPage(
      name: Routes.rentCheckoutPage,
      page: () => RentCarCheckoutPage(),
      binding: RentCarCheckoutBinding(),
    ),
    GetPage(
      name: Routes.rentInfoSupplierPage,
      page: () => RentInfoSupplierPage(),
      binding: InfoSupplierBinding(),
    ),
    GetPage(
      name: Routes.rentNeedHelpPage,
      page: () => RentNeedHelpPage(),
      binding: RentHelpBinding(),
    ),
    GetPage(
      name: Routes.rentApplicationStatusPage,
      page: () => RentApplicationStatusPage(),
      binding: RentApplicationProgressBinding(),
    ),
    GetPage(
      name: Routes.rentCalendarPage,
      page: () => RentCalendarPage(),
      binding: RentCarCheckoutBinding(),
    ),
    GetPage(
      name: Routes.rentSuccessfullyPage,
      page: () => RentSuccessfullyPage(),
      binding: RentCarCheckoutBinding(),
    ),
    GetPage(
      name: Routes.rentPaymentPage,
      page: () => RentPaymentPage(),
      binding: RentCarCheckoutBinding(),
    ),
    GetPage(
      name: Routes.rentPersonalDetails,
      page: () => RentPersonalDetailsPage(),
      binding: RentCarCheckoutBinding(),
    ),
    /* ⛔ */ GetPage(
        name: Routes.reviewUperApplicationPage,
        page: () {
          return UperApplicationReviewPage();
        },
        binding: ReviewUperRequestBinding()),
    GetPage(
        name: Routes.linkUberAccountPage,
        page: () {
          return LinkUberAccountPage();
        },
        binding: LinkUberWiththrivveBinding()),
    GetPage(
        name: Routes.rentVehicleDetailsPage,
        page: () {
          return RentVehicleDetailsPage();
        },
        binding:
            RentVehicleDetailsBinding()), //dont include this in the documentation
  ];
}

class Routes {
  // static const rentLoginPage = '/rentLoginPage';
  static const rentCheckoutPage = '/rentCheckoutPage';
  static const rentPersonalDetails = '/rentPersonalDetails';
  static const rentVehicleDetailsPage = '/vehicleDetails';
  static const findYourCarPage = '/findYourCar';
  static const savedCardsPage = '/savedCardsPage';
  static const infoSupplierPage = '/infoSupplierPage';
  static const progressApplicationPage = '/progressApplicationPage';
  static const uberPartnerPage = '/uberPartnerPage';
  static const htmlProductPage = '/htmlProductPage';
  static const selectVehiclesUberFlowPage = '/selectVehiclesUberFlowPage';
  static const instructionsUberFlowPage = '/instructionsUberFlowPage';
  static const underProcessingPage = '/underProcessingPage';
  static const updateMobileScreen = '/updateMobileScreen';
  static const fullScreenImageView = '/fullScreenImageView';
  static const profileScreen = '/profileScreen';
  static const personalDetailsScreen = '/personalDetailsScreen';
  static const withdrawPage = '/withdraw_page';
  static const homePage = '/home_page';
  static const referralScreen = '/referral_screen';
  static const pinScreen = '/PinScreen';
  static const changePinScreen = '/ChangePinScreen';
  static const notification = '/notification';
  static const addAccountPage = '/addAccountPage';
  static const splash = '/';
  static const bankAccountListPage = '/bank_account_list_page';
  static const identityIInfo = '/identityIInfo';
  static const personalInfo = '/personalInfo';
  static const listOfCarsPage = '/listOfCarsPage';
  static const driveToOwn = '/driveToOwn';
  static const productsPage = '/productsPage';
  static const faqScreen = '/faqs';
  static const withdrawConfirmationScreen = '/withdrawConfirmationScreen';
  static const paymentRequestDonePage = '/paymentRequestDonePage';
  static const pendingTransactionDetailsPage = '/pendingTransactionDetailsPage';
  static const transactionDetailsPage = '/transactionDetailsPage';
  static const onboardingPage = '/onboardingPage';
  static const transactionsPage = '/transactionsPage';
  static const vehicleDetailsPage = '/vehicleDetailsPage';
  static const privacyAndSecurity = '/privacyAndSecurity';
  static const settingScreen = '/settingScreen';
  static const topUpInstructionsPage = '/topUpInstructionsPage';
  static const topUpPage = '/topUpPage';
  static const topUpBankAccountPage = '/topUpBankAccountPage';
  static const topUpAddReceiptPage = '/topUpAddReceiptPage';
  static const KYCUberPage = '/KYCUberPage';
  static const reviewUperApplicationPage = '/reviewUperApplicationPage';
  static const linkUberAccountPage = '/linkUberApplicationPage';
  static const rentInfoSupplierPage = '/rentInfoSupplierPage';
  static const rentNeedHelpPage = '/rentNeedHelpPage';
  static const rentApplicationStatusPage = '/rentApplicationStatusPage';
  static const rentCalendarPage = '/rentCalendarPage';
  static const rentSuccessfullyPage = '/rentSuccessfullyPage';
  static const rentPaymentPage = '/rentPaymentPage';
  static final Set<String> allRoutes = {
    underProcessingPage,
    updateMobileScreen,
    fullScreenImageView,
    profileScreen,
    personalDetailsScreen,
    withdrawPage,
    homePage,
    referralScreen,
    pinScreen,
    changePinScreen,
    notification,
    addAccountPage,
    splash,
    bankAccountListPage,
    identityIInfo,
    personalInfo,
    listOfCarsPage,
    driveToOwn,
    productsPage,
    faqScreen,
    withdrawConfirmationScreen,
    paymentRequestDonePage,
    pendingTransactionDetailsPage,
    transactionDetailsPage,
    onboardingPage,
    transactionsPage,
    vehicleDetailsPage,
    privacyAndSecurity,
    settingScreen,
    topUpInstructionsPage,
    topUpPage,
    topUpBankAccountPage,
    topUpAddReceiptPage,
    KYCUberPage,
    progressApplicationPage,
    rentInfoSupplierPage,
    rentNeedHelpPage,
    rentApplicationStatusPage,
    rentCalendarPage,
    rentSuccessfullyPage,
    rentPaymentPage,
    rentPersonalDetails
  };

  // Function to check if a route exists
  static bool routeExists(String routeName) {
    return allRoutes.contains(routeName);
  }
}
