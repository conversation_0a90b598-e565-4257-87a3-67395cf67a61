class SentryConstants {
  // Categories
  static const String navigation = 'navigation';
  static const String customization = 'customization';
  static const String checkout = 'checkout';
  static const String payment = 'payment';
  static const String user = 'user';
  static const String error = 'error';

  // Operations
  static const String rentCarFlow = 'rent_car_flow';
  static const String carSelection = 'car_selected';
  static const String reviewCheckout = 'review_checkout';
  static const String uploadLicense = 'upload_license';
  static const String submitPayment = 'submit_payment';

  // Tags
  static const String carId = 'car_id';
  static const String userId = 'user_id';
  static const String screen = 'screen';
  static const String flow = 'flow';

  // Messages
  static const String customizationSkipped = 'Customization skipped';
  static const String checkoutAbandonment = 'Possible checkout abandonment';
  static const String paymentFailed = 'Payment failed';
  static const String invalidDate = 'Invalid date selected';
  static const String missingUpload = 'Missing required upload';
} 
 