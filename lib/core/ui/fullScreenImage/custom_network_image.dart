import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class CustomNetworkImage extends StatelessWidget {
  final String? imageUrl;
  final double? height;
  final double? width;

  const CustomNetworkImage(
      {super.key, required this.imageUrl, this.height, this.width});
  @override
  Widget build(BuildContext context) {
    return imageUrl == null
        ? Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(8),
            ),
          )
        : CachedNetworkImage(
            width: width,
            height: height,
            imageUrl: imageUrl ?? "",
            placeholder: (context, url) => Shimmer.fromColors(
              baseColor: context.borderAddBranch,
              highlightColor: context.appBackgroundColor,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            errorWidget: (context, url, error) => const Icon(Icons.error),
            cacheKey: imageUrl?.split("?").first,
          );
  }
}
