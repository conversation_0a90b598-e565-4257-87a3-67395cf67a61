import 'dart:io';

import 'package:dartz/dartz.dart';

class FileUploadValidator {
  static const int maxSizeInBytes = 10 * 1024 * 1024; // 5MB
  static const List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];

  static Either<String, bool> validateFile(String filePath) {
    final file = File(filePath);
    final extension = filePath.split('.').last.toLowerCase();
    final size = file.lengthSync();

    if (!allowedExtensions.contains(extension)) {
      return Left('supported_formats');
    }

    if (size > maxSizeInBytes) {
      return Left('supported_formats');
    }

    return const Right(true);
  }
}
