// custom_file_upload_widget.dart
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:thrivve/core/binding/init_binding.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/upload_image/file_upload_controller.dart';
import 'package:thrivve/core/upload_image/file_upload_validator.dart';
import 'package:thrivve/features/personalInfo/presentation/widgets/camera_gallery_bottom_sheet.dart';
import 'package:thrivve/generated/assets.dart';

class CustomFileUploadWidget extends StatelessWidget {
  final String controllerId; // Unique identifier for this widget instance
  final double? width;
  final double? height;
  final String title;
  final String description;
  final Function(String?) onFileSelected;
  final VoidCallback onFileDeleted;
  final bool isFile;
  final bool showPreview;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? iconColor;
  final Widget? customWidget;
  final String? initialFileUrl;
  final String? initialFileType;
  final double? borderRadius;
  final bool showDeleteButtonAsOverlay;
  final double? deleteButtonSize;
  final Positioned? deleteButtonPosition;
  final Function(String?)? onFileUploaded;

  CustomFileUploadWidget({
    Key? key,
    required this.controllerId,
    this.width,
    this.height,
    required this.title,
    required this.description,
    required this.onFileSelected,
    required this.onFileDeleted,
    this.customWidget,
    this.backgroundColor,
    this.isFile = false,
    this.showPreview = false,
    this.iconColor,
    this.initialFileUrl,
    this.initialFileType,
    this.borderRadius,
    this.borderColor,
    this.showDeleteButtonAsOverlay = false,
    this.deleteButtonSize,
    this.deleteButtonPosition,
    this.onFileUploaded,
  }) : super(key: key) {
    // Get the factory and create/get controller instance
    final factory = Get.find<FileUploadControllerFactory>();
    factory.create(controllerId);
  }

  void _trackUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'File Upload Widget: $action',
      category: 'file_upload',
      data: {
        'controller_id': controllerId,
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        if (data != null) ...data,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FileUploadController>(tag: controllerId);

    return Obx(() {
      if (controller.status == UploadStatus.success &&
          controller.filePath != null &&
          onFileUploaded != null) {
        Future.microtask(() {
          onFileUploaded!(controller.filePath);
          _trackUserInteraction('upload_success', data: {
            'file_path': controller.filePath,
            'file_type': controller.fileType,
          });
        });
      }

      final showPreview =
          controller.localFilePath != null || initialFileUrl != null;

      return GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
          _trackUserInteraction('upload_widget_clicked', data: {
            'has_initial_file': initialFileUrl != null,
            'is_uploading': controller.status == UploadStatus.loading,
            'show_preview': showPreview,
          });
          _showBottomSheet(context, isFile: isFile);
        },
        child: Container(
          width: width ?? double.infinity,
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor ?? context.containerColor,
            border: Border.all(
                width: 1,
                color: borderColor ?? context.black.withValues(alpha: 0.6)),
            borderRadius: BorderRadius.circular(borderRadius ?? 16.r),
          ),
          child: showPreview
              ? _buildFilePreview(context, controller)
              : customWidget ?? _buildUploadPrompt(context),
        ),
      );
    });
  }

  Widget _buildDeleteButton(
      BuildContext context, FileUploadController controller) {
    if (showDeleteButtonAsOverlay) {
      return deleteButtonPosition ??
          PositionedDirectional(
            top: 5.sp,
            end: 5.sp,
            child: Container(
              height: deleteButtonSize ?? 20.h,
              width: deleteButtonSize ?? 20.w,
              decoration: BoxDecoration(
                color: context.borderLeaseColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: context.whiteColor,
                  width: 2.w,
                ),
              ),
              child: InkWell(
                onTap: () {
                  _trackUserInteraction('delete_button_clicked', data: {
                    'is_uploading': controller.status == UploadStatus.loading,
                    'file_path': controller.filePath ?? initialFileUrl,
                  });
                  if (controller.status == UploadStatus.loading) {
                    controller.cancelUpload();
                  } else {
                    controller.deleteFile();
                  }
                  onFileDeleted.call();
                },
                child: Center(
                  child: Icon(
                    Icons.close,
                    color: context.black,
                    size: 12.w,
                  ),
                ),
              ),
            ),
          );
    }

    return PositionedDirectional(
      top: 5.sp,
      end: 5.sp,
      child: IconButton(
        onPressed: () {
          _trackUserInteraction('delete_button_clicked', data: {
            'is_uploading': controller.status == UploadStatus.loading,
            'file_path': controller.filePath ?? initialFileUrl,
          });
          if (controller.status == UploadStatus.loading) {
            controller.cancelUpload();
          } else {
            controller.deleteFile();
          }
          onFileDeleted.call();
        },
        icon: Icon(
          controller.status == UploadStatus.loading
              ? Icons.close
              : Icons.delete_outline,
          color: Colors.white,
          size: 24.w,
        ),
        style: IconButton.styleFrom(
          backgroundColor: Colors.black45,
          padding: EdgeInsets.all(8.w),
        ),
      ),
    );
  }

  Widget _buildFilePreview(
      BuildContext context, FileUploadController controller) {
    final bool showingPdf = _isPdfFile(controller.fileType ?? initialFileType,
        controller.filePath ?? initialFileUrl);
    final String? displayPath = controller.filePath ?? initialFileUrl;
    final bool isLocalFile = controller.localFilePath != null;

    return Stack(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
            _openFilePreview(context, controller);
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius ?? 16.r),
            child: showingPdf
                ? _buildPdfPreview(context, controller, displayPath)
                : _buildImagePreview(context, controller, isLocalFile),
          ),
        ),
        if (controller.status == UploadStatus.loading)
          _buildUploadingOverlay(context, controller),
        _buildDeleteButton(context, controller),
      ],
    );
  }

  Widget _buildPdfLoadingPlaceholder() {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.picture_as_pdf, size: 48.w, color: Colors.grey),
            SizedBox(height: 8.h),
          ],
        ),
      ),
    );
  }

  Widget _buildPdfPreview(
      BuildContext context, FileUploadController state, String? path) {
    if (state.status == UploadStatus.loading) {
      return Stack(
        children: [
          Center(
            child: SpinKitWave(
              color: context.appPrimaryColor,
              size: 20.0,
              itemCount: 6,
            ),
          ),
          _buildPdfLoadingPlaceholder(),
        ],
      );
    }
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: PDF(
        enableSwipe: false,
        pageSnap: false,
      ).fromUrl(
        path ?? "",
        placeholder: (progress) => Stack(
          children: [
            _buildPdfLoadingPlaceholder(),
            Center(
              child: SpinKitWave(
                color: context.appPrimaryColor,
                size: 20.0,
                itemCount: 6,
              ),
            ),
          ],
        ),
        errorWidget: (error) => _buildErrorWidget(),
      ),
    );
  }

  Widget _buildImagePreview(
      BuildContext context, FileUploadController state, bool isLocalFile) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Opacity(
        opacity: state.status == UploadStatus.loading ? 0.5 : 1.0,
        child: isLocalFile
            ? Image.file(
                File(state.localFilePath!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildErrorWidget(),
              )
            : Image.network(
                initialFileUrl ?? "",
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: SpinKitWave(
                      color: context.appPrimaryColor,
                      size: 20.0,
                      itemCount: 6,
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) =>
                    _buildErrorWidget(),
              ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 32.w),
        ],
      ),
    );
  }

  Widget _buildUploadingOverlay(
      BuildContext context, FileUploadController state) {
    return Container(
      decoration: BoxDecoration(
        // color: context.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(borderRadius ?? 16.r),
      ),
      child: Center(
        child: Stack(
          children: [
            SpinKitWave(
              color: Colors.white,
              size: 20.0,
              itemCount: 6,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadPrompt(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 46.w,
          height: 46.h,
          decoration: BoxDecoration(
            color: context.appBackgroundColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Image.asset(
              Assets.thrivvePhotosUploadImg,
              width: 24.w,
              height: 24.h,
              color: iconColor ?? context.black.withValues(alpha: 0.4),
            ),
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          title,
          style: TextStyle(
            color: context.black.withValues(alpha: 0.4),
            fontSize: 13.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          description,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: context.black.withValues(alpha: 0.6),
            fontSize: 11.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  void _showBottomSheet(BuildContext context, {bool isFile = false}) {
    _trackUserInteraction('showing_bottom_sheet', data: {
      'is_file': isFile,
      'has_initial_file': initialFileUrl != null,
    });

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => CameraGalleryFileBottomSheet(
        onFileSelected: (String? path) {
          if (path != null) {
            _trackUserInteraction('file_selected', data: {
              'file_path': path,
              'is_file': isFile,
            });
            onFileSelected(path);
          }
        },
        isFile: isFile,
      ),
    );
  }

  void _openFilePreview(BuildContext context, FileUploadController state) {
    if (showPreview) {
      _showBottomSheet(context, isFile: isFile);
      return;
    }
    if (state.status != UploadStatus.loading) {
      final bool isPdf = state.fileType?.toLowerCase() == 'pdf' ||
          initialFileType?.toLowerCase() == 'pdf';
      final String? path = state.filePath ?? initialFileUrl;
      final bool isLocalFile = state.localFilePath != null;

      _trackUserInteraction('file_preview_opened', data: {
        'is_pdf': isPdf,
        'is_local_file': isLocalFile,
        'file_path': path,
      });

      if (isPdf) {
        Get.to(() => Scaffold(
              appBar: AppBar(
                title: Text('pdf_preview'.tr),
                leading: IconButton(
                  icon: Icon(Icons.arrow_back),
                  onPressed: () {
                    _trackUserInteraction('pdf_preview_closed');
                    Get.back();
                  },
                ),
              ),
              body: PDF(
                enableSwipe: true,
                swipeHorizontal: true,
                autoSpacing: false,
                pageFling: false,
                backgroundColor: Colors.grey,
              ).fromUrl(
                path ?? "",
                placeholder: (progress) => Center(
                  child: SpinKitWave(
                    color: context.appPrimaryColor,
                    size: 20.0,
                    itemCount: 6,
                  ),
                ),
                errorWidget: (error) {
                  _trackUserInteraction('pdf_preview_error', data: {
                    'error': error.toString(),
                  });
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline,
                            color: Colors.red, size: 32.w),
                        SizedBox(height: 8.h),
                        Text(error.toString()),
                      ],
                    ),
                  );
                },
              ),
            ));
      } else {
        Get.to(
          () => Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.black,
              leading: IconButton(
                icon: Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  _trackUserInteraction('image_preview_closed');
                  Get.back();
                },
              ),
            ),
            body: PhotoView(
              imageProvider: isLocalFile
                  ? FileImage(File(state.localFilePath!))
                  : NetworkImage(path!) as ImageProvider,
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
              backgroundDecoration: BoxDecoration(color: Colors.black),
            ),
          ),
        );
      }
    }
  }

  bool _isPdfFile(String? fileType, String? url) {
    if (fileType?.toLowerCase() == 'pdf') return true;
    if (url == null) return false;

    try {
      // Parse the URL to get the path
      final uri = Uri.parse(url);
      final path = uri.path;

      // Get the last segment of the path (filename)
      final fileName = path.split('/').last;

      // Check the extension
      return fileName.toLowerCase().endsWith('.pdf');
    } catch (e) {
      print('Error parsing URL: $e');
      return false;
    }
  }
}
