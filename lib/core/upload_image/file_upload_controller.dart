import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/upload_image/file_upload_validator.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/features/personalInfo/domain/use_cases/upload_image_use_case.dart';

enum UploadStatus { initial, loading, success, failure }

class FileUploadController extends GetxController {
  final String controllerId; // Unique identifier for each instance
  final UploadImageUseCase uploadImageUseCase;

  // Observable variables
  final _filePath = Rxn<String>();
  final _localFilePath = Rxn<String>();
  final _status = Rx<UploadStatus>(UploadStatus.initial);
  final _errorMessage = Rxn<String>();
  final _uploadProgress = RxDouble(0.0);
  final _isFileSelected = RxBool(false);
  final _fileType = Rxn<String>();
  final _fileSize = Rxn<int>();

  // Getters
  String? get filePath => _filePath.value;
  String? get localFilePath => _localFilePath.value;
  UploadStatus get status => _status.value;
  String? get errorMessage => _errorMessage.value;
  double get uploadProgress => _uploadProgress.value;
  bool get isFileSelected => _isFileSelected.value;
  String? get fileType => _fileType.value;
  int? get fileSize => _fileSize.value;

  CancelToken? _cancelToken;

  FileUploadController({
    required this.uploadImageUseCase,
    required this.controllerId,
  });

  // Factory method to get or create controller instance
  static FileUploadController getInstance({
    required String controllerId,
    required UploadImageUseCase uploadImageUseCase,
  }) {
    if (Get.isRegistered<FileUploadController>(tag: controllerId)) {
      return Get.find<FileUploadController>(tag: controllerId);
    } else {
      return Get.put(
        FileUploadController(
          uploadImageUseCase: uploadImageUseCase,
          controllerId: controllerId,
        ),
        tag: controllerId,
      );
    }
  }

  void cancelUpload() {
    _cancelToken?.cancel();
    resetState();
  }

  void deleteFile() {
    _filePath.value = null;
    _localFilePath.value = null;
    _status.value = UploadStatus.initial;
    _uploadProgress.value = 0.0;
    _isFileSelected.value = false;
    _fileType.value = null;
    _fileSize.value = null;
  }

  Future<void> uploadFile(String filePath) async {
    final validation = FileUploadValidator.validateFile(filePath);

    if (validation.isLeft()) {
      _status.value = UploadStatus.failure;
      _errorMessage.value = validation.fold((l) => l.tr, (r) => null);
      return;
    }

    _cancelToken = CancelToken();
    final file = File(filePath);

    // Update state for upload start
    _status.value = UploadStatus.loading;
    _localFilePath.value = filePath;
    _uploadProgress.value = 0.0;
    _isFileSelected.value = true;
    _fileType.value = filePath.split('.').last;
    _fileSize.value = file.lengthSync();

    final result = await uploadImageUseCase(
      ImageUploadParams(
        filePath: filePath,
        cancelToken: _cancelToken,
        onProgress: (sent, total) {
          _uploadProgress.value = (sent / total) * 100;
        },
      ),
    );

    if (_cancelToken?.isCancelled ?? false) {
      resetState();
      return;
    }

    result?.fold(
      (failure) {
        _status.value = UploadStatus.failure;
        _errorMessage.value = mapFailureToMessage(failure);
        _uploadProgress.value = 0.0;
      },
      (fileUrl) {
        log(fileUrl ?? 'no file');
        _filePath.value = fileUrl;
        _status.value = UploadStatus.success;
        _uploadProgress.value = 100.0;
      },
    );
  }

  void resetState() {
    // Reset all the Rx variables to their initial states
    _filePath.value = null;
    _localFilePath.value = null;
    _status.value = UploadStatus.initial;
    _errorMessage.value = null;
    _uploadProgress.value = 0.0;
    _isFileSelected.value = false;
    _fileType.value = null;
    _fileSize.value = null;

    // Cancel any ongoing upload
    _cancelToken?.cancel();
    _cancelToken = null;
  }

  // Reset specific parts if needed
  void resetProgress() {
    _uploadProgress.value = 0.0;
  }

  void resetError() {
    _errorMessage.value = null;
  }

  void resetFileSelection() {
    _isFileSelected.value = false;
    _localFilePath.value = null;
    _fileType.value = null;
    _fileSize.value = null;
  }

  @override
  void onClose() {
    _cancelToken?.cancel();
    super.onClose();
  }
}
