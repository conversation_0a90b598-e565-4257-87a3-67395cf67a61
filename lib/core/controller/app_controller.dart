import 'package:get/get.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/logout_use_case.dart';

class AppController extends GetxController {
  final LogoutUseCase logoutUseCase;
  final IPageLoadingDialog iPageLoadingDialog;
  final UserSecureDataSource userSecureDataSource;

  AppController({
    required this.logoutUseCase,
    required this.iPageLoadingDialog,
    required this.userSecureDataSource,
  });

  Future<void> _trackUserInteraction(String action,
      {Map<String, dynamic>? data}) async {
    SentryService.instance.addBreadcrumb(
      message: 'App Controller: $action',
      category: 'app_controller',
      data: {
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        'scope': 'app_controller',
        if (data != null) ...data,
      },
    );
  }

  Future<void> logOut() async {
    _trackUserInteraction('logout_initiated');

    // LogoutUseCase
    final loader = iPageLoadingDialog.showLoadingDialog();
    final logoutUseCaseData = await logoutUseCase(NoParams());
    loader.hide();

    logoutUseCaseData?.fold((failure) {
      _trackUserInteraction('logout_failed', data: {
        'error': mapFailureToMessage(failure),
      });
      final error = mapFailureToMessage(failure);
      errorSnackBar(context: Get.context!, title: error);
    }, (data) {
      _trackUserInteraction('logout_successful');
      logout(userSecureDataSource);
    });
  }
}
