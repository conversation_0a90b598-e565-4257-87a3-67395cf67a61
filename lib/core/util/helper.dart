import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/controller/language_controller.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/util/static_var.dart';
import 'package:thrivve/core/widget/border_button.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/dashboard/data/models/work_with_uber_all_data_model.dart';
import 'package:thrivve/features/dashboard/domain/entities/message_entity.dart';
import 'package:thrivve/features/dashboard/domain/enum/application_type_enum.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:thrivve/features/dashboard/presentation/pages/my_web_view_widget_2.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/main_app_bar_widget.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/user_icon_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';
import 'package:thrivve/features/pin/domain/entities/thrivve_user.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/bindings/rent_vehicle_details_bindings.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/request_params_review_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/manager/vehicle_details_controller.dart';
import 'package:thrivve/features/topUp/domain/entities/top_up.dart';
import 'package:thrivve/features/uber_flow/uber_partner/presentation/arguments/uber_partner_arguments.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../features/dashboard/domain/entities/product.dart';
import '../../features/personalInfo/presentation/widgets/camera_gallery_bottom_sheet.dart';
import '../../features/products/presentation/widgets/list_of_products_vertical_widget.dart';
import '../../generated/assets.dart';
import '../app_routes.dart';
import '../localDataSource/user_secure_data_source.dart';
import '../widget/container_general_bottom_sheet_widget.dart';
import 'analytics_actions.dart';
import 'const.dart';

Future<void> launchURL(
    {required String url, LaunchMode mode = LaunchMode.platformDefault}) async {
  try {
    await launchUrl(Uri.parse(url), mode: mode);
  } catch (e) {
    throw Exception('Could not launch url , ${e.toString()}');
  }
}

// check the platform is android or ios then return update link from const
String getUpdateLink() {
  return Platform.isAndroid ? playStoreUrl : appStoreUrl;
}

String getSound(String? sound) {
  const defaultSound = 'default_sound';
  if (sound == null || sound.trim().isEmpty) {
    return defaultSound;
  }
  final normalizedSound = sound.toLowerCase();
  if (normalizedSound == 'none' || normalizedSound == 'default') {
    return defaultSound;
  }
  return normalizedSound;
}

showLoaderDialog(BuildContext context) {
  Get.dialog(
      Center(
        child: Container(
          padding: EdgeInsets.all(15),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.sp),
              color: context.bottomsheetColor),
          height: 50.sp,
          width: 50.sp,
          child: CircularProgressIndicator(
            color: context.outlineButtonColor,
            strokeWidth: 3,
          ),
        ),
      ),
      barrierDismissible: false);
}

dismissLoaderDialog(BuildContext context) {
  Navigator.of(context).pop();
}

showDatePickerForIos(context, onTimeChange) {
  showCupertinoModalPopup(
      context: context,
      builder: (BuildContext builder) {
        return Container(
          height: MediaQuery.of(context).copyWith().size.height * 0.25,
          color: context.containerColor,
          child: CupertinoDatePicker(
            mode: CupertinoDatePickerMode.date,
            onDateTimeChanged: onTimeChange,
            initialDateTime: DateTime.now(),
            minimumYear: DateTime.now().year,
            maximumYear: DateTime.now().year + 100,
          ),
        );
      });
}

showDatePickerForIosFilterPreviousDates(context, onTimeChange) {
  showCupertinoModalPopup(
      context: context,
      builder: (BuildContext builder) {
        return Container(
          height: MediaQuery.of(context).copyWith().size.height * 0.25,
          color: context.containerColor,
          child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.date,
              onDateTimeChanged: onTimeChange,
              initialDateTime: DateTime.now(),
              minimumYear: DateTime(1950, 1, 1).year,
              maximumYear: DateTime.now().year),
        );
      });
}

String getPlatForm() {
  return Platform.isAndroid ? "Android" : "iOS";
}

Future<String> getPlatFormVersion() async {
  if (Platform.isAndroid) {
    var androidInfo = await DeviceInfoPlugin().androidInfo;
    var sdkInt = androidInfo.version.sdkInt;
    return sdkInt.toString();
  } else {
    var iosInfo = await DeviceInfoPlugin().iosInfo;

    return iosInfo.systemVersion;
  }
}

String? validateName(String? name) {
  // Invalid name. (An example for a valid name : Mohammad)// allow to the arabic name expt numbers in arabic
  final nameRegex = RegExp(r'^[a-zA-Z\u0600-\u06FF\s]+$');

  if (name == null || name.isEmpty) {
    return "name_required".tr;
  } else if (!nameRegex.hasMatch(name)) {
    return "invalid_name".tr;
  }
  return null; // Null means the name is valid
}

// error snackbar
void errorSnackBar({
  String? title,
  String? message,
  Duration? duration,
  VoidCallback? onTap,
  Color messageColor = Colors.red,
  Color colorTitle = Colors.red,
  required BuildContext context,
}) {
  Get.snackbar(
    "GeeksforGeeks",
    "Hello everyone",
    snackPosition: SnackPosition.TOP,
    isDismissible: true,
    onTap: (_) {
      onTap?.call();
      Get.closeCurrentSnackbar();
    },
    titleText: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
                width: 35.w,
                height: 35.h,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(90.r),
                ),
                child: Center(
                    child: Icon(
                  Icons.notifications_active,
                  color: context.whiteColor,
                  size: 18.w,
                ))),
            const SizedBox(
              width: 7,
            ),
            Text(
              title ?? "err".tr,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: colorTitle,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
        InkWell(
          child: Icon(
            Icons.close,
            color: CupertinoColors.systemGrey3,
            size: 18.sp,
          ),
          onTap: () => Get.closeCurrentSnackbar(),
        )
      ],
    ),
    messageText: Text(
      message ?? '',
      style: TextStyle(
        color: messageColor,
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
      ),
    ),
    backgroundColor: context.containerColor,
    borderWidth: 1.w,
    borderColor: Colors.grey[300],
    borderRadius: 4.r,
    duration: duration ?? const Duration(seconds: 3),
    padding: EdgeInsets.all(8.w),
    margin: EdgeInsets.all(16.w),
  );
}

void showSuccessSnackBar(String title, String message) {
  Get.snackbar(
    title, // Title of the Snackbar
    message, // Message content
    snackPosition: SnackPosition.TOP, // Position (TOP or BOTTOM)
    backgroundColor: Colors.green.shade600, // Success-themed background color
    colorText: Colors.white, // Text color
    icon: const Icon(
      Icons.check_circle,
      color: Colors.white,
      size: 30,
    ), // Success icon
    borderRadius: 10, // Rounded corners
    margin: const EdgeInsets.all(15), // Margin from screen edges
    padding: const EdgeInsets.symmetric(
        horizontal: 20, vertical: 15), // Inner padding
    duration: const Duration(seconds: 3), // Display duration
    isDismissible: true, // Allow user to dismiss
    forwardAnimationCurve: Curves.easeOutBack, // Smooth animation
    reverseAnimationCurve: Curves.easeIn, // Smooth dismiss animation
    titleText: title.isNotEmpty
        ? Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          )
        : null,
    messageText: Text(
      message,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 14,
      ),
    ),
    boxShadows: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        spreadRadius: 2,
        blurRadius: 5,
        offset: const Offset(0, 3), // Shadow position
      ),
    ],
  );
}

void customInfoSnackBar({
  title,
  message,
  duration,
  onTap,
  Color? messageColor = Colors.blueGrey,
  Color? colorTitle,
}) {
  Get.snackbar("GeeksforGeeks", "Hello everyone",
      snackPosition: SnackPosition.TOP, isDismissible: true, onTap: (_) {
    onTap();
    Get.closeCurrentSnackbar();
  },
      titleText: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(90),
                  ),
                  child: Center(
                      child: Icon(
                    Icons.notifications_active,
                    color: Get.context!.whiteColor,
                    size: 18,
                  ))),
              const SizedBox(
                width: 7,
              ),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: colorTitle,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          InkWell(
            child: const Icon(
              Icons.close,
              color: CupertinoColors.systemGrey3,
              size: 18,
            ),
            onTap: () => Get.closeCurrentSnackbar(),
          )
        ],
      ),
      messageText: Text(
        message,
        style: TextStyle(
          color: messageColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: Get.context!.containerColor,
      borderWidth: 1,
      borderColor: Colors.grey[300],
      borderRadius: 4,
      duration: duration,
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.all(16));
}

void showSelectOptionBottomSheet(
    {required BuildContext context,
    required Function(String) onImageSelected}) {
  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return CameraGalleryFileBottomSheet(
        onFileSelected: (filePath) async {
          if (filePath != null && filePath.isNotEmpty == true) {
            onImageSelected(filePath);
          }
        },
      );
    },
  );
}

Future<T?> showGeneralBottomSheet<T>({
  required BuildContext context,
  String? title,
  String? description,
  String? icon,
  Widget? iconWidget,
  bool isSvg = false,
  String? firstBtnText,
  String? secondBtnText,
  String? thirdBtnText,
  Color? buttonColor,
  Function()? firstBtnOnClick,
  Function()? secondBtnOnClick,
  Function()? thirdBtnOnClick,
  Function()? onCloseClick,
  double? imageBackgroundWidth,
  double? imageBackgroundHeight,
  double? imageWidth,
  double? imageHeight,
  Color? iconColor,
  Color? iconBackgroundColor,
  bool showCloseBtn = true,
  bool swapButtons = false,
  TextDirection? descTextDirection,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    backgroundColor: context.bottomsheetColor,
    builder: (context) {
      return ContainerGeneralBottomSheetWidget(
        iconWidget: iconWidget,
        title: title,
        swapButtons: swapButtons,
        showCloseBtn: showCloseBtn,
        onCloseClick: onCloseClick,
        description: description,
        icon: icon,
        firstBtnText: firstBtnText,
        secondBtnText: secondBtnText,
        firstBtnOnClick: firstBtnOnClick,
        secondBtnOnClick: secondBtnOnClick,
        buttonColor: buttonColor,
        thirdBtnText: thirdBtnText,
        thirdBtnOnClick: thirdBtnOnClick,
        imageBackgroundWidth: imageBackgroundWidth,
        imageBackgroundHeight: imageBackgroundHeight,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
        iconColor: iconColor,
        iconBackgroundColor: iconBackgroundColor,
        descTextDirection: descTextDirection,
        isSvg: isSvg,
      );
    },
  );
}

void setPasscodeBottomSheet({
  required BuildContext context,
  required Function() onEnableFaceIdClick,
  required Function() onSkipClick,
  required bool isFaceId,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "passcode_set_successfully".tr,
    description: isFaceId
        ? "passcode_set_successfully_desc".tr
        : "touch_id_passcode_set_successfully_desc".tr,
    firstBtnText: isFaceId ? "yes_enable_face_id".tr : "yes_enable_touch_id".tr,
    secondBtnText: isFaceId ? "no_enable_face_id".tr : "no_enable_touch_id".tr,
    icon: Assets.thrivvePhotosSuccess,
    firstBtnOnClick: () {
      onEnableFaceIdClick();
    },
    secondBtnOnClick: () {
      onSkipClick();
    },
  );
}

void warningDisableFaceIdOrTouchIdBottomSheet({
  required BuildContext context,
  required Function() onYesClick,
  required Function() onNoClick,
  required bool isFaceId,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "are_you_sure".tr,
    description:
        isFaceId ? "title_disable_face_id".tr : "title_disable_touch_id".tr,
    firstBtnText: isFaceId ? "no_disable_face_id".tr : "no_disable_touch_id".tr,
    secondBtnText:
        isFaceId ? "yes_disable_face_id".tr : "yes_disable_touch_id".tr,
    icon: Assets.thrivvePhotosSuccess,
    buttonColor: context.colorButton1D2B53,
    swapButtons: true,
    firstBtnOnClick: () {
      onNoClick();
    },
    secondBtnOnClick: () {
      onYesClick();
    },
  );
}

void disableFaceIdSuccessfullyBottomSheet({
  required BuildContext context,
  required bool isFaceId,
}) {
  showGeneralBottomSheet(
    context: context,
    title:
        isFaceId ? "success_disable_face_id".tr : "success_disable_touch_id".tr,
    description: "note_disable_face_id".tr,
    firstBtnText: "got_it".tr,
    iconWidget: SvgPicture.asset(
      isFaceId
          ? Assets.thrivvePhotosDisabledFaceIdImage
          : Assets.thrivvePhotosDisabledFingerPrinterImage,
    ),
    firstBtnOnClick: () {
      Get.back();
    },
  );
}

void setFaceIdBottomSheet({
  required BuildContext context,
  required Function()? firstBtnOnClick,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "face_id".tr,
    description: "passcode_set_successfully_desc".tr,
    firstBtnText: "yes_enable_face_id".tr,
    secondBtnText: "no_enable_face_id".tr,
    icon: Assets.thrivvePhotosFaceId,
    firstBtnOnClick: () {
      Get.back();
      firstBtnOnClick?.call();
    },
    secondBtnOnClick: () {
      Get.back();
    },
  );
}

void setFaceIdSuccessfullyBottomSheet({
  required BuildContext context,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "face_id_success".tr,
    description: "face_id_success_desc".tr,
    firstBtnText: "got_it".tr,
    icon: Assets.thrivvePhotosSuccess,
    firstBtnOnClick: () {
      Get.back();
    },
  );
}

void setTouchIdEnableBottomSheet({
  required BuildContext context,
  required Function()? firstBtnOnClick,
}) {
  showGeneralBottomSheet(
      context: context,
      title: "touch_id".tr,
      description: "touch_id_desc".tr,
      icon: Assets.thrivvePhotosFingerprint,
      firstBtnText: "yes_enable_touch_id".tr,
      firstBtnOnClick: () {
        Get.back();
        firstBtnOnClick?.call();
      },
      secondBtnText: "no_enable_touch_id".tr,
      secondBtnOnClick: () {
        Get.back();
      });
}

void touchIdEnableSuccessfullyBottomSheet({
  required BuildContext context,
}) {
  showGeneralBottomSheet(
      context: context,
      title: "touch_id_enable_successfully".tr,
      description: "touch_id_enable_successfully_desc".tr,
      icon: Assets.thrivvePhotosSuccess,
      firstBtnText: "got_it".tr,
      firstBtnOnClick: () {
        Get.back();
      });
}

String convertArabicNumbersToEnglish(String input) {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

  String converted = input;
  for (int i = 0; i < arabicNumbers.length; i++) {
    converted = converted.replaceAll(arabicNumbers[i], englishNumbers[i]);
  }

  return converted;
}

void applyAsALeadSuccessfullyBottomSheet({
  required BuildContext context,
  String? message,
}) {
  showGeneralBottomSheet(
      context: context,
      title: "successfully_submitted".tr,
      description: message ?? "successfully_submitted_desc".tr,
      icon: Assets.thrivvePhotosSuccess,
      firstBtnText: "got_it".tr,
      firstBtnOnClick: () {
        Get.back();
      });
}

void needHelpBottomSheet({
  required BuildContext context,
  required Function() chatWithUsClick,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "need_help".tr,
    description: "need_help_message".tr,
    icon: Assets.thrivvePhotosNeedHelp,
    firstBtnText: "send_us_a_message".tr,
    firstBtnOnClick: () async {
      const String recipientEmail = '<EMAIL>';
      final String subject = "support_in_your_app".tr;
      final String body = await getBody();
      sendEmailHelper(recipientEmail, subject, body);
    },
    secondBtnText: "chat_with_us".tr,
    secondBtnOnClick: () {
      chatWithUsClick();
    },
    thirdBtnText: "frequently_asked_questions".tr,
    thirdBtnOnClick: () {
      Navigator.of(context).pushNamed(AppRoutes.faqScreen);
    },
  );
}

void needHelpOnboardingBottomSheet({
  required BuildContext context,
  required Function() chatWithUsClick,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "need_help".tr,
    description: "need_help_message".tr,
    icon: Assets.thrivvePhotosNeedHelp,
    secondBtnText: "send_us_a_message".tr,
    secondBtnOnClick: () async {
      const String recipientEmail = '<EMAIL>';
      final String subject = "support_in_your_app".tr;
      final String body = await getBody();
      sendEmailHelper(recipientEmail, subject, body);
    },
    thirdBtnText: "chat_with_us".tr,
    thirdBtnOnClick: () {
      chatWithUsClick();
    },
    firstBtnText: "frequently_asked_questions".tr,
    firstBtnOnClick: () {
      Navigator.of(context).pushNamed(AppRoutes.faqScreen);
    },
  );
}

Future<String> getBody() async {
  final packageInfo = await PackageInfo.fromPlatform();
  final deviceInfo = DeviceInfoPlugin();
  String deviceDetails;

  if (Platform.isAndroid) {
    var androidInfo = await deviceInfo.androidInfo;
    deviceDetails =
        '| Device: ${androidInfo.model} |\n| Version: ${packageInfo.version} |';
  } else if (Platform.isIOS) {
    var iosInfo = await deviceInfo.iosInfo;
    deviceDetails =
        '| Device: ${iosInfo.name} |\n| Version: ${packageInfo.version} |';
  } else {
    deviceDetails = '| Device: Unknown |\n| Version: ${packageInfo.version} |';
  }

  // add some lines in the beginning of the email body
  deviceDetails = '\n\n\n\n\n\n$deviceDetails\n\n';

  return deviceDetails;
}

Future<String> getDeviceId() async {
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

  try {
    if (Platform.isAndroid) {
      // Android device ID
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      final id = androidInfo.id;
      await getIt<UserSecureDataSource>().setDeviceId(id);
      return id;
    } else if (Platform.isIOS) {
      // iOS device ID
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      final id = iosInfo.identifierForVendor ?? "Unknown";
      await getIt<UserSecureDataSource>().setDeviceId(id);
      return id; // Returns the identifier for vendor
    }
    return "Unknown";
  } catch (e) {
    return "Error";
  }
}

Future<void> sendFeedbackEmail({
  required String username,
  required String profile,
}) async {
  // Gather device information
  final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  final deviceData = await deviceInfo.deviceInfo;
  final deviceName = deviceData.data['model'] ?? "Unknown Device";
  final deviceVersion = deviceData.data['version.release'] ?? "Unknown Version";

  // Gather app information
  final packageInfo = await PackageInfo.fromPlatform();
  final appVersion = packageInfo.version;

  // Compose email body
  final emailBody = '''
| Info for the Thrive team: |
| User: $username |
| Profile: $profile |
| Device: $deviceName, $deviceVersion |
| Application: v$appVersion |
''';

  // Create the mailto URL
  final Uri emailUri = Uri(
    scheme: 'mailto',
    path: '<EMAIL>',
    query: Uri.encodeQueryComponent(
        'subject=Feedback on your app&body=$emailBody'),
  );

  // Launch the email app
  if (await canLaunchUrl(emailUri)) {
    await launchUrl(emailUri);
  } else {
    throw 'Could not open email client.';
  }
}

Future<void> sendEmailHelper(recipientEmail, subject, body) async {
  final Uri emailUri = Uri(
    scheme: 'mailto',
    path: recipientEmail,
    query:
        'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
  );

  if (!await launchUrl(emailUri)) {
    Get.snackbar(
      "err".tr,
      "could_not_send_email".tr,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Get.context!.containerColor,
    );
  }
}

void applyAsALeadUnsuccessfullyBottomSheet({
  required BuildContext context,
  Function()? onLoginClick,
  Function()? onSignUpClick,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "failed".tr,
    description: "leas_submit_unsuccessfully".tr,
    icon: Assets.thrivvePhotosX,
    buttonColor: context.appPrimaryColor,
    firstBtnText: "login".tr,
    firstBtnOnClick: () {
      onLoginClick!();
    },
    secondBtnText: "create_new_account".tr,
    secondBtnOnClick: () {
      onSignUpClick!();
    },
  );
}

void forgetMeBottomSheet({
  required BuildContext context,
  onClick,
}) {
  showGeneralBottomSheet(
    context: context,
    title: "are_you_sure".tr,
    description: "are_you_sure_msj".tr,
    icon: Assets.thrivvePhotosForgetMe,
    firstBtnText: "yes_logout".tr,
    firstBtnOnClick: onClick,
    secondBtnText: "no_logout".tr,
    secondBtnOnClick: () {
      Get.back();
    },
    buttonColor: context.logoutBtnBackground,
  );
}

void logout(UserSecureDataSource? userSecureDataSource) async {
  await userSecureDataSource?.clearAll();
  Get.find<ThemeController>().getTheme();
  Get.offAllNamed(AppRoutes.splash);
}

Future<void> saveUserToLocalStorage(
    ThrivveUser? data, UserSecureDataSource? userSecureDataSource,
    {bool saveTokens = true}) async {
  await userSecureDataSource?.setUserData(data);
  if (saveTokens) {
    StaticVar.isUserLoggedIn = true;
    await userSecureDataSource?.setMobileNumber(data?.mobile);
    await userSecureDataSource?.saveTokens(
        data?.accessToken ?? '', data?.refreshToken ?? '');
    await userSecureDataSource
        ?.setDeviceFingerprint(data?.deviceFingerprint ?? '');
    await userSecureDataSource?.setCountryCode(data?.countryCode ?? "");
    await userSecureDataSource
        ?.setPinEnabled(data?.securitySettings?.hasPinCode ?? false);
    await userSecureDataSource?.setBioMetricEnabled(
        data?.securitySettings?.isBiometricEnabled ?? false);
    configureScope(userSecureDataSource);
  }
}

Future<void> configureScope(UserSecureDataSource? userSecureDataSource) async {
  final isLogin = await userSecureDataSource?.isLogin();

  if ((isLogin ?? false).inverted) return;
  final user = await userSecureDataSource?.getUserData();

  if (user?.customerId == null) return;
  SentryService.instance.configureScope(
    userId: user?.customerId.toString(),
    extras: user?.toSentry(),
  );
}

// this function call in many widget and screen , this used for redirect user when status need to pay
// the user will not enter the value , the value will come from server to pay
void goToTopUpPageToPayDirect(
    BuildContext context, MessageEntity? messageEntity,
    {TopUp? topUp}) {
  final input = TopUp(
    currency: messageEntity?.currency ?? '',
    amountTopUp: messageEntity?.amountTopUp ?? 0,
    applicationId: messageEntity?.applicationId,
    paymentReceivedTime: messageEntity?.paymentReceivedTime ?? '',
    payDirect: true,
    onlinePaymentItemId: messageEntity?.onlinePaymentItemId,
    isInsurancePayment: true,
  );
  getIt<IAnalyticsLogger>()
      .logEvent(AnalyticsActions.topUpAmountEntered, parameters: {
    AnalyticsActions.enteredTypeParams:
        checkTheAmountEntered((messageEntity?.amountTopUp ?? 0).toString(), [])
  });
  Get.toNamed(
    AppRoutes.topUpPage,
    arguments: {
      "topUpObject": input,
    },
  )?.then((value) {
    // for refresh data after top up
    context.read<DashboardBloc>().add(GetWidgetNotificationEvent());
    context.read<DashboardBloc>().add(GetLastProgress());
    context.read<DashboardBloc>().add(GetWidgetNotificationEvent());
    context.read<DashboardBloc>().add(GetListOfTransactionsEvent());
    context.read<DashboardBloc>().add(GetUnderProcessingListEvent());
  });
}

void reinitializeBloc() {
  if (getIt.isRegistered<MainHomeBloc>()) {
    getIt.unregister<MainHomeBloc>();
  }

  getIt.registerLazySingleton(
    () => MainHomeBloc(
      getFlagsUseCase: getIt(),
      getWithdrawBalanceUseCase: getIt(),
      changeLanguageUseCase: getIt(),
      deleteUserAccountUseCase: getIt(),
      getUnSeenNotificationUseCase: getIt(),
      getPersonalInfoUseCase: getIt(),
      getDriveToOwnDataUseCase: getIt(),
      userSecureDataSource: getIt(),
      getSupportUrlUseCase: getIt(),
      getReferralStatusUseCase: getIt(),
      saveUserInfoUseCase: getIt(),
      updateBiometricSettingsUseCase: getIt(),
      infoSupplierUseCase: getIt(),
    ),
  );
}

// format the phone number to be like this +966 55 123 4567
String formatPhoneNumber(String phone) {
  if (phone.isEmpty) {
    return '';
  }
  final formattedPhone = phone.replaceAllMapped(
      RegExp(r'^(\d{1,3})(\d{2})(\d{3})(\d{4})$'),
      (Match m) => '+${m[1]} ${m[2]} ${m[3]} ${m[4]}');
  return formattedPhone;
}

Offset? getWidgetPosition(GlobalKey key) {
  final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
  final position = renderBox?.localToGlobal(Offset.zero);
  return position;
}

Future<bool> openWhatsApp({required String url}) async {
  final trimmedUrl = url.trim();
  final uri = Uri.parse(trimmedUrl);
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri);
    return true;
  } else {
    return false;
  }
}

bool isNewVersionAvailable(String currentVersion, String newVersion) {
  List<int> currentParts = currentVersion.split('.').map(int.parse).toList();
  List<int> newParts = newVersion.split('.').map(int.parse).toList();

  // Compare each part of the version number
  for (int i = 0; i < currentParts.length; i++) {
    if (newParts.length <= i) {
      // If new version has fewer parts, assume it's not newer unless all previous parts match
      return false;
    }
    if (currentParts[i] < newParts[i]) {
      return true;
    } else if (currentParts[i] > newParts[i]) {
      return false;
    }
    // If currentParts[i] == newParts[i], continue to the next part
  }

  // If all parts are equal up to the length of currentParts, check if there are additional parts in newVersion
  return newParts.length > currentParts.length;
}

checkTheAmountEntered(String text, List<num> list) {
  if (list.contains(double.tryParse(text))) {
    return AnalyticsActions.predefinedAmount;
  } else {
    return AnalyticsActions.customAmount;
  }
}

Future<void> navigateTo(
  BuildContext context,
  Product product,
  PersonalInfoEntity? personalInfo,
  bool? canWithdraw, {
  Map<String, dynamic>? dlParams,
  MessageEntity? message,
  WorkWithUberAllDataModel? workWithUber,
}) async {
  getIt<IAnalyticsLogger>().logEvent(AnalyticsActions.browseProductDetails,
      parameters: {
        AnalyticsActions.screenNameParams: AnalyticsActions.dashboard
      });
  // if product status soon show soon button else show the page

  dlParams ??= {};
  dlParams['product_id'] = product.id;

  if (product.isComingSoon == true) {
    cheekSoon(product.applicationName, context);
    return;
  }
  if (product.isHtml == true) {
    Get.toNamed(AppRoutes.htmlPage,
        arguments: UberPartnerArguments(
            dlParams: dlParams,
            goto: product.goto,
            htmlContent: product.htmlContent,
            title: product.title));
    return;
  }
  if (product.applicationName == 'Rent Car') {
    clickStatusProgress(
      context,
      applicationTypeEnum: ApplicationTypeEnum.Rent,
      dlParams: dlParams,
      homePageMessage: message ?? getIt<DashboardBloc>().state.homePageMessage,
      dataWorkWithUber:
          workWithUber ?? getIt<DashboardBloc>().state.dataWorkWithUber,
    );
    return;
  } else if (product.applicationName == 'Work with Uber') {
    Get.toNamed(
      AppRoutes.uberPartnerPage,
      arguments: UberPartnerArguments(
        dlParams: dlParams,
      ),
    );
    return;
  } else if (product.url == null ||
      product.url?.isEmpty == true ||
      product.url!.split('/').length < 2) {
    return;
  }

  List<String> dateParts = product.url?.split("/") ?? [];
  var action = dateParts[0];
  var url = dateParts.sublist(1).join("/");

  switch (action) {
    case "page":
      Get.toNamed("/$url?$productId=${product.id}");
      break;
    case "action":
      switch (url) {
        case "withdraw":
          checkIfCanDoWithdrawOrNot(
            isInstantPaymentIsEnable: canWithdraw,
            context: context,
            personalInfo: personalInfo,
          );
          break;
        case "soon":
          cheekSoon(product.applicationName, context);
          break;
      }
      break;
    case "webview":
      _goToWebView(
        url,
        dlParams: dlParams,
        product: product,
      );
      return;
    default:
      cheekSoon(product.applicationName, context);
      return;
  }
}

Future<void> cheekSoon(String? applicationName, BuildContext context) async {
  if (applicationName == 'Lease To Own') {
    final isLogin = await getIt<UserSecureDataSource>().isLogin();
    if (isLogin) {
      getIt<DashboardBloc>().add(ClickLeaseToOwnSoonEvent());
    }
    showInfoBottomSheeLeaseToOwn(context, "coming_soon_rent".tr,
        "coming_soon_message_rent".tr, "sounds_good");
  } else {
    showInfoBottomSheet(
      context,
      "coming_soon".tr,
      "coming_soon_message".tr,
    );
  }
}

void _goToWebView(
  String url, {
  Map<String, dynamic>? dlParams,
  Product? product,
}) async {
  var token = await getIt<UserSecureDataSource>().getAccessToken();
  var leadId = await getIt<UserSecureDataSource>().getLeadId();
  Get.to(() => MyWebViewWidget2(
        url: url,
        token: token ?? '',
        leadId: leadId ?? '',
        dlParams: dlParams,
        product: product,
      ));
}

void checkIfCanDoWithdrawOrNot({
  required BuildContext context,
  required bool? isInstantPaymentIsEnable,
  PersonalInfoEntity? personalInfo,
}) {
  if (isInstantPaymentIsEnable == true) {
    if (personalInfo?.isCompleteProfile == true) {
      navigateToWithdraw(context);
    } else {
      if (personalInfo?.profileReviewStatus == profileReviewStatusExpired ||
          personalInfo?.profileReviewStatus == profileReviewStatusRejected ||
          personalInfo?.profileReviewStatus == null) {
        showContactDialog(context: context);
      } else if (personalInfo?.profileReviewStatus ==
              profileReviewStatusInReview ||
          personalInfo?.profileReviewStatus == profileReviewStatusPending) {
        userProfileInReviewBottomSheet(context);
      } else {
        navigateToWithdraw(context);
      }
    }
  } else {
    showInfoBottomSheet(
      context,
      "coming_soon".tr,
      "coming_soon_message".tr,
    );
  }
}

void navigateToWithdraw(BuildContext context) {
  Get.toNamed(AppRoutes.withdrawPage)?.then((value) {
    refreshWhenWithdrawSuccess(value, context);
  });
}

void refreshWhenWithdrawSuccess(value, BuildContext context) {
  if (value == true) {
    context.read<MainHomeBloc>().add(const GetBalanceEvent());
    context.read<DashboardBloc>().add(GetListOfTransactionsEvent());
    context.read<DashboardBloc>().add(GetUnderProcessingListEvent());
  }
}

void showCustomBottomSheet(
  BuildContext context, {
  required String title,
  required Widget message,
  String? iconImage,
  Widget? extraWidget,
  String primaryButtonText = "got_it",
  VoidCallback? primaryButtonAction,
  bool isSecondaryButtonVisible = false,
  String secondaryButtonText = "back",
}) {
  showModalBottomSheet(
    isDismissible: false,
    context: context,
    isScrollControlled: true,
    backgroundColor: context.bottomsheetColor,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    builder: (context) {
      return Padding(
        padding: EdgeInsets.all(20.sp),
        child: Column(
          key: ValueKey("bottom_sheet_helper_key"),
          mainAxisSize: MainAxisSize.min,
          children: [
            // Close Button
            Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: context.iconBackgroundColor,
                  ),
                  child: const Icon(Icons.close, size: 25),
                ),
              ),
            ),

            // Icon (if available)
            if (iconImage != null) ...[
              Container(
                padding: EdgeInsets.all(15.sp),
                height: 60.sp,
                width: 60.sp,
                decoration: BoxDecoration(
                    color: context.iconBackgroundColor, shape: BoxShape.circle),
                child: Image.asset(iconImage, color: context.black),
              ),
              const SizedBox(height: 30),
            ],

            // Title
            Text(
              title.tr,
              style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),

            // Message
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.sp),
              child: message,
            ),

            // Extra Widget (if available)
            if (extraWidget != null) ...[
              const SizedBox(height: 10),
              extraWidget,
            ],

            const SizedBox(height: 20),

            // Primary Button
            CustomButton(
              text: primaryButtonText.tr,
              onPressed: () {
                primaryButtonAction?.call();
              },
            ),

            // Secondary Button (if visible)
            if (isSecondaryButtonVisible) ...[
              const SizedBox(height: 10),
              CustomButton(
                colorBorder: context.black,
                colorButton: context.whiteColor,
                text: secondaryButtonText.tr,
                colorText: context.black,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],

            const SizedBox(height: 10),
          ],
        ),
      );
    },
  );
}

reviewRentApplication(ReviewAndCheckoutParams reviewAndCheckoutParams) async {
  RentVehicleDetailsBinding(false).dependencies();
  Get.find<VehicleDetailsController>()
    ..vehicleId = reviewAndCheckoutParams.vehicleId!
    ..selectedPackageEntityId.value = reviewAndCheckoutParams.vehiclePricingId
    ..selectedPackageCommitmentMontEntityId.value =
        reviewAndCheckoutParams.commitmentMonthId
    ..selectedInsuranceEntityId.value = reviewAndCheckoutParams.insuranceId
    ..selectedMilegeEntityId.value = reviewAndCheckoutParams.mileageId
    ..selectedAddsOns.value = reviewAndCheckoutParams.addOnsIds ?? []
    ..reviewAndCheckout();
}

changeStatusBarColor(
    {required Color systemNavigationBarColor,
    required Color statusBarColor,
    Brightness? statusBarIconBrightness}) {
  // SystemChrome.setEnabledSystemUIMode(
  //   SystemUiMode.manual,
  //   overlays: [SystemUiOverlay.top], // Keep status bar, hide navigation bar
  // );
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    systemNavigationBarDividerColor: statusBarColor,
    systemNavigationBarColor: statusBarColor,
    systemNavigationBarIconBrightness: Brightness.dark,
    statusBarColor: statusBarColor, // Android only
    statusBarIconBrightness: statusBarIconBrightness ??
        (Get.find<ThemeController>().isDarkMode()
            ? Brightness.light
            : Brightness.dark),
    // Android only
  ));
}

Color getBackgroundColor(String? color) {
  Color scaffoldBackgroundColor = color == null
      ? Get.context!.whiteColor
      : Color(int.parse(
          color.replaceFirst(
            '#',
            '0xFF',
          ),
        ));
  return scaffoldBackgroundColor;
}

int getProductsRemoteConfig() {
  var remoteConfig = FirebaseRemoteConfig.instance;

  // Set default values

  int x = remoteConfig.getInt('product_flow');
  return x;
}

void showChangeLanguage(
  BuildContext context,
) {
  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return Container(
        decoration: BoxDecoration(
            color: context.whiteColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: 10.w),
                IconButton(
                  padding: EdgeInsetsDirectional.zero,
                  onPressed: () {
                    Get.back();
                  },
                  icon: SvgPicture.asset(
                    Assets.thrivvePhotosIconCloseSoon,
                    width: 16.w,
                    height: 16.w,
                    colorFilter:
                        ColorFilter.mode(context.black, BlendMode.srcIn),
                    key: Key("backIcon"),
                  ),
                )
              ],
            ),
            _divider(4, context),
            SizedBox(height: 40.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomTextWidget(
                  title: 'Language',
                  color: context.black,
                  size: 17,
                  fontFamily: 'NotoSans',
                  paddingStart: 20.w,
                  paddingEnd: 20.w,
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomTextWidget(
                  title: 'اللغة',
                  color: context.black,
                  size: 17,
                  fontFamily: 'NotoSansArabic',
                  paddingStart: 20.w,
                  paddingEnd: 20.w,
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
            SizedBox(height: 40.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomTextWidget(
                  title: 'Choose your preferred language',
                  color: context.black.withValues(alpha: 0.8),
                  size: 12,
                  paddingStart: 20.w,
                  paddingEnd: 20.w,
                  fontFamily: 'NotoSans',
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w400,
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomTextWidget(
                  title: 'اختر اللغة المفضلة لديك',
                  color: context.black.withValues(alpha: 0.8),
                  size: 12,
                  paddingStart: 20.w,
                  paddingEnd: 20.w,
                  fontFamily: 'NotoSansArabic',
                  textAlign: TextAlign.center,
                  fontWeight: FontWeight.w400,
                ),
              ],
            ),
            SizedBox(height: 40.h),
            BorderButton(
              borderColor: context.outlineButtonColor,
              color: Colors.transparent,
              margin: EdgeInsetsDirectional.symmetric(horizontal: 20.w),
              text: "English",
              fontFamily: 'NotoSans',
              fontWeight: FontWeight.w600,
              height: 48.sp,
              fontSize: 13.sp,
              onTab: () {
                if (Get.locale == Locale("en")) {
                  Get.back();
                  return;
                }
                Get.find<LanguageController>().changeLanguage("en");
                getIt<DashboardBloc>().add(GetNewDashboardProductsEvent());

                Get.back();
              },
            ),
            SizedBox(height: 20.h),
            BorderButton(
              borderColor: context.outlineButtonColor,
              color: Colors.transparent,
              margin: EdgeInsetsDirectional.symmetric(horizontal: 20.w),
              text: "اللغة العربية",
              fontFamily: 'NotoSansArabic',
              fontWeight: FontWeight.w600,
              height: 48.sp,
              fontSize: 13.sp,
              onTab: () {
                if (Get.locale == Locale("ar")) {
                  Get.back();
                  return;
                }
                Get.find<LanguageController>().changeLanguage("ar");
                getIt<DashboardBloc>().add(GetNewDashboardProductsEvent());
                Get.back();
              },
            ),
            SizedBox(height: 20.h),
            SizedBox(
                height:
                    MediaQuery.of(context).padding.bottom > 0 ? 20.h : 20.h),
          ],
        ),
      );
    },
  );
}

void showExistAppWarningSheet(
  BuildContext context,
) {
  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return Container(
        decoration: BoxDecoration(
            color: context.whiteColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SizedBox(width: 10.w),
                IconButton(
                  padding: EdgeInsetsDirectional.zero,
                  onPressed: () {
                    Get.back();
                  },
                  icon: SvgPicture.asset(
                    Assets.thrivvePhotosIconCloseSoon,
                    width: 16.w,
                    height: 16.w,
                    colorFilter:
                        ColorFilter.mode(context.black, BlendMode.srcIn),
                    key: Key("backIcon"),
                  ),
                )
              ],
            ),
            _divider(4, context),
            SizedBox(height: 40.h),
            Center(
              child: CustomTextWidget(
                title: 'yo_have_exist_app_title'.tr,
                color: context.black,
                size: 17,
                paddingStart: 20.w,
                paddingEnd: 20.w,
                textAlign: TextAlign.center,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 20.h),
            CustomTextWidget(
              title: 'yo_have_exist_app_desc'.tr,
              color: context.black.withValues(alpha: 0.8),
              size: 12,
              paddingStart: 20.w,
              paddingEnd: 20.w,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w400,
            ),
            SizedBox(height: 40.h),
            CustomButton(
              margin: EdgeInsetsDirectional.symmetric(horizontal: 20.w),
              text: "go_to_main_page".tr,
              onPressed: () {
                reinitializeBloc();
                getIt<MainHomeBloc>().add(InitializeMainHomeEvent());
                Get.offAllNamed(AppRoutes.homePage);
              },
            ),
            SizedBox(height: 20.h),
            BorderButton(
              borderColor: context.outlineButtonColor,
              color: Colors.transparent,
              margin: EdgeInsetsDirectional.symmetric(horizontal: 20.w),
              text: "do_need_help".tr,
              fontWeight: FontWeight.w600,
              height: 48,
              fontSize: 12.sp,
              onTab: () {
                Get.offNamed(AppRoutes.rentNeedHelpPage);
              },
            ),
            SizedBox(height: 20.h),
            SizedBox(
                height:
                    MediaQuery.of(context).padding.bottom > 0 ? 20.h : 20.h),
          ],
        ),
      );
    },
  );
}

Widget _divider(double value, BuildContext context) {
  return Divider(
    thickness: value,
    height: value,
    color: context.black.withValues(alpha: 0.08),
  );
}
