import 'package:flutter/material.dart';

class AppLifecycleHandler {
  static bool _isAppInBackground = false;

  static bool get isAppInBackground => _isAppInBackground;

  void init() {
    WidgetsBinding.instance.addObserver(_AppLifecycleObserver());
  }
}

class _AppLifecycleObserver extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        AppLifecycleHandler._isAppInBackground = false;
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        AppLifecycleHandler._isAppInBackground = true;
        break;
      default:
        break;
    }
  }
} 