class AnalyticsActions {
  // Notifications
  static const clickNotifications = 'Click_Notifications';

  // Promotions
  static const clickEarn200SAR = 'Click_Earn 200 SAR';

  // Product Browsing
  static const browseProducts = 'Browse Products';
  static const browseProductDetails = 'Browse Product Details';
  static const applyForProduct = 'Action_Apply for Product';

  // Profile Interactions
  static const visitProfileClick = 'Visit Profile Click';

  // Financial Features
  static const hideMoneyClick = 'Hide Money Click';
  static const addMoneyClick = 'Add Money Click';
  static const sendMoneyClick = 'Send Money Click';
  static const seeInsights = 'See Insights';

  // Top-Up Actions
  static const topUpButtonClick = 'Top-Up Button Clicked';
  static const topUpAmountEntered = 'Top-Up Amount Entered';
  static const bankDetailsScreenViewed = 'Bank Details Screen Viewed';
  static const receiptUploaded = 'Receipt Uploaded';
  static const topUpRequestSubmitted = 'Top-Up Request Submitted';
  static const topUpQuestionMarkClick = 'Top-Up Question Mark Click';
  static const topUp_checkout = 'Top-Up checkout';

  // Send Money Actions
  static const withdrawAmountEntered = 'Withdraw Amount Entered';

  // Transactions
  static const seeAllTransactionsClick = 'See All Transactions Click';
  static const allTransactionsFilterApplied = 'All Transactions Filter Applied';
  static const transactionDetailsView = 'Transaction Details View';

  // My Products
  static const myProductsClick = 'My Products Click';
  static const browseMyProducts = 'Browse My Products';

  // Invoice
  static const invoicesClick = 'Invoices Click';
  static const viewInvoiceDetail = 'View Invoice Detail';

  // Cards
  static const cardsClick = 'Cards Click';
  static const orderCardClick = 'Order Card Click';

  // Notifications Banner
  static const updateRelease = 'Update the release';
  static const dismissNotification = 'Dismiss the notification';

  // Referral
  static const inviteFriendClick = 'Click_Invite Friend';
  static const referralPageVisit = 'Visit_Referral Page';
  static const shareReferralLinkClick = 'Click_Share Referral Link';

  // helper params for analytics
  static const screenNameParams = 'screenName';
  static const earningParams = 'earning';
  static const enteredTypeParams = 'enteredType';
  static const predefinedAmount = 'Predefined Amount';
  static const customAmount = 'Custom Amount';
  static const type = 'type';
  static const leadId = 'lead_id';
  static const questionTitle = 'question_title';
  // screen names
  static const dashboard = 'Dashboard';
  static const onboarding = 'Onboarding';
  static const productsPage = 'Products Page';
  static const transactionsPage = 'Transactions Page';
  static const bankAccountListPage = 'Bank Account List Page';
  static const addBankAccountPage = 'Add Bank Account Page';
  static const withdrawPage = 'Withdraw Page';

  //Menu Interactions

  // Profile & Settings
  static const changeProfilePhoto = 'Click_Change Profile Photo';
  static const viewPersonalDetails = 'Click_View Personal Details';
  static const viewIdentityInformation = 'Click_View Identity Information';
  static const viewSettings = 'Click_View Settings';
  static const viewBankAccounts = 'Click_View Bank Accounts';
  static const viewSavedCards = 'click_view_saved_cards';

  // Payment Methods
  static const addPaymentMethod = 'Click_Add Payment Method';
  static const submitNewPaymentMethod = 'Action_Submit New Payment Method';
  static const deletePaymentMethod = 'Action_Delete Payment Method';

  // Account Management
  static const deleteMyAccount = 'Click_Delete My Account';
  static const submitDeleteAccountRequest =
      'Action_Submit Delete Account Request';

  // Privacy & Security
  static const privacyAndSecurity = 'Click_Privacy and Security';
  static const changePhoneNumber = 'Click_Change Phone Number';
  static const phoneNumberChanged = 'Action_Phone number changed';
  static const viewPrivacyPolicy = 'View_Privacy Policy';
  static const viewTermsAndConditions = 'View_Terms and Conditions';

  // Help & Support
  static const requestSupport = 'Click_Request Support';
  static const helpCenter = 'Click_Help Center';
  static const searchFAQs = 'Action_Search_FAQs';
  static const viewFAQDetails = 'View_FAQ Details';
  static const inviteFriend = 'Click_Invite a Friend';
  static const changeLanguage = 'Click_Change Language';
  static const logout = 'Click_Logout';

  // uber application actions
  static const loginCompleted = 'Login_Completed';
  static const otpEntered = 'OTP_Entered';
  static const pinEntered = 'PIN_Entered';
  // uber application parameters
  static const userID = 'user_ID';
  static const mobile = 'mobile';
}
