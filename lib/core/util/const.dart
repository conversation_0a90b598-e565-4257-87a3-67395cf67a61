const String breakdownChartPeriodDay = "day";
const String breakdownChartPeriodWeek = "week";
const String breakdownChartPeriodMonth = "month";
const String installmentsDueDateStatusNum = "overdue";
const int saudiArabiaCountyId = 195;
const int backImageType = 2;
const int frontImageType = 1;
const String dataSubmitRejectedStatus = "Rejected";
const String dataSubmitApprovedStatus = "Approved";
const String dataSubmitPendingStatus = "Pending";
const String withdrawTransactionsPendingStatus = "Pending";
const String withdrawTransactionsCanceledStatus = "Canceled";
const String withdrawTransactionsPendingStatusAr = "قيد التنفيذ";
const String withdrawTransactionsRejectedStatus = "Rejected";
const String withdrawTransactionsRejectedStatusAr = "رفضت";
const String withdrawTransactionsApprovedStatus = "Approved";
const String paymentMethodBankType = "Bank Transfer";
const String bankNameArguments = "bankName";
const String isAddNewAccountArguments = "isAddNewAccount";
const String paymentTypeBankTransfer = "Bank Transfer";
const String paymentTypeBankInReview = "In-Review";
const String notificationReviewAction = "Review";
const String profileReviewStatusRejected = "Rejected";
const String profileReviewStatusPending = "Pending";
const String profileReviewStatusInReview = "In Review";
const String profileReviewStatusExpirySoon = "Expiry Soon";
const String profileReviewStatusExpired = "Expired";
const String notificationIdArg = "notificationId";
const String serverFailureMessage = "Server Failure";
const String networkFailureMessage = "Network Failure";
const String carId = 'carId';
const String productId = 'productId';
const String driveWithUsFlag = 'driveWithUs';
const String isPinSetupSuccessfully = 'isPinSetupSuccessfully';
const String valueArLanguage = "ar";
const String valueEnLanguage = "en";
const String numberWhenBeHide = "**.**";
const String valueArabicLanguageForAPI = "Arabic";
const String valueEnglishLanguageForAPI = "English";
const String createPaymentRequestOnboardingKey = "create_payment_request";
const String changePaymentMethodOnboardingKey = "change_payment_method";
const String viewTransactionOnboardingKey = "view_transaction";
// to screen name
// from update_mobile

const String attachmentTypeImages = "Images";
const String attachmentTypeVideo = "Video";
const String attachmentTypeNoAttachement = "No Attachment";

const String appStoreUrl =
    'https://apps.apple.com/tr/app/thrivve-app/id6478132223';
const String playStoreUrl =
    'https://play.google.com/store/apps/details?id=com.app.thrivve.finance.thrivve';
const String forceUpdateKey = "thrivve_force_update";
const String forceLogoutKey = "thrivve_force_logout";

bool isRejected(String? status) {
  return status == withdrawTransactionsRejectedStatus;
}

bool isPending(String? status) {
  return status == withdrawTransactionsPendingStatus;
}

bool isCanceled(String? status) {
  return status == withdrawTransactionsCanceledStatus;
}
