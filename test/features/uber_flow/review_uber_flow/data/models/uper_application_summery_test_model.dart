import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/models/city_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_vehicle_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_vehicle_pricing_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_vehicle_type_model.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/vehicle_models.dart';

class UperApplicationSummeryTestModel extends UperApplicationSummeryModel {
  UperApplicationSummeryTestModel()
      : super(
          hasUberAccount: true,
          vehicleType: UperApplicationSummeryVehicleTypeModel(
              id: 1, textAr: "Test Vehicle", textEn: "Test Vehicle"),
          customerId: 12345,
          identityDocumentUrl: "https://example.com/id.pdf",
          vehiclePricingId: 6789,
          averageWorkingTimeType: "Full-time",
          vehicle: UperApplicationSummeryVehicleModel(),
          uberAccountEmail: "<EMAIL>",
          workVehicle: null,
          vehicleModel: VehicleModel(
            id: 1,
            title: 'dsdsd',
            shouldRentCar: false,
          ),
          dateOfBirth: DateTime(1990, 1, 1),
          drivingLicenceUrl: "https://example.com/license.pdf",
          carRegistrationAttachmentUrl: "https://example.com/registration.pdf",
          captainMobile: "+**********",
          referralId: null,
          customerOwnedCar: true,
          nationality: "Egyptian",
          cityId: 1,
          vehicleId: 101,
          stepName: "Verification",
          captainName: "Test Captain",
          nationalId: "**********1234",
          campaignName: "Test Campaign",
          vehiclePricing: UperApplicationSummeryVehiclePricingModel(
              id: 1, subtitle: "Test Pricing", title: 'Test Pricing'),
          workingTime: "9 AM - 5 PM",
          age: 34,
          uberAccountPhone: "+**********",
          vehicleTypeId: 5,
          city: CityModel(id: "1", name: "Cairo"),
        );
}
