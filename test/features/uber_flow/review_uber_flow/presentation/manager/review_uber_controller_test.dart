import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/submit_response_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/use_cases/get_uper_application_use_case.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/use_cases/submit_uper_application_use_case.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/presentation/manager/submit_uper_application_controller.dart';
import 'package:dartz/dartz.dart';

import '../../data/models/uber_flow_application_data_test_model.dart';
import 'review_uber_controller_test.mocks.dart';

// Generate mocks for the dependencies
@GenerateMocks([SubmitUperApplicationUseCase, GetUberApplicationSummeryUseCase])
void main() {
  late SubmitUperApplicationController controller;
  late MockSubmitUperApplicationUseCase mockSubmitUperApplicationUseCase;
  late MockGetUberApplicationSummeryUseCase
      mockGetUberApplicationSummeryUseCase;

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    // Initialize GetX test environment
    Get.testMode = true;

    // Set up mocks
    mockSubmitUperApplicationUseCase = MockSubmitUperApplicationUseCase();
    mockGetUberApplicationSummeryUseCase =
        MockGetUberApplicationSummeryUseCase();

    // Create controller with mock dependencies
    controller = SubmitUperApplicationController(
        mockSubmitUperApplicationUseCase, mockGetUberApplicationSummeryUseCase);
  });

  tearDown(() {
    Get.reset();
  });

  group('SubmitUperApplicationController Tests', () {
    test('Initial values should be properly set', () {
      // Verify initial values
      expect(controller.captainName.value, '');
      expect(controller.nationality.value, '');
      expect(controller.identification.value, '');
      expect(controller.birthDate.value, '');
      expect(controller.city.value, '');
      expect(controller.deliveryWorkTime.value, '');
      expect(controller.weeklyHoursAverage.value, '');
      expect(controller.howWorkWithUber.value, '');
      expect(controller.idImageUrl.value, '');
      expect(controller.lisenceImageUrl.value, '');
      expect(controller.carDocUrl.value, null);
      expect(controller.customerHascar.value, false);
      expect(controller.acceptConditions.value, false);
      expect(controller.acceptInsurance.value, false);
      expect(controller.acceptThriveLinking.value, false);
      expect(controller.isNextButtonEnabled.value, false);
    });

    test('setAcceptCondition should update acceptConditions value', () {
      controller.setAcceptCondition(true);
      expect(controller.acceptConditions.value, true);

      controller.setAcceptCondition(false);
      expect(controller.acceptConditions.value, false);
    });

    test('setAcceptInsurance should update acceptInsurance value', () {
      controller.setAcceptInsurance(true);
      expect(controller.acceptInsurance.value, true);

      controller.setAcceptInsurance(false);
      expect(controller.acceptInsurance.value, false);
    });

    test('setAcceptThriveLinking should update acceptThriveLinking value', () {
      controller.setAcceptThriveLinking(true);
      expect(controller.acceptThriveLinking.value, true);

      controller.setAcceptThriveLinking(false);
      expect(controller.acceptThriveLinking.value, false);
    });

    test(
        '_validateForm should enable next button when all conditions are met with customer having car',
        () {
      // Setup initial state - customer has car
      controller.onInit();
      controller.customerHascar.value = true;

      // Accept all conditions
      controller.setAcceptCondition(true);
      controller.setAcceptThriveLinking(true);

      expect(controller.isNextButtonEnabled.value, true);
    });

    test(
        '_validateForm should enable next button when all conditions are met without customer having car',
        () {
      controller.onInit();
      controller.customerHascar.value = false;

      // Accept all conditions
      controller.setAcceptCondition(true);
      controller.setAcceptInsurance(true);
      controller.setAcceptThriveLinking(true);

      // Next button should be enabled
      expect(controller.isNextButtonEnabled.value, true);
    });

    test(
        '_validateForm should not enable next button when conditions are partially met',
        () {
      // Only accept some conditions
      controller.customerHascar.value = true;
      controller.setAcceptCondition(true);
      controller.setAcceptThriveLinking(false);

      // Next button should not be enabled
      expect(controller.isNextButtonEnabled.value, false);
    });

    group('_fillFields Tests', () {
      test(
          '_fillFields should correctly populate fields with data from WorkWithUberAllDataModel',
          () {
        // Set up mock locale
        Get.locale = const Locale('en', 'US');

        // Create mock data
        final mockData = WorkWithUberAllDataModelTest();

        // Call the private method through its exposed wrapper for testing
        controller.fillFields(mockData);

        // Verify fields are populated correctly
        expect(controller.captainName.value, 'Test Captain');
        expect(controller.identification.value, '1234567890');
        expect(controller.birthDate.value, '2000-01-01');
        expect(controller.city.value, 'Riyadh'); // English locale
        expect(controller.deliveryWorkTime.value, '3_6_months');
        expect(controller.weeklyHoursAverage.value, 'larg_35');
        expect(controller.nationality.value, 'saudi');
        expect(controller.customerHascar.value, true);
        expect(controller.howWorkWithUber.value, 'by_my_own_car'.tr);
        expect(controller.carDocUrl.value,
            'https://example.com/car_registration.png');
        expect(controller.idImageUrl.value, 'https://example.com/id.png');
        expect(controller.lisenceImageUrl.value,
            'https://example.com/license.png');
      });

      test('_fillFields should handle Arabic locale correctly', () {
        // Set up mock locale for Arabic
        Get.locale = const Locale('ar', 'SA');

        // Create mock data
        final mockData = WorkWithUberAllDataModelTest();

        // Call the private method
        controller.fillFields(mockData);

        // Verify city is in Arabic
        expect(controller.city.value, 'الرياض');
        // Verify how work with uber text is correct for non-car owner
        expect(controller.howWorkWithUber.value, 'by_my_own_car'.tr);
      });
    });

    group('Submit Application Tests', () {
      test(
          'submitUberApplication should show confirmation dialog when form is valid',
          () {
        // TODO: This would require mocking the showCustomBottomSheet function
        // which is outside the scope of this test
      });

      testWidgets('_submitUberApplication should handle success response',
          (tester) async {
        // Arrange: Ensure ScreenUtil is initialized
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(375, 821), // Use your app's design size
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (_, __) => GetMaterialApp(
              home: Scaffold(body: Container()), // Provides Get.context
            ),
          ),
        );
        // Mock successful response

        final submitResponseEntity = SubmitResponseEntity("message", "title");
        when(mockSubmitUperApplicationUseCase.call(any))
            .thenAnswer((_) async => Right(submitResponseEntity));

        // Call the method
        await controller.submitUberApplication();

        // Verify the use case was called
        verify(mockSubmitUperApplicationUseCase.call(any)).called(1);

        // Other assertions would verify dialog dismissal and navigation,
        // but these would require additional mocking of static methods
      });

      testWidgets('_submitUberApplication should handle failure response',
          (tester) async {
        // Arrange: Ensure ScreenUtil is initialized
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(375, 821), // Use your app's design size
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (_, __) => GetMaterialApp(
              home: Scaffold(body: Container()), // Provides Get.context
            ),
          ),
        );
        // Mock failure response
        when(mockSubmitUperApplicationUseCase.call(any))
            .thenAnswer((_) async => Left(ServerFailure()));

        // Call the method
        await controller.submitUberApplication();

        // Verify the use case was called
        verify(mockSubmitUperApplicationUseCase.call(any)).called(1);
        await tester.pump(const Duration(seconds: 3));
        // Other assertions would verify error handling
      });
    });
  });
}
