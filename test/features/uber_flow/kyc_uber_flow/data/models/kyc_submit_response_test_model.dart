import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/models/kyc_submition_response_model.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/models/customer_model.dart';

class KYCSubmitResponseTestModel extends KYCSubmitResponseModel {
  KYCSubmitResponseTestModel()
      : super(
          uberAccountEmail: "<EMAIL>",
          identityDocumentUrl: "https://example.com/identity.jpg",
          status: "approved",
          campaignName: "Uber Onboarding Campaign",
          applicationVersion: 1,
          hasUberAccount: true,
          drivingLicenceUrl: "https://example.com/license.jpg",
          customerOwnedCar: false,
          uberLead: "Uber Lead Test",
          customer: CustomerModel(
            id: 101,
            fullName: "Test Customer",
            mobile: "**********",
          ),
          carRegistrationAttachmentUrl: "https://example.com/car_reg.jpg",
          uberAccountPhone: "+2**********0",
          id: 12345,
          vehicleId: 10,
          uberLeadId: 5001,
        );
}
