import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/pricing_vehicle_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/repositories/i_select_vehicles_repository.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/use_cases/get_list_pricing_vehicles_use_case.dart';

import '../save_vehicle_setup_use_case/save_vehicle_step_usecase_test.mocks.dart';

@GenerateMocks([ISelectVehiclesRepository])
void main() {
  late GetListPricingVehiclesUseCase usecase;
  late MockISelectVehiclesRepository mockRepository;

  setUp(() {
    mockRepository = MockISelectVehiclesRepository();
    usecase = GetListPricingVehiclesUseCase(
      selectVehiclesRepository: mockRepository,
    );
  });

  group('GetListPricingVehiclesUseCase', () {
    final vehicleId = 1;
    final tPricingVehicles = [
      PricingVehicleEntity(
        id: 1,
        title: 'Vehicle 1',
        subtitle: 'Subtitle 1',
        commitmentMonths: 12,
      ),
      PricingVehicleEntity(
        id: 2,
        title: 'Vehicle 2',
        subtitle: 'Subtitle 2',
        commitmentMonths: 24,
      ),
    ];

    test(
      'should get list of pricing vehicles from the repository',
      () async {
        // arrange
        when(mockRepository.getAllPricingVehicles(vehicleId))
            .thenAnswer((_) async => Right(tPricingVehicles));

        // act
        final result = await usecase(vehicleId);

        // assert
        expect(result, Right(tPricingVehicles));
        verify(mockRepository.getAllPricingVehicles(vehicleId));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test(
      'should return server failure when repository returns server failure',
      () async {
        // arrange
        when(mockRepository.getAllPricingVehicles(vehicleId))
            .thenAnswer((_) async => Left(ServerFailure(msj: 'Server error')));

        // act
        final result = await usecase(vehicleId);

        // assert
        expect(result, Left(ServerFailure(msj: 'Server error')));
        verify(mockRepository.getAllPricingVehicles(vehicleId));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test(
      'should return network failure when repository returns network failure',
      () async {
        // arrange
        when(mockRepository.getAllPricingVehicles(vehicleId))
            .thenAnswer((_) async => Left(NetworkFailure()));

        // act
        final result = await usecase(vehicleId);

        // assert
        expect(result, Left(NetworkFailure()));
        verify(mockRepository.getAllPricingVehicles(vehicleId));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test(
      'should return null when repository returns null',
      () async {
        // arrange
        when(mockRepository.getAllPricingVehicles(vehicleId))
            .thenAnswer((_) async => null);

        // act
        final result = await usecase(vehicleId);

        // assert
        expect(result, null);
        verify(mockRepository.getAllPricingVehicles(vehicleId));
        verifyNoMoreInteractions(mockRepository);
      },
    );
  });
}
