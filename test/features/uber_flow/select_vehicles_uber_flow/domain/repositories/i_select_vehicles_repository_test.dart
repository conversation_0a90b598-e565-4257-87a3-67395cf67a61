import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/data_sources/i_select_vehicles_data_soruce.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/PricingVehiclesModel.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/save_vehicle_step_model.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/save_vehicle_step_input_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/repositories/select_vehicles_repository_impl.dart';

import 'i_select_vehicles_repository_test.mocks.dart';

// Generate mocks for the data source and network info
@GenerateMocks([ISelectVehiclesDataSource, NetworkInfo])
void main() {
  late SelectVehiclesRepositoryImpl selectVehiclesRepositoryImpl;
  late MockISelectVehiclesDataSource mockSelectVehiclesDataSource;
  late MockNetworkInfo mockNetworkInfo;

  setUp(() {
    mockSelectVehiclesDataSource = MockISelectVehiclesDataSource();
    mockNetworkInfo = MockNetworkInfo();
    selectVehiclesRepositoryImpl = SelectVehiclesRepositoryImpl(
      selectVehiclesDataSource: mockSelectVehiclesDataSource,
      networkInfo: mockNetworkInfo,
    );
  });

  // Test getAllPricingVehicles method
  group('getAllPricingVehicles', () {
    final vehicleId = 1;
    final tPricingVehicleModels =
        <PricingVehiclesModel>[]; // Empty list as a safe mock

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSelectVehiclesDataSource.getAllPricingVehicles(vehicleId))
            .thenAnswer((_) async => tPricingVehicleModels);

        // act
        await selectVehiclesRepositoryImpl.getAllPricingVehicles(vehicleId);

        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });

      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          final tPricingVehicleModels = [
            PricingVehiclesModel(
              id: 1,
              title: "Test Vehicle",
              subtitle: "Test Subtitle",
              commitmentMonths: 12,
            ),
          ];
          when(mockSelectVehiclesDataSource.getAllPricingVehicles(vehicleId))
              .thenAnswer((_) async => tPricingVehicleModels);

          // act
          final result = await selectVehiclesRepositoryImpl
              .getAllPricingVehicles(vehicleId);

          // assert
          verify(mockSelectVehiclesDataSource.getAllPricingVehicles(vehicleId));

          // Use fold to check the result instead of direct comparison
          result?.fold(
              (failure) => fail("Expected Right, got Left with $failure"),
              (data) {
            expect(data?.length, tPricingVehicleModels.length);
            // You can add more specific assertions here if needed
          });
        },
      );

      test(
        'should return server failure when the call to remote data source throws ServerException',
        () async {
          // arrange
          when(mockSelectVehiclesDataSource.getAllPricingVehicles(vehicleId))
              .thenThrow(ServerException(msj: 'Server error'));

          // act
          final result = await selectVehiclesRepositoryImpl
              .getAllPricingVehicles(vehicleId);

          // assert
          verify(mockSelectVehiclesDataSource.getAllPricingVehicles(vehicleId));

          // Use isA matcher to avoid type parameter issues
          expect(result, isA<Left<Failure, dynamic>>());
          result?.fold((failure) => expect(failure, isA<ServerFailure>()),
              (data) => fail("Expected Left, got Right with $data"));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });

      test(
        'should return network failure when device is offline',
        () async {
          // act
          final result = await selectVehiclesRepositoryImpl
              .getAllPricingVehicles(vehicleId);

          // assert
          verifyZeroInteractions(mockSelectVehiclesDataSource);

          // Use isA matcher to avoid type parameter issues
          expect(result, isA<Left<Failure, dynamic>>());
          result?.fold((failure) => expect(failure, isA<NetworkFailure>()),
              (data) => fail("Expected Left, got Right with $data"));
        },
      );
    });
  });

  // Test saveVehicleStepInputEntity method
  group('saveVehicleStepInputEntity', () {
    final tSaveVehicleStepInputEntity = SaveVehicleStepInputEntity(
      vehicleId: 1,
      carRegistrationAttachmentUrl: '',
      customerOwnedCar: false,
      vehiclePricingId: 1,
      commitmentMonths: 1,
    );

    final tSaveVehicleStepModel = SaveVehicleStepModel(
      id: 1,
      vehicleId: 1,
      hasUberAccount: true,
      uberAccountEmail: '<EMAIL>',
      uberAccountPhone: '**********',
      status: 'Active',
    );

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(mockSelectVehiclesDataSource
                .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity))
            .thenAnswer((_) async => tSaveVehicleStepModel);

        // act
        await selectVehiclesRepositoryImpl
            .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity);

        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });

      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockSelectVehiclesDataSource
                  .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity))
              .thenAnswer((_) async => tSaveVehicleStepModel);

          // act
          final result = await selectVehiclesRepositoryImpl
              .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity);

          // assert
          verify(mockSelectVehiclesDataSource
              .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity));

          // Use fold to check the result instead of direct comparison
          result
              .fold((failure) => fail("Expected Right, got Left with $failure"),
                  (data) {
            expect(data?.id, tSaveVehicleStepModel.id);
            // Add more specific assertions if needed
          });
        },
      );

      test(
        'should return server failure when the call to remote data source throws ServerException',
        () async {
          // arrange
          when(mockSelectVehiclesDataSource
                  .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity))
              .thenThrow(ServerException(msj: 'Server error'));

          // act
          final result = await selectVehiclesRepositoryImpl
              .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity);

          // assert
          verify(mockSelectVehiclesDataSource
              .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity));

          // Use isA matcher to avoid type parameter issues
          expect(result, isA<Left<Failure, dynamic>>());
          result.fold((failure) => expect(failure, isA<ServerFailure>()),
              (data) => fail("Expected Left, got Right with $data"));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });

      test(
        'should return network failure when device is offline',
        () async {
          // act
          final result = await selectVehiclesRepositoryImpl
              .saveVehicleStepInputEntity(tSaveVehicleStepInputEntity);

          // assert
          verifyZeroInteractions(mockSelectVehiclesDataSource);

          // Use isA matcher to avoid type parameter issues
          expect(result, isA<Left<Failure, dynamic>>());
          result.fold((failure) => expect(failure, isA<NetworkFailure>()),
              (data) => fail("Expected Left, got Right with $data"));
        },
      );
    });
  });
}
