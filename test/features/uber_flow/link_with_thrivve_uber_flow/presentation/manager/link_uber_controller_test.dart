import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_support_url_use_case.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/mappers/link_uber_with_thrivve_response_extension.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/use_cases/link_uber_with_thrivve_use_case.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/presentation/manager/link_uber_account_controller.dart';

import '../../data/models/link_uber_with_thrivve_response_test_model.dart';
import 'link_uber_controller_test.mocks.dart';

// Generate mocks for the use cases and bottom sheet helper
@GenerateMocks([
  LinkUberWithThrivveUseCase,
  GetSupportUrlUseCase,
  IShowBottomSheetHelper,
])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized(); // ✅ Fix for binding error

  late LinkUberAccountController controller;
  late MockLinkUberWithThrivveUseCase mockLinkUberWithThrivveUseCase;
  late MockGetSupportUrlUseCase mockGetSupportUrlUseCase;
  late MockIShowBottomSheetHelper mockIShowBottomSheetHelper;

  setUp(() {
    // Initialize GetX test environment
    Get.testMode = true;
    mockLinkUberWithThrivveUseCase = MockLinkUberWithThrivveUseCase();
    mockGetSupportUrlUseCase = MockGetSupportUrlUseCase();
    mockIShowBottomSheetHelper = MockIShowBottomSheetHelper();

    // Initialize controller with mocks
    controller = LinkUberAccountController(mockLinkUberWithThrivveUseCase,
        mockIShowBottomSheetHelper, mockGetSupportUrlUseCase);
  });

  tearDown(() {
    Get.reset();
  });

  group('LinkUberAccountController Initialization Tests', () {
    test('should initialize controller with default values', () {
      // Assert initial values
      expect(controller.haveUberAccount.value, null);
      expect(controller.showPage.value, false);
      expect(controller.hasAccount.value, false);
      expect(controller.isNextButtonEnabled.value, false);
      expect(controller.mailController.text, '');
      expect(controller.mobileController.text, '');
    });

    test('should add listeners to text controllers on init', () {
      // This is hard to test directly, but we can verify behavior by changing
      // the text in the controllers and checking if validation is triggered
      Get.put<LinkUberAccountController>(controller);
      // Set valid email and phone
      controller.mailController.text = '<EMAIL>';
      controller.mobileController.text = '*********';

      // Validation should update isNextButtonEnabled
      expect(controller.isNextButtonEnabled.value, true);
    });
  });

  group('Form Validation Tests', () {
    test('Next button should be disabled with empty fields', () {
      expect(controller.isNextButtonEnabled.value, false);
    });

    test('Next button should be disabled with invalid email', () {
      // Arrange
      controller.mailController.text = 'invalid-email';
      controller.mobileController.text = '*********';

      // Act - validation is triggered by listeners
      controller.validateForm();

      // Assert
      expect(controller.isNextButtonEnabled.value, false);
    });

    test('Next button should be disabled with invalid phone number', () {
      // Arrange
      controller.mailController.text = '<EMAIL>';
      controller.mobileController.text = 'abc123'; // Invalid phone number

      // Act
      controller.validateForm();

      // Assert
      expect(controller.isNextButtonEnabled.value, false);
    });

    test('Next button should be enabled with valid email and phone number', () {
      // Arrange
      controller.mailController.text = '<EMAIL>';
      controller.mobileController.text = '*********';

      // Act
      controller.validateForm();

      // Assert
      expect(controller.isNextButtonEnabled.value, true);
    });
  });

  group('Link Uber Account API Tests', () {
    testWidgets(
        'should call linkUberWithThrivveUseCase with correct parameters',
        (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      final linkUberWithThrivveResponseTestModel =
          LinkUberWithThrivveResponseTestModel();
      // Arrange

      late LinkUberWithThrivveRequestParamsModel captured;
      when(mockLinkUberWithThrivveUseCase.call(any)).thenAnswer((invocation) {
        captured = invocation.positionalArguments.first
            as LinkUberWithThrivveRequestParamsModel;
        return Future.value(Right(linkUberWithThrivveResponseTestModel
            .toEntity())); // Or whatever your use case returns
      });

      controller.mailController.text = '<EMAIL>';
      controller.mobileController.text = '*********';
      controller.hasAccount.value = true;

      // Act
      await controller.linkUberWithThrive();

      // Assert
      verify(mockLinkUberWithThrivveUseCase.call(any)).called(1);

      expect(captured.hasUberAccount, true);
      expect(captured.uberAccountEmail, '<EMAIL>');
      expect(captured.uberAccountPhone,
          '966*********'); // Should be prefixed with 966
    });

    testWidgets(
        'should handle success response from linkUberWithThrivveUseCase',
        (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      final linkUberWithThrivveResponseTestModel =
          LinkUberWithThrivveResponseTestModel();
      when(mockLinkUberWithThrivveUseCase.call(any)).thenAnswer(
          (_) async => Right(linkUberWithThrivveResponseTestModel.toEntity()));

      controller.mailController.text = '<EMAIL>';
      controller.mobileController.text = '*********';

      await controller.linkUberWithThrive();

      // Assert - show bottom sheet should be called
      // This is simplified as we can't fully test bottom sheet in unit tests
      verify(mockLinkUberWithThrivveUseCase.call(any)).called(1);
    });

    testWidgets(
        'should handle failure response from linkUberWithThrivveUseCase',
        (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      when(mockLinkUberWithThrivveUseCase.call(any))
          .thenAnswer((_) async => Left(ServerFailure()));

      controller.mailController.text = '<EMAIL>';
      controller.mobileController.text = '*********';

      // Act
      await controller.linkUberWithThrive();
      await tester.pump();
      // Assert
      verify(mockLinkUberWithThrivveUseCase.call(any)).called(1);
      // We can't directly test snackbar in unit tests, but we can verify the call was made
      await tester.pump(const Duration(seconds: 3));
    });
  });

  group('Get Support Information Tests', () {
    testWidgets('should call getSupportUrlUseCase', (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      // Arrange
      when(mockGetSupportUrlUseCase.call(any))
          .thenAnswer((_) async => const Right('https://support.example.com'));

      // Act
      await controller.getSupportInformation();

      // Assert
      verify(mockGetSupportUrlUseCase.call(any)).called(1);
    });

    testWidgets('should handle successful response from getSupportUrlUseCase',
        (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      // Arrange
      const supportUrl = 'https://support.example.com';
      when(mockGetSupportUrlUseCase.call(any))
          .thenAnswer((_) async => const Right(supportUrl));

      // Act
      await controller.getSupportInformation();

      // Assert
      verify(mockGetSupportUrlUseCase.call(any)).called(1);
      // We can't directly test url launching in unit tests
    });

    testWidgets('should handle failure response from getSupportUrlUseCase',
        (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      // Arrange
      when(mockGetSupportUrlUseCase.call(any))
          .thenAnswer((_) async => Left(ServerFailure()));

      // Act
      await controller.getSupportInformation();

      // Assert
      verify(mockGetSupportUrlUseCase.call(any)).called(1);
      await tester.pump(const Duration(seconds: 3));
    });
  });

  group('UI Interaction Tests', () {
    testWidgets('submitForm should show custom bottom sheet', (tester) async {
      // Arrange: Ensure ScreenUtil is initialized
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 821), // Use your app's design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (_, __) => GetMaterialApp(
            home: Scaffold(body: Container()), // Provides Get.context
          ),
        ),
      );
      // Arrange - We can't fully test bottom sheets in unit tests
      // but we can check that the method is called

      // Act
      controller.submitForm();

      // Assert
      // Verification is limited for UI interactions in unit tests
      // Would need integration tests for more thorough verification
    });
  });
}
