import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/data_sources/i_link_uber_with_thrivve_data_source.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/mappers/link_uber_with_thrivve_response_extension.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/entities/link_uber_with_thrive_response_entity.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/repositories/link_uber_with_thrivve_repository_implementation.dart';

import '../../data/models/link_uber_with_thrivve_response_test_model.dart';
import 'link_uber_with_thrivve_repository_implementation_test.mocks.dart';

@GenerateMocks([LinkUberWithThrivveDataSource, NetworkInfo])
void main() {
  late LinkUberWithThrivveRepositoryImplentation repository;
  late MockLinkUberWithThrivveDataSource mockDataSource;
  late MockNetworkInfo mockNetworkInfo;

  setUp(() {
    mockDataSource = MockLinkUberWithThrivveDataSource();
    mockNetworkInfo = MockNetworkInfo();
    repository = LinkUberWithThrivveRepositoryImplentation(
        mockDataSource, mockNetworkInfo);
  });

  final tParams = LinkUberWithThrivveRequestParamsModel();
  final tResponseModel = LinkUberWithThrivveResponseTestModel();

  test('should check if the device is online', () async {
    // arrange
    when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
    when(mockDataSource.linkUberWithThrivve(any))
        .thenAnswer((_) async => tResponseModel);
    // act
    repository.linkUberWithThrivve(tParams);
    // assert
    verify(mockNetworkInfo.isConnected);
  });

  group('device is online', () {
    setUp(() {
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
    });

    test('should return data when the call to data source is successful',
        () async {
      // arrange
      when(mockDataSource.linkUberWithThrivve(any))
          .thenAnswer((_) async => tResponseModel);
      // act
      final result = await repository.linkUberWithThrivve(tParams);
      // assert
      verify(mockDataSource.linkUberWithThrivve(tParams));
      expect(result, isA<Right<Failure, LinkUberWithThrivveResponseEntity?>>());
      expect(result.getOrElse(() => tResponseModel.toEntity()),
          equals(tResponseModel.toEntity()));
    });

    test(
        'should return ServerFailure when the call to data source is unsuccessful',
        () async {
      // arrange
      when(mockDataSource.linkUberWithThrivve(any))
          .thenThrow(ServerException(msj: 'Server Error'));
      // act
      final result = await repository.linkUberWithThrivve(tParams);
      // assert
      verify(mockDataSource.linkUberWithThrivve(tParams));
      expect(result, equals(Left(ServerFailure(msj: 'Server Error'))));
    });

    test('should return NetworkFailure when there is a network issue',
        () async {
      // arrange
      when(mockDataSource.linkUberWithThrivve(any))
          .thenThrow(NetworkException(msj: 'Network Error'));
      // act
      final result = await repository.linkUberWithThrivve(tParams);
      // assert
      verify(mockDataSource.linkUberWithThrivve(tParams));
      expect(result, equals(Left(NetworkFailure(msj: 'Network Error'))));
    });
  });

  group('device is offline', () {
    setUp(() {
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
    });

    test('should return NetworkFailure when device is offline', () async {
      // act
      final result = await repository.linkUberWithThrivve(tParams);
      // assert
      verifyZeroInteractions(mockDataSource);
      expect(result, equals(Left(NetworkFailure())));
    });
  });
}
