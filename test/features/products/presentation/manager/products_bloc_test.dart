import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/dashboard/data/models/products_model.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_products_use_case.dart';
import 'package:thrivve/features/products/presentation/manager/products_bloc.dart';
import 'package:thrivve/generated/assets.dart';

import 'products_bloc_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<GetProductsUseCase>(),
])
void main() {
  late ProductsBloc productsBloc;
  late MockGetProductsUseCase mockGetProductsUseCase;

  setUp(() {
    mockGetProductsUseCase = MockGetProductsUseCase();
    productsBloc = ProductsBloc(
      getProductsUseCase: mockGetProductsUseCase,
    );
  });

  tearDown(() {
    productsBloc.close();
  });

  test('initial state is correct', () {
    expect(productsBloc.state, ProductsState());
  });

  group("get list of products", () {
    final listOfProducts = [
      ProductModel(
          id: 0, image: Assets.thrivvePhotosX, nameAr: '', subTitleAr: ''),
      ProductModel(
          id: 1, image: Assets.thrivvePhotosX, nameAr: '', subTitleAr: ''),
      ProductModel(
          id: 2, image: Assets.thrivvePhotosX, nameAr: '', subTitleAr: ''),
    ];

    final products = ProductsModel(
      productsList: listOfProducts,
      defaultObject: null,
      viewMore: false,
    );

    blocTest<ProductsBloc, ProductsState>(
      'emits [] when nothing is added',
      build: () => productsBloc,
      expect: () => [],
    );

    test("initialState should be empty", () async {
      expect(
        productsBloc.state.getListOfProductsStatuses,
        ProductsStatuses.initial,
        reason: "initial state should be empty",
      );
    });

    blocTest<ProductsBloc, ProductsState>(
      'emits loading, failure status when GetListOfProductsEvent is added',
      build: () {
        when(mockGetProductsUseCase(any))
            .thenAnswer((_) async => Left(ServerFailure()));
        return productsBloc;
      },
      expect: () => [
        ProductsState(
          getListOfProductsStatuses: ProductsStatuses.loading,
          page: 1,
          viewMore: true,
        ),
        ProductsState(
          getListOfProductsStatuses: ProductsStatuses.failure,
          errorMessage: "server_failure",
          page: 1,
          viewMore: true,
        ),
      ],
      wait: const Duration(milliseconds: 500),
      act: (bloc) => bloc.add(const GetListOfProductsEvent()),
    );

    blocTest<ProductsBloc, ProductsState>(
      'emits loading, success status when GetListOfProductsEvent is added',
      build: () {
        when(mockGetProductsUseCase(any))
            .thenAnswer((_) async => Right(products));
        return productsBloc;
      },
      expect: () => [
        ProductsState(
          getListOfProductsStatuses: ProductsStatuses.loading,
          page: 1,
          viewMore: true,
        ),
        ProductsState(
          getListOfProductsStatuses: ProductsStatuses.success,
          listOfProducts: listOfProducts,
          page: 2,
          viewMore: products.viewMore,
        ),
      ],
      wait: const Duration(milliseconds: 1000),
      act: (bloc) => bloc.add(const GetListOfProductsEvent()),
    );
  });
}
