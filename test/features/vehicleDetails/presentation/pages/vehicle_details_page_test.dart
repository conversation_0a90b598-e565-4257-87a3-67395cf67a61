import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/vehicleDetails/domain/entities/vehicle.dart';
import 'package:thrivve/features/vehicleDetails/presentation/manager/vehicle_details_bloc.dart';
import 'package:thrivve/features/vehicleDetails/presentation/pages/vehicle_details_page.dart';

import '../../../products/presentation/pages/products_page_test.mocks.dart';
import 'vehicle_details_page_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<VehicleDetailsBloc>(),
])
void main() {
  GetIt getIt = GetIt.instance;
  late MockVehicleDetailsBloc mockBloc;
  late MockMainHomeBloc mainHomeBloc;

  setUp(() async {
    await initializeDateFormatting('en', null);

    mockBloc = MockVehicleDetailsBloc();
    mainHomeBloc = MockMainHomeBloc();
    getIt.registerSingleton<VehicleDetailsBloc>(mockBloc);
    getIt.registerSingleton<MainHomeBloc>(mainHomeBloc);
  });

  tearDown(() {
    getIt.reset();
  });

  group('VehicleDetailsPage tests', () {
    testWidgets(
      "VehicleDetailsPage displays loading state successfully",
      (widgetTester) async {
        // Arrange: Stub state and stream
        when(mockBloc.state).thenReturn(
          const VehicleDetailsState(status: AppStatus.loading),
        );

        // Act
        await widgetTester.pumpWidget(getMaterialApp(getIt));

        // Assert: Check for Shimmer widget instead of CircularProgressIndicator
        expect(find.byType(Shimmer), findsOneWidget);
      },
    );

    testWidgets(
      'shows vehicle details when state is success',
      (WidgetTester tester) async {
        // Arrange: Setup a successful state with vehicle data
        const successState = VehicleDetailsState(
          status: AppStatus.success,
          vehicle: Vehicle(
            carName: 'Test Car',
            totalPrice: '\$50,000',
            deliveryDate: '2024-12-31',
            images: ['https://example.com/image.png'],
            gearType: 'Automatic',
            fuelType: 'Petrol',
            motorSize: '2.0L',
            carType: 'Sedan',
            id: '123',
            likes: null,
            features: [],
            isLiked: null,
            priceNote: '',
          ),
        );
        when(mockBloc.state).thenReturn(successState);
        when(mockBloc.stream).thenAnswer((_) => Stream.value(successState));

        // Act
        await tester.pumpWidget(getMaterialApp(getIt));
        await tester.pump(); // Allow the widget to build

        // Assert
        expect(find.text('Test Car'), findsOneWidget);
        expect(find.text('\$50,000'), findsOneWidget);
        expect(find.text('Automatic'), findsOneWidget);
      },
    );

    testWidgets(
      'shows error message when state is error',
      (WidgetTester tester) async {
        // Arrange: Setup an error state
        const errorState = VehicleDetailsState(
          status: AppStatus.failure,
          errorMessage: 'An error occurred',
        );
        when(mockBloc.state).thenReturn(errorState);
        when(mockBloc.stream).thenAnswer((_) => Stream.value(errorState));

        // Act
        await tester.pumpWidget(getMaterialApp(getIt));
        await tester.pump(); // Allow the widget to build

        // Assert
        expect(find.text('An error occurred'), findsOneWidget);
      },
    );

    testWidgets(
      'should call LikeVehicleEvent when heart icon is tapped',
      (WidgetTester tester) async {
        // Arrange: Set up a state with a vehicle
        const initState = VehicleDetailsState(
          status: AppStatus.success,
          isFavourite: false,
          vehicle: Vehicle(
            carName: 'Test Car',
            totalPrice: '\$50,000',
            deliveryDate: '2024-12-31',
            images: ['https://example.com/image.png'],
            gearType: 'Automatic',
            fuelType: 'Petrol',
            motorSize: '2.0L',
            carType: 'Sedan',
            id: '123',
            likes: null,
            features: [],
            isLiked: null,
            priceNote: '',
          ),
        );
        when(mockBloc.state).thenReturn(initState);
        when(mockBloc.stream).thenAnswer((_) => Stream.value(initState));

        // Act
        await tester.pumpWidget(getMaterialApp(getIt));
        await tester.pump(); // Allow the widget to build

        await tester.tap(find.byIcon(Icons.favorite_border_rounded));
        await tester.pump(); // Process the tap

        // Assert
        verify(mockBloc.add(const LikeVehicleEvent())).called(1);
      },
    );
  });
}

ScreenUtilInit getMaterialApp(GetIt getIt) {
  return ScreenUtilInit(
    designSize: const Size(375, 821),
    minTextAdapt: true,
    splitScreenMode: true,
    builder: (context, child) {
      return GetMaterialApp(
        getPages: AppRoutes.routes,
        home: MultiBlocProvider(
          providers: [
            BlocProvider<VehicleDetailsBloc>(
              create: (context) => getIt<VehicleDetailsBloc>(),
            ),
            BlocProvider<MainHomeBloc>(
              create: (context) => getIt<MainHomeBloc>(),
            ),
          ],
          child: const VehicleDetailsPage(carId: 1), // Use String for carId
        ),
      );
    },
  );
}
