import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/features/referral/domain/entities/referral_status.dart';
import 'package:thrivve/features/referral/domain/entities/referral_status_values.dart';
import 'package:thrivve/features/referral/domain/use_cases/get_referral_status_values_use_case.dart';
import 'package:thrivve/features/referral/presentation/manager/referral_bloc.dart';
import 'package:thrivve/features/referral/presentation/manager/referral_event.dart';
import 'package:thrivve/features/referral/presentation/manager/referral_state.dart';

import 'referral_bloc_test.mocks.dart'; // Use the mocks generated by this file

const String SERVER_FAILURE_MESSAGE = "server_failure";

@GenerateMocks([IAnalyticsLogger, GetReferralStatusValuesUseCase])
void main() {
  late MockGetReferralStatusValuesUseCase mockGetReferralStatusValues;
  late MockIAnalyticsLogger mockAnalyticsLogger;
  late ReferralBloc referralBloc;
  final getIt = GetIt.instance;

  setUp(() {
    mockAnalyticsLogger = MockIAnalyticsLogger();
    mockGetReferralStatusValues = MockGetReferralStatusValuesUseCase();

    // Register dependencies before instantiating the bloc
    getIt.registerSingleton<IAnalyticsLogger>(mockAnalyticsLogger);
    // Stub the logEvent call
    when(mockAnalyticsLogger.logEvent(any))
        .thenAnswer((_) async => Future.value());

    referralBloc = ReferralBloc(
      getReferralStatusValues: mockGetReferralStatusValues,
    );
  });

  tearDown(() {
    referralBloc.close();
    getIt.reset();
  });

  group('ReferralBloc', () {
    const referralStatus = ReferralStatus(
      rewardValue: 294,
      rewardRequirement: 94,
      rewardCurrency: 'SAR',
      referralSummary: 'Get a 294 SAR for each friend you refer...',
      rewardRequirementStr: 'Completed 94 Trips',
      referralInstructions: [],
      referralNotes: [],
      referralUrl: 'https://example.com',
    );

    const referralStatusValues = ReferralStatusValues(
      registeredCount: 0,
      rewardedCount: 0,
    );

    test('initial state is ReferralState', () {
      expect(referralBloc.state, const ReferralState());
    });

    blocTest<ReferralBloc, ReferralState>(
      'emits [loading, success] when FetchReferralStatus is added and succeeds',
      build: () {
        when(mockGetReferralStatusValues())
            .thenAnswer((_) async => const Right(referralStatusValues));
        return referralBloc;
      },
      act: (bloc) =>
          bloc.add(const FetchReferralStatus(referralStatus: referralStatus)),
      expect: () => [
        const ReferralState(status: AppStatus.loading),
        const ReferralState(
          status: AppStatus.success,
          referralStatus: referralStatus,
          referralStatusValues: referralStatusValues,
        ),
      ],
    );

    blocTest<ReferralBloc, ReferralState>(
      'emits [loading, failure] when FetchReferralStatus is added and fails',
      build: () {
        when(mockGetReferralStatusValues())
            .thenAnswer((_) async => Left(ServerFailure()));
        return referralBloc;
      },
      act: (bloc) =>
          bloc.add(const FetchReferralStatus(referralStatus: referralStatus)),
      expect: () => [
        const ReferralState(status: AppStatus.loading),
        const ReferralState(
          status: AppStatus.failure,
          errorMessage: SERVER_FAILURE_MESSAGE,
        ),
      ],
    );
  });
}
