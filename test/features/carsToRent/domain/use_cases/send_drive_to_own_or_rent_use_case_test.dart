import 'package:dartz/dartz.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/listOfVehicles/domain/repositories/list_of_cars_repository.dart';

class MockListOfCarsRepository extends Mock implements ListOfCarsRepository {}

void main() {
  late MockListOfCarsRepository mockListOfCarsRepository;
  setUp(() {
    mockListOfCarsRepository = MockListOfCarsRepository();
  });

  int? driveWithUs = 1;
  String? message = "success";

  // test get send drive to own or rent use case
  test("test send drive to own or rent use case", () async {
    // arrange
    when(mockListOfCarsRepository.setDriveToOwnRequest(
            driveWithUs: driveWithUs))
        .thenAnswer((_) async => Right(message));

    // act
    var result = await mockListOfCarsRepository.setDriveToOwnRequest(
        driveWithUs: driveWithUs);

    // assert
    verify(mockListOfCarsRepository.setDriveToOwnRequest(
        driveWithUs: driveWithUs));
    expect(result, Right(message));
  });

  // test send drive to own or rent use case when return NetworkFailure
  test("test send drive to own or rent use case when return NetworkFailure",
      () async {
    // arrange
    when(mockListOfCarsRepository.setDriveToOwnRequest(
            driveWithUs: driveWithUs))
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act
    var result = await mockListOfCarsRepository.setDriveToOwnRequest(
        driveWithUs: driveWithUs);

    // assert
    verify(mockListOfCarsRepository.setDriveToOwnRequest(
        driveWithUs: driveWithUs));
    expect(result, Left(NetworkFailure()));
  });

  // test send drive to own or rent use case when return ServerFailure
  test("test send drive to own or rent use case when return ServerFailure",
      () async {
    // arrange
    when(mockListOfCarsRepository.setDriveToOwnRequest(
            driveWithUs: driveWithUs))
        .thenAnswer((_) async => Left(ServerFailure()));

    // act
    var result = await mockListOfCarsRepository.setDriveToOwnRequest(
        driveWithUs: driveWithUs);

    // assert
    verify(mockListOfCarsRepository.setDriveToOwnRequest(
        driveWithUs: driveWithUs));
    expect(result, Left(ServerFailure()));
  });

}
