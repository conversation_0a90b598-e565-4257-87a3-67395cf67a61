import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/personalInfo/data/data_sources/personal_info_and_identity_remote_data_source.dart';
import 'package:thrivve/features/personalInfo/data/models/personal_info_model.dart';
import 'package:thrivve/features/personalInfo/data/models/personal_info_result_model.dart';
import 'package:thrivve/features/personalInfo/data/repositories/personal_info_repository_impl.dart';
import 'package:thrivve/features/personalInfo/domain/entities/city.dart';

class MockRemoteDataSource extends Mock
    implements PersonalAndIdentityInfoRemoteDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

void main() {
  late PersonalInfoRepositoryImpl personalInfoRepositoryImpl;
  late MockRemoteDataSource mockRemoteDataSource;
  late MockNetworkInfo mockNetworkInfo;
  setUp(() {
    mockRemoteDataSource = MockRemoteDataSource();
    mockNetworkInfo = MockNetworkInfo();
    personalInfoRepositoryImpl = PersonalInfoRepositoryImpl(
        networkInfo: mockNetworkInfo, remoteDataSource: mockRemoteDataSource);
  });

  group("save user info", () {
    var image = "image";
    var address = "address";
    var fullName = "fullName";
    var cityId = "cityId";
    test("should check if the device is online ", () async {
      // arrange
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      // act
      await personalInfoRepositoryImpl.saveUserInfo();
      // assert
      verify(mockNetworkInfo.isConnected);
    });

    group("device is online", () {
      var personalInfoResult = const PersonalInfoResultModel(message: "done");

      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => true));

      test(
          "should return remote data when call remote data source is successfully",
          () async {
        // arrange
        when(mockRemoteDataSource.setPersonalInfo(
                image: image,
                fullName: fullName,
                cityId: cityId,
                address: address))
            .thenAnswer((_) async => personalInfoResult);
        // act
        final result = await personalInfoRepositoryImpl.saveUserInfo(
            fullName: fullName,
            imageUrl: image,
            city: cityId,
            address: address);
        // assert
        verify(mockRemoteDataSource.setPersonalInfo(
            image: image,
            fullName: fullName,
            cityId: cityId,
            address: address));

        expect(result, Right(personalInfoResult));
      });

      test(
          "should return server Failure when call remote data source is unsuccessfully",
          () async {
        // arrange
        when(mockRemoteDataSource.setPersonalInfo(
                image: image,
                fullName: fullName,
                cityId: cityId,
                address: address))
            .thenThrow(ServerException());
        // act
        final result = await personalInfoRepositoryImpl.saveUserInfo(
            fullName: fullName,
            imageUrl: image,
            city: cityId,
            address: address);
        // assert
        verify(mockRemoteDataSource.setPersonalInfo(
            image: image,
            fullName: fullName,
            cityId: cityId,
            address: address));
        expect(result, Left(ServerFailure()));
      });
    });
    group("device is offline", () {
      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => false));

      test(
          "should return network Failure when call remote data source  is failure",
          () async {
        // arrange
        when(mockRemoteDataSource.setPersonalInfo(
                image: image,
                fullName: fullName,
                cityId: cityId,
                address: address))
            .thenThrow(NetworkException());
        // act
        final result = await personalInfoRepositoryImpl.saveUserInfo(
            fullName: fullName,
            imageUrl: image,
            city: cityId,
            address: address);
        // assert
        verifyZeroInteractions(mockRemoteDataSource);
        verifyNoMoreInteractions(mockRemoteDataSource);
        expect(result, Left(NetworkFailure()));
      });
    });
  });

  // test uploadUserImage method
  group("upload user image", () {
    var filePath = FormData();
    var cancelToken = CancelToken();
    Function(int, int)? onProgress;
    test("should check if the device is online ", () async {
      // arrange
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      // act
      await personalInfoRepositoryImpl.uploadUserImage(
        filePath,
        cancelToken,
        onProgress,
      );
      // assert
      verify(mockNetworkInfo.isConnected);
    });

    group("device is online", () {
      var imageUrl = "image url";

      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => true));

      test(
          "should return remote data when call remote data source is successfully",
          () async {
        // arrange
        when(mockRemoteDataSource.uploadImage(filePath: filePath))
            .thenAnswer((_) async => imageUrl);
        // act
        final result = await personalInfoRepositoryImpl.uploadUserImage(
          filePath,
          cancelToken,
          onProgress,
        );
        // assert
        verify(mockRemoteDataSource.uploadImage(filePath: filePath));

        expect(result, Right(imageUrl));
      });

      test(
          "should return server Failure when call remote data source is unsuccessfully",
          () async {
        // arrange
        when(mockRemoteDataSource.uploadImage(filePath: filePath))
            .thenThrow(ServerException());
        // act
        final result = await personalInfoRepositoryImpl.uploadUserImage(
          filePath,
          cancelToken,
          onProgress,
        );
        // assert
        verify(mockRemoteDataSource.uploadImage(filePath: filePath));
        expect(result, Left(ServerFailure()));
      });
    });
    group("device is offline", () {
      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => false));

      test(
          "should return network Failure when call remote data source  is failure",
          () async {
        // arrange
        when(mockRemoteDataSource.uploadImage(filePath: filePath))
            .thenThrow(NetworkException());
        // act
        final result = await personalInfoRepositoryImpl.uploadUserImage(
          filePath,
          cancelToken,
          onProgress,
        );
        // assert
        verifyZeroInteractions(mockRemoteDataSource);
        verifyNoMoreInteractions(mockRemoteDataSource);
        expect(result, Left(NetworkFailure()));
      });
    });
  });

  // test getUserInfo method
  group("get User info", () {
    var personalInfoResult = const PersonalInfoModel();
    test("should check if the device is online ", () async {
      // arrange
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      // act
      await personalInfoRepositoryImpl.getUserInfo();
      // assert
      verify(mockNetworkInfo.isConnected);
    });

    group("device is online", () {
      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => true));

      test(
          "should return remote data when call remote data source is successfully",
          () async {
        // arrange
        when(mockRemoteDataSource.getPersonalInfo())
            .thenAnswer((_) async => personalInfoResult);
        // act
        final result = await personalInfoRepositoryImpl.getUserInfo();
        // assert
        verify(mockRemoteDataSource.getPersonalInfo());

        expect(result, Right(personalInfoResult));
      });

      test(
          "should return server Failure when call remote data source is unsuccessfully",
          () async {
        // arrange
        when(mockRemoteDataSource.getPersonalInfo())
            .thenThrow(ServerException());
        // act
        final result = await personalInfoRepositoryImpl.getUserInfo();
        // assert
        verify(mockRemoteDataSource.getPersonalInfo());
        expect(result, Left(ServerFailure()));
      });
    });
    group("device is offline", () {
      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => false));

      test(
          "should return network Failure when call remote data source  is failure",
          () async {
        // arrange
        when(mockRemoteDataSource.getPersonalInfo())
            .thenThrow(NetworkException());
        // act
        final result = await personalInfoRepositoryImpl.getUserInfo();
        // assert
        verifyZeroInteractions(mockRemoteDataSource);
        verifyNoMoreInteractions(mockRemoteDataSource);
        expect(result, Left(NetworkFailure()));
      });
    });
  });

// test getCityList method
  group("get city list", () {
    var cityListResult = [City(id: 1, nameAr: "name", nameEn: "name")];
    var countryId = 1;
    test("should check if the device is online ", () async {
      // arrange
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      // act
      await personalInfoRepositoryImpl.getCities(countryId: countryId);
      // assert
      verify(mockNetworkInfo.isConnected);
    });

    group("device is online", () {
      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => true));

      test(
          "should return remote data when call remote data source is successfully",
          () async {
        // arrange
        when(mockRemoteDataSource.getListOfCitiesByCountryId(
                countryId: countryId))
            .thenAnswer((_) async => cityListResult);
        // act
        final result =
            await personalInfoRepositoryImpl.getCities(countryId: countryId);
        // assert
        verify(mockRemoteDataSource.getListOfCitiesByCountryId(
            countryId: countryId));

        expect(result, Right(cityListResult));
      });

      test(
          "should return server Failure when call remote data source is unsuccessfully",
          () async {
        // arrange
        when(mockRemoteDataSource.getListOfCitiesByCountryId(
                countryId: countryId))
            .thenThrow(ServerException());
        // act
        final result =
            await personalInfoRepositoryImpl.getCities(countryId: countryId);
        // assert
        verify(mockRemoteDataSource.getListOfCitiesByCountryId(
            countryId: countryId));
        expect(result, Left(ServerFailure()));
      });
    });
    group("device is offline", () {
      setUp(() =>
          when(mockNetworkInfo.isConnected).thenAnswer((_) async => false));

      test(
          "should return network Failure when call remote data source  is failure",
          () async {
        // arrange
        when(mockRemoteDataSource.getListOfCitiesByCountryId(
                countryId: countryId))
            .thenThrow(NetworkException());
        // act
        final result =
            await personalInfoRepositoryImpl.getCities(countryId: countryId);
        // assert
        verifyZeroInteractions(mockRemoteDataSource);
        verifyNoMoreInteractions(mockRemoteDataSource);
        expect(result, Left(NetworkFailure()));
      });
    });
  });
}
