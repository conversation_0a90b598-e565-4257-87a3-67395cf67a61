import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source_impl.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/onboarding/data/data_sources/auth_remote_data_source.dart';
import 'package:thrivve/features/onboarding/data/repositories/auth_repository_impl.dart';
import 'package:thrivve/features/onboarding/domain/entities/country.dart';
import 'package:thrivve/features/onboarding/domain/entities/return_otp.dart';
import 'package:thrivve/features/onboarding/domain/entities/verify_otp.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/send_otp_use_case.dart';
import 'package:thrivve/features/pin/data/mappers/thrivve_user_mapper.dart';
import 'package:thrivve/features/pin/domain/entities/thrivve_user.dart';

import '../../../products/presentation/pages/products_page_test.mocks.dart';
import 'auth_repository_impl_test.mocks.dart';

@GenerateMocks([
  NetworkInfo,
  UserSecureDataSourceImpl,
  AuthRemoteDataSource,
  AuthRepositoryImpl
])
void main() {
  late AuthRepositoryImpl authRepositoryImpl;
  late MockAuthRemoteDataSource mockAuthRemoteDataSource;
  late MockUserSecureDataSource mockUserSecureDataSource;
  late MockNetworkInfo mockNetworkInfo;

  setUp(() {
    mockUserSecureDataSource = MockUserSecureDataSource();
    mockAuthRemoteDataSource = MockAuthRemoteDataSource();
    mockNetworkInfo = MockNetworkInfo();
    authRepositoryImpl = AuthRepositoryImpl(
        networkInfo: mockNetworkInfo,
        remoteDataSource: mockAuthRemoteDataSource,
        localDataSource: mockUserSecureDataSource);
  });

  // test sendOtp method
  group('sendOtp', () {
    var mobile = '1234567890';
    var otpResult = const ReturnOtp(otp: "123456", message: 'success');

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(
            mockAuthRemoteDataSource.sendOtp(
              params: SendOptParams(
                mobile: mobile,
                isLogin: true,
              ),
            ),
          ).thenAnswer((_) async => otpResult);
          // act
          final result = await authRepositoryImpl.sendOtp(
            params: SendOptParams(
              mobile: mobile,
              isLogin: true,
            ),
          );
          // assert
          verify(mockAuthRemoteDataSource.sendOtp(
            params: SendOptParams(
              mobile: mobile,
              isLogin: true,
            ),
          ));
          expect(result, equals(Right(otpResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.sendOtp(
            params: SendOptParams(
              mobile: mobile,
              isLogin: true,
            ),
          )).thenThrow(ServerException());
          // act
          final result = await authRepositoryImpl.sendOtp(
            params: SendOptParams(
              mobile: mobile,
              isLogin: true,
            ),
          );
          // assert
          verify(mockAuthRemoteDataSource.sendOtp(
            params: SendOptParams(
              mobile: mobile,
              isLogin: true,
            ),
          ));
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // act
          final result = await authRepositoryImpl.sendOtp(
            params: SendOptParams(
              mobile: mobile,
              isLogin: true,
            ),
          );
          // assert
          verifyZeroInteractions(mockAuthRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test signIn method
  group("signIn", () {
    var thrivveUser = const ThrivveUser(
      mobile: 'mobile',
      accessToken: 'token',
      language: 'language',
      customerId: 1,
      fullName: '',
      currency: '',
      countryCode: '',
      image: '',
      refreshToken: 'refreshToken',
      deviceFingerprint: 'deviceFingerprint',
      deviceId: null,
      nickName: '',
      isBiometricEnabled: null,
      biometricType: '',
      appVersion: null,
      securitySettings: null,
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(
            mockAuthRemoteDataSource.signIn(
              language: "language",
              platform: "platform",
              platformVersion: "platformVersion",
              appVersion: "appVersion",
              pushNotificationToken: "pushNotificationToken",
              isNew: false,
            ),
          ).thenAnswer((_) async => thrivveUser.toModel());
          // act
          final result = await authRepositoryImpl.signIn(
            language: "language",
            platform: "platform",
            platformVersion: "platformVersion",
            appVersion: "appVersion",
            pushNotificationToken: "pushNotificationToken",
            isNew: false,
          );
          // assert
          verify(
            mockAuthRemoteDataSource.signIn(
              language: "language",
              platform: "platform",
              platformVersion: "platformVersion",
              appVersion: "appVersion",
              pushNotificationToken: "pushNotificationToken",
              isNew: false,
            ),
          );
          expect(result, Right(thrivveUser));
        },
      );

      test(
        'should return server failure when the call to remote data source throws ServerException',
        () async {
          // arrange
          when(
            mockAuthRemoteDataSource.signIn(
              language: "language",
              platform: "platform",
              platformVersion: "platformVersion",
              appVersion: "appVersion",
              pushNotificationToken: "pushNotificationToken",
              isNew: false,
            ),
          ).thenThrow(ServerException(msj: 'Server error'));

          // act
          final result = await authRepositoryImpl.signIn(
            language: "language",
            platform: "platform",
            platformVersion: "platformVersion",
            appVersion: "appVersion",
            pushNotificationToken: "pushNotificationToken",
            isNew: false,
          );

          // assert
          verify(
            mockAuthRemoteDataSource.signIn(
              language: "language",
              platform: "platform",
              platformVersion: "platformVersion",
              appVersion: "appVersion",
              pushNotificationToken: "pushNotificationToken",
              isNew: false,
            ),
          );

          // Use isA matcher to avoid type parameter issues
          expect(result, isA<Left<Failure, dynamic>>());
          result.fold((failure) => expect(failure, isA<ServerFailure>()),
              (data) => fail("Expected Left, got Right with $data"));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // act
          final result = await authRepositoryImpl.signIn(
              language: "language",
              platform: "platform",
              platformVersion: "platformVersion",
              appVersion: "appVersion",
              pushNotificationToken: "pushNotificationToken");
          // assert
          verifyZeroInteractions(mockAuthRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test verifyOtp method
  group("verifyOtp", () {
    var verifyOtpResult = const VerifyOtp(
      token: "token",
      message: "message",
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.verifyOtp(
                  mobile: 'mobile', otpCode: 'otpCode'))
              .thenAnswer((_) async => verifyOtpResult);
          // act
          final result = await authRepositoryImpl.verifyOtp(
              mobile: 'mobile', otpCode: 'otpCode');
          // assert
          verify(mockAuthRemoteDataSource.verifyOtp(
              mobile: 'mobile', otpCode: 'otpCode'));
          expect(result, equals(Right(verifyOtpResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.verifyOtp(
                  mobile: 'mobile', otpCode: 'otpCode'))
              .thenThrow(ServerException());
          // act
          final result = await authRepositoryImpl.verifyOtp(
              mobile: 'mobile', otpCode: 'otpCode');
          // assert
          verify(mockAuthRemoteDataSource.verifyOtp(
              mobile: 'mobile', otpCode: 'otpCode'));
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // act
          final result = await authRepositoryImpl.verifyOtp(
              mobile: 'mobile', otpCode: 'otpCode');
          // assert
          verifyZeroInteractions(mockAuthRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test getCountries method
  group("getCountries", () {
    setUp(() {
      when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
    });
    var countriesResult = [
      Country(nameAr: 'nameAr', nameEn: 'nameEn', dial: '', code: '', id: 1)
    ];

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.getCountries())
              .thenAnswer((_) async => countriesResult);
          // act
          final result = await authRepositoryImpl.getCountries();
          // assert
          verify(mockAuthRemoteDataSource.getCountries());
          expect(result, equals(Right(countriesResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.getCountries())
              .thenThrow(ServerException());
          // act
          final result = await authRepositoryImpl.getCountries();
          // assert
          verify(mockAuthRemoteDataSource.getCountries());
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // act
          final result = await authRepositoryImpl.getCountries();
          // assert
          verifyZeroInteractions(mockAuthRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // Test getCurrentCountryCode method
  group("getCurrentCountryCode", () {
    var countryCode = "PS";

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.getCurrentCountryCode())
              .thenAnswer((_) async => countryCode);
          // act
          final result = await authRepositoryImpl.getCurrentCountryCode();
          // assert
          verify(mockAuthRemoteDataSource.getCurrentCountryCode());
          expect(result, equals(Right(countryCode)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockAuthRemoteDataSource.getCurrentCountryCode())
              .thenThrow(ServerException());
          // act
          final result = await authRepositoryImpl.getCurrentCountryCode();
          // assert
          verify(mockAuthRemoteDataSource.getCurrentCountryCode());
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // act
          final result = await authRepositoryImpl.getCurrentCountryCode();
          // assert
          verifyZeroInteractions(mockAuthRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });
}
