import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/bankAccount/data/data_sources/bank_remote_data_source.dart';
import 'package:thrivve/features/bankAccount/data/repositories/add_bank_repository_impl.dart';
import 'package:thrivve/features/bankAccount/domain/entities/add_bank.dart';
import 'package:thrivve/features/bankAccount/domain/entities/bank.dart';
import 'package:thrivve/features/bankAccount/domain/entities/bank_details.dart';

class MockRemoteDataSource extends Mock implements BankRemoteDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

void main() {
  late BankRepositoryImpl bankRepositoryImpl;
  late MockRemoteDataSource mockRemoteDataSource;
  late MockNetworkInfo mockNetworkInfo;

  setUp(() {
    mockRemoteDataSource = MockRemoteDataSource();
    mockNetworkInfo = MockNetworkInfo();
    bankRepositoryImpl = BankRepositoryImpl(
        networkInfo: mockNetworkInfo, remoteDataSource: mockRemoteDataSource);
  });
  // test addBank method
  group('addBank', () {
    var bankResult = const AddBankSuccessfully(
      message: "message",
    );

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        // act
        bankRepositoryImpl.addBank(
          paymentType: 'paymentType',
          bankId: 1,
          beneficiaryName: 'accountName',
          beneficiaryIban: 'bankName',
        );
        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockRemoteDataSource.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          )).thenAnswer((_) async => bankResult);
          // act
          final result = await bankRepositoryImpl.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          );
          // assert
          verify(mockRemoteDataSource.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          ));
          expect(result, equals(Right(bankResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          )).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          );
          // assert
          verify(mockRemoteDataSource.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          ));
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          )).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.addBank(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          );
          // assert
          verifyZeroInteractions(mockRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test getBankList method
  group("getBankList", () {
    var bankListResult = [
      const Bank(
        id: 1,
        text: 'text',
        image: 'image',
      ),
      const Bank(
        id: 2,
        text: 'text',
        image: 'image',
      ),
    ];

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        // act
        bankRepositoryImpl.getBankList();
        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockRemoteDataSource.getBanks())
              .thenAnswer((_) async => bankListResult);
          // act
          final result = await bankRepositoryImpl.getBankList();
          // assert
          verify(mockRemoteDataSource.getBanks());
          expect(result, equals(Right(bankListResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.getBanks()).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.getBankList();
          // assert
          verify(mockRemoteDataSource.getBanks());
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.getBanks()).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.getBankList();
          // assert
          verifyZeroInteractions(mockRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test updateBankDetails method
  group('updateBankDetails', () {
    var bankResult = const AddBankSuccessfully(
      message: "message",
    );

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        // act
        bankRepositoryImpl.updateBankDetails(
          paymentType: 'paymentType',
          bankId: 1,
          beneficiaryName: 'accountName',
          beneficiaryIban: 'bankName',
          paymentMethodId: 1,
        );
        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockRemoteDataSource.updateBankDetails(
            paymentMethodId: 1,
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
          )).thenAnswer((_) async => bankResult);
          // act
          final result = await bankRepositoryImpl.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          );
          // assert
          verify(mockRemoteDataSource.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          ));
          expect(result, equals(Right(bankResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          )).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          );
          // assert
          verify(mockRemoteDataSource.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          ));
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          )).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.updateBankDetails(
            paymentType: 'paymentType',
            bankId: 1,
            beneficiaryName: 'accountName',
            beneficiaryIban: 'bankName',
            paymentMethodId: 1,
          );
          // assert
          verifyZeroInteractions(mockRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test deleteBankDetails method
  group('deleteBankDetails', () {
    var bankResult = "bank deleted successfully";

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        // act
        bankRepositoryImpl.deleteBank(
          bankId: 1,
        );
        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockRemoteDataSource.deleteBank(
            bankId: 1,
          )).thenAnswer((_) async => bankResult);
          // act
          final result = await bankRepositoryImpl.deleteBank(
            bankId: 1,
          );
          // assert
          verify(mockRemoteDataSource.deleteBank(
            bankId: 1,
          ));
          expect(result, equals(Right(bankResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.deleteBank(
            bankId: 1,
          )).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.deleteBank(
            bankId: 1,
          );
          // assert
          verify(mockRemoteDataSource.deleteBank(
            bankId: 1,
          ));
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.deleteBank(
            bankId: 1,
          )).thenThrow(ServerException());
          // act
          final result = await bankRepositoryImpl.deleteBank(
            bankId: 1,
          );
          // assert
          verifyZeroInteractions(mockRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });

  // test getBankDetails method
  group("getBankDetails", () {
    var bankId = 1;
    var bankResult = BankDetails(
      bankId: 1,
      bankName: "bankName",
      accountNumber: "accountNumber",
      paymentType: "paymentType",
      beneficiaryName: 'accountName',
      beneficiaryIban: 'bankName',
      swiftCode: 'bankCode',
      id: 1,
      isEnabled: true,
    );

    test(
      'should check if the device is online',
      () async {
        // arrange
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        // act
        bankRepositoryImpl.getBankDetails(bankId: bankId);
        // assert
        verify(mockNetworkInfo.isConnected);
      },
    );

    group('device is online', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      });
      test(
        'should return remote data when the call to remote data source is successful',
        () async {
          // arrange
          when(mockRemoteDataSource.getBankDetails(bankId: bankId))
              .thenAnswer((_) async => bankResult);
          // act
          final result =
              await bankRepositoryImpl.getBankDetails(bankId: bankId);
          // assert
          verify(mockRemoteDataSource.getBankDetails(bankId: bankId));
          expect(result, equals(Right(bankResult)));
        },
      );

      test(
        'should return server failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.getBankDetails(bankId: bankId))
              .thenThrow(ServerException());
          // act
          final result =
              await bankRepositoryImpl.getBankDetails(bankId: bankId);
          // assert
          verify(mockRemoteDataSource.getBankDetails(bankId: bankId));
          expect(result, equals(Left(ServerFailure())));
        },
      );
    });

    group('device is offline', () {
      setUp(() {
        when(mockNetworkInfo.isConnected).thenAnswer((_) async => false);
      });
      test(
        'should return network failure when the call to remote data source is unsuccessful',
        () async {
          // arrange
          when(mockRemoteDataSource.getBankDetails(bankId: bankId))
              .thenThrow(ServerException());
          // act
          final result =
              await bankRepositoryImpl.getBankDetails(bankId: bankId);
          // assert
          verifyZeroInteractions(mockRemoteDataSource);
          expect(result, equals(Left(NetworkFailure())));
        },
      );
    });
  });
}
