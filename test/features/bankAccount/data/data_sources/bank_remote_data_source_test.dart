import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/flavor/flavor_config.dart';
import 'package:thrivve/features/bankAccount/data/data_sources/bank_remote_data_source.dart';
import 'package:thrivve/features/bankAccount/data/models/add_bank_model.dart';
import 'package:thrivve/features/bankAccount/data/models/bank_details_model.dart';
import 'package:thrivve/features/bankAccount/data/models/bank_model.dart';
import 'package:thrivve/features/bankAccount/domain/entities/add_bank.dart';
import 'package:thrivve/features/bankAccount/domain/entities/bank.dart';
import 'package:thrivve/features/bankAccount/domain/entities/bank_details.dart';

import '../../../../fixtures/fixture-reader.dart';
import 'bank_remote_data_source_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  late MockApiClient mockApiClient;
  late BankRemoteDataSource bankRemoteDataSource;

  setUp(() {
    FlavorConfig(flavor: Flavor.dev);
    mockApiClient = MockApiClient();
    bankRemoteDataSource = BankRemoteDataSourceImpl(apiClient: mockApiClient);
  });

  group('addBank', () {
    final tAddBankResponse =
        AddBankModel.fromJson(json.decode(fixture('add_bank_response.json')));

    const tPaymentType = 'VISA';
    const tBankId = 1;
    const tBeneficiaryIban = 'IBAN123';
    const tBeneficiaryName = 'John Doe';

    test('should successfully add a bank account', () async {
      // arrange
      when(mockApiClient.request<AddBankSuccessfully>(
        endpoint: ApiSettings.thrivvePaymentMethod,
        method: RequestType.post,
        data: {
          'payment_type': tPaymentType,
          'bank_id': tBankId,
          'beneficiary_iban': tBeneficiaryIban,
          'beneficiary_name': tBeneficiaryName,
        },
        fromJson: anyNamed('fromJson'),
      )).thenAnswer((_) async => ApiResponse(
            data: tAddBankResponse,
            statusCode: 200,
          ));

      // act
      final result = await bankRemoteDataSource.addBank(
        paymentType: tPaymentType,
        bankId: tBankId,
        beneficiaryIban: tBeneficiaryIban,
        beneficiaryName: tBeneficiaryName,
      );

      // assert
      expect(result, equals(tAddBankResponse));
      verify(mockApiClient.request<AddBankSuccessfully>(
        endpoint: ApiSettings.thrivvePaymentMethod,
        method: RequestType.post,
        data: {
          'payment_type': tPaymentType,
          'bank_id': tBankId,
          'beneficiary_iban': tBeneficiaryIban,
          'beneficiary_name': tBeneficiaryName,
        },
        fromJson: anyNamed('fromJson'),
      ));
    });

    test('should throw ServerException when adding bank fails', () {
      // arrange
      when(mockApiClient.request<AddBankSuccessfully>(
        endpoint: ApiSettings.thrivvePaymentMethod,
        method: RequestType.post,
        data: anyNamed('data'),
        fromJson: anyNamed('fromJson'),
      )).thenThrow(ServerException(msj: 'Failed to add bank'));

      // act & assert
      expect(
        () => bankRemoteDataSource.addBank(
          paymentType: tPaymentType,
          bankId: tBankId,
          beneficiaryIban: tBeneficiaryIban,
          beneficiaryName: tBeneficiaryName,
        ),
        throwsA(isA<ServerException>()),
      );
    });
  });

  group('getBanks', () {
    final tBanksList = (json.decode(fixture('bank_list.json')) as List)
        .map((x) => BankModel.fromJson(x))
        .toList();

    test('should successfully get list of banks', () async {
      // arrange
      when(mockApiClient.request<List<Bank>>(
        endpoint: ApiSettings.thrivveBanks,
        fromJson: anyNamed('fromJson'),
      )).thenAnswer((_) async => ApiResponse(
            data: tBanksList,
            statusCode: 200,
          ));

      // act
      final result = await bankRemoteDataSource.getBanks();

      // assert
      expect(result, equals(tBanksList));
    });

    test('should throw ServerException when getting banks list fails', () {
      // arrange
      when(mockApiClient.request<List<Bank>>(
        endpoint: ApiSettings.thrivveBanks,
        fromJson: anyNamed('fromJson'),
      )).thenThrow(ServerException(msj: 'Failed to get banks'));

      // act & assert
      expect(
        () => bankRemoteDataSource.getBanks(),
        throwsA(isA<ServerException>()),
      );
    });
  });

  group('deleteBank', () {
    const tBankId = 1;
    final tDeleteResponse = {'message': 'Bank deleted successfully'};

    test('should successfully delete bank', () async {
      // arrange
      when(mockApiClient.request<Map<String, dynamic>>(
        endpoint: '${ApiSettings.thrivvePaymentMethod}/$tBankId/delete',
        method: RequestType.post,
        fromJson: anyNamed('fromJson'),
      )).thenAnswer((_) async => ApiResponse(
            data: tDeleteResponse,
            statusCode: 200,
          ));

      // act
      final result = await bankRemoteDataSource.deleteBank(bankId: tBankId);

      // assert
      expect(result, equals(tDeleteResponse['message']));
    });

    test('should throw ServerException when deleting bank fails', () {
      // arrange
      when(mockApiClient.request<Map<String, dynamic>>(
        endpoint: '${ApiSettings.thrivvePaymentMethod}/$tBankId/delete',
        method: RequestType.post,
        fromJson: anyNamed('fromJson'),
      )).thenThrow(ServerException(msj: 'Failed to delete bank'));

      // act & assert
      expect(
        () => bankRemoteDataSource.deleteBank(bankId: tBankId),
        throwsA(isA<ServerException>()),
      );
    });
  });

  group('getBankDetails', () {
    final tBankDetails =
        BankDetailsModel.fromJson(json.decode(fixture('bank_details.json')));
    const tBankId = 1;

    test('should successfully get bank details', () async {
      // arrange
      when(mockApiClient.request<BankDetails>(
        endpoint: '${ApiSettings.thrivvePaymentMethod}/$tBankId',
        fromJson: anyNamed('fromJson'),
      )).thenAnswer((_) async => ApiResponse(
            data: tBankDetails,
            statusCode: 200,
          ));

      // act
      final result = await bankRemoteDataSource.getBankDetails(bankId: tBankId);

      // assert
      expect(result, equals(tBankDetails));
    });

    test('should throw ServerException when getting bank details fails', () {
      // arrange
      when(mockApiClient.request<BankDetails>(
        endpoint: '${ApiSettings.thrivvePaymentMethod}/$tBankId',
        fromJson: anyNamed('fromJson'),
      )).thenThrow(ServerException(msj: 'Failed to get bank details'));

      // act & assert
      expect(
        () => bankRemoteDataSource.getBankDetails(bankId: tBankId),
        throwsA(isA<ServerException>()),
      );
    });
  });
}
