import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/bankAccount/domain/entities/bank.dart';
import 'package:thrivve/features/bankAccount/domain/use_cases/get_banks_use_case.dart';

import 'add_bank_account_use_case_test.dart';

void main() {
  late MockBankRepository mockBankRepository;
  late GetBanksUseCase getBanksUseCase;
  setUp(() {
    mockBankRepository = MockBankRepository();
    getBanksUseCase = GetBanksUseCase(bankRepository: mockBankRepository);
  });

  var bankList = [
    const Bank(
      id: 1,
      text: '',
      image: '',
    ),
    const Bank(
      id: 2,
      text: '',
      image: '',
    )
  ];

  test("test get bank list use case", () async {
    // arrange
    when(mockBankRepository.getBankList())
        .thenAnswer((_) async => Right(bankList));

    // act

    var result = await getBanksUseCase(NoParams());

    // assert
    verify(mockBankRepository.getBankList());
    expect(result, Right(bankList));
  });

  test("test get bank list use case when return NetworkFailure", () async {
    // arrange
    when(mockBankRepository.getBankList())
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act

    var result = await getBanksUseCase(NoParams());

    // assert
    verify(mockBankRepository.getBankList());
    expect(result, Left(NetworkFailure()));
  });

  test("test get bank list use case when return ServerFailure", () async {
    // arrange
    when(mockBankRepository.getBankList())
        .thenAnswer((_) async => Left(ServerFailure()));

    // act

    var result = await getBanksUseCase(NoParams());

    // assert
    verify(mockBankRepository.getBankList());
    expect(result, Left(ServerFailure()));
  });
}
