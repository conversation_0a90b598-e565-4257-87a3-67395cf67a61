import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/withdraw/domain/entities/payment_method_creation_setting.dart';
import 'package:thrivve/features/withdraw/domain/use_cases/get_payment_method_creation_setting_use_case.dart';

import 'check_withdraw_fees_use_case_test.mocks.dart';

void main() {
  late GetPaymentMethodCreationSettingUseCase useCase;
  late MockWithdrawRepository mockRepository;

  setUp(() {
    mockRepository = MockWithdrawRepository();
    useCase =
        GetPaymentMethodCreationSettingUseCase(repository: mockRepository);
  });

  final tPaymentMethodCreationSetting = PaymentMethodCreationSetting(
    notes: [
      'note1',
      'note2',
    ],
  ); // Example PaymentMethodCreationSetting object

  test('should get PaymentMethodCreationSetting from the repository on success',
      () async {
    // Arrange
    when(mockRepository.getPaymentMethodCreationSetting())
        .thenAnswer((_) async => Right(tPaymentMethodCreationSetting));

    // Act
    final result = await useCase(NoParams());

    // Assert
    expect(result, Right(tPaymentMethodCreationSetting));
    verify(mockRepository.getPaymentMethodCreationSetting());
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return a Failure when the repository returns an error',
      () async {
    // Arrange
    final tFailure = ServerFailure();
    when(mockRepository.getPaymentMethodCreationSetting())
        .thenAnswer((_) async => Left(tFailure));

    // Act
    final result = await useCase(NoParams());

    // Assert
    expect(result, Left(tFailure));
    verify(mockRepository.getPaymentMethodCreationSetting());
    verifyNoMoreInteractions(mockRepository);
  });
}
