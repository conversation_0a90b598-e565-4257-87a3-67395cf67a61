import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/withdraw/domain/entities/withdraw_confirmation.dart';
import 'package:thrivve/features/withdraw/domain/repositories/withdraw_repository.dart';
import 'package:thrivve/features/withdraw/domain/use_cases/check_withdraw_fees_use_case.dart';

import 'check_withdraw_fees_use_case_test.mocks.dart';

// Generate a MockWithdrawRepository using Mockito
// class MockWithdrawRepository extends Mock implements WithdrawRepository {}

@GenerateMocks([WithdrawRepository])
void main() {
  late CheckWithdrawFeesUseCase useCase;
  late MockWithdrawRepository mockRepository;

  setUp(() {
    mockRepository = MockWithdrawRepository();
    useCase = CheckWithdrawFeesUseCase(repository: mockRepository);
  });

  final tAmount = 100.0;
  final tPaymentMethodId = 1;
  final tWithdrawConfirmation = WithdrawConfirmation(
    message: 'Success',
    sentMessage: 'Success',
    sentAmount: '100.0',
    receivedMessage: 'Success',
    receivedAmount: '100.0',
    feesMessage: 'Success',
    fees: '0.0',
    steps: [],
    countryCode: '',
    currency: '', title: '', data: {}, statusKey: '',
  ); // Example confirmation

  test('should get WithdrawConfirmation from the repository on success',
      () async {
    // Arrange
    when(mockRepository.checkWithdrawFees(
      amount: tAmount,
      paymentMethodId: tPaymentMethodId,
    )).thenAnswer((_) async => Right(tWithdrawConfirmation));

    // Act
    final result = await useCase(
        WithdrawFeesParams(amount: tAmount, paymentMethodId: tPaymentMethodId));

    // Assert
    expect(result, Right(tWithdrawConfirmation));
    verify(mockRepository.checkWithdrawFees(
        amount: tAmount, paymentMethodId: tPaymentMethodId));
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return a Failure when the repository returns an error',
      () async {
    // Arrange
    final tFailure = ServerFailure();
    when(mockRepository.checkWithdrawFees(
      amount: tAmount,
      paymentMethodId: tPaymentMethodId,
    )).thenAnswer((_) async => Left(tFailure));

    // Act
    final result = await useCase(
        WithdrawFeesParams(amount: tAmount, paymentMethodId: tPaymentMethodId));

    // Assert
    expect(result, Left(tFailure));
    verify(mockRepository.checkWithdrawFees(
        amount: tAmount, paymentMethodId: tPaymentMethodId));
    verifyNoMoreInteractions(mockRepository);
  });
}
