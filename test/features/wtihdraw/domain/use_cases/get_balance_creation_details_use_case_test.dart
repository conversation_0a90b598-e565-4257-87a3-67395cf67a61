import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/withdraw/domain/entities/withdraw.dart';
import 'package:thrivve/features/withdraw/domain/use_cases/get_balance_creation_details_use_case.dart';

import 'check_withdraw_fees_use_case_test.mocks.dart';

// Generate a MockWithdrawRepository using Mockito

void main() {
  late GetBalanceCreationDetailsUseCase useCase;
  late MockWithdrawRepository mockRepository;

  setUp(() {
    mockRepository = MockWithdrawRepository();
    useCase = GetBalanceCreationDetailsUseCase(repository: mockRepository);
  });

  final tWithdraw = Withdraw.dummy(); // Example confirmation

  test('should get Withdraw details from the repository on success', () async {
    // Arrange
    when(mockRepository.getWithdrawRequestCreationDetails())
        .thenAnswer((_) async => Right(tWithdraw));

    // Act
    final result = await useCase(NoParams());

    // Assert
    expect(result, Right(tWithdraw));
    verify(mockRepository.getWithdrawRequestCreationDetails());
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return a Failure when the repository returns an error',
      () async {
    // Arrange
    final tFailure = ServerFailure();
    when(mockRepository.getWithdrawRequestCreationDetails())
        .thenAnswer((_) async => Left(tFailure));

    // Act
    final result = await useCase(NoParams());

    // Assert
    expect(result, Left(tFailure));
    verify(mockRepository.getWithdrawRequestCreationDetails());
    verifyNoMoreInteractions(mockRepository);
  });
}
