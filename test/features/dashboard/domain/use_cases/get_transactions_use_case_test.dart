import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/dashboard/domain/entities/main_transaction.dart';

import 'change_language_use_case_test.dart';

class MockMainTransaction extends Mock implements MainTransaction {}

void main() {
  late MockDashboardRepository mockDashboardRepository;
  late MockMainTransaction mockMainTransaction;

  setUp(() {
    mockMainTransaction = MockMainTransaction();
    mockDashboardRepository = MockDashboardRepository();
  });

  int page = 1;
  int perPage = 10;

  // test Get Transactions use case when return successfully
  test("test Get Transactions use case when return successfully", () async {
    // arrange
    when(mockDashboardRepository.getTransactions(page: page, perPage: perPage))
        .thenAnswer((_) async => Right(mockMainTransaction));

    // act
    var result = await mockDashboardRepository.getTransactions(
        page: page, perPage: perPage);

    // assert
    verify(
        mockDashboardRepository.getTransactions(page: page, perPage: perPage));
    expect(result, Right(mockMainTransaction));
  });

  // test Get Transactions use case when return Server failure
  test("test Get Transactions use case when return Server failure", () async {
    // arrange
    when(mockDashboardRepository.getTransactions(page: page, perPage: perPage))
        .thenAnswer((_) async => Left(ServerFailure()));

    // act
    var result = await mockDashboardRepository.getTransactions(
        page: page, perPage: perPage);

    // assert
    verify(
        mockDashboardRepository.getTransactions(page: page, perPage: perPage));
    expect(result, Left(ServerFailure()));
  });

  // test Get Transactions use case when return Network failure
  test("test Get Transactions use case when return Network failure", () async {
    // arrange
    when(mockDashboardRepository.getTransactions(page: page, perPage: perPage))
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act
    var result = await mockDashboardRepository.getTransactions(
        page: page, perPage: perPage);

    // assert
    verify(
        mockDashboardRepository.getTransactions(page: page, perPage: perPage));
    expect(result, Left(NetworkFailure()));
  });
}
