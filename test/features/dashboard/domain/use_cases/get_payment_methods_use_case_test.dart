import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/dashboard/domain/entities/payment_method.dart';

import 'change_language_use_case_test.dart';

class MockPaymentMethods extends Mock implements List<PaymentMethod> {}

void main() {
  late MockDashboardRepository mockDashboardRepository;
  late MockPaymentMethods mockPaymentMethods;
  setUp(() {
    mockPaymentMethods = MockPaymentMethods();
    mockDashboardRepository = MockDashboardRepository();
  });

  String paymentType = "type";

  // test Get Payment Methods use case when return successfully
  test("test Get Payment Methods without params use case when return successfully", () async {
    // arrange
    when(mockDashboardRepository.getPaymentMethods())
        .thenAnswer((_) async => Right(mockPaymentMethods));

    // act
    var result = await mockDashboardRepository.getPaymentMethods(
        );

    // assert
    verify(mockDashboardRepository.getPaymentMethods());
    expect(result, Right(mockPaymentMethods));
  });
  // test Get Payment Methods use case when return successfully
  test("test Get Payment Methods use case when return successfully", () async {
    // arrange
    when(mockDashboardRepository.getPaymentMethods(paymentType: paymentType))
        .thenAnswer((_) async => Right(mockPaymentMethods));

    // act
    var result = await mockDashboardRepository.getPaymentMethods(
        paymentType: paymentType);

    // assert
    verify(mockDashboardRepository.getPaymentMethods(paymentType: paymentType));
    expect(result, Right(mockPaymentMethods));
  });

  // test Get Payment Methods use case when return Server failure
  test("test Get Payment Methods use case when return Server failure",
      () async {
    // arrange
    when(mockDashboardRepository.getPaymentMethods(paymentType: paymentType))
        .thenAnswer((_) async => Left(ServerFailure()));

    // act
    var result = await mockDashboardRepository.getPaymentMethods(paymentType:  paymentType);

    // assert
    verify(mockDashboardRepository.getPaymentMethods(paymentType : paymentType));
    expect(result, Left(ServerFailure()));
  });

  // test Get Payment Methods use case when return Network failure
  test("test Get Payment Methods use case when return Network failure",
      () async {
    // arrange
    when(mockDashboardRepository.getPaymentMethods(paymentType : paymentType))
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act
    var result = await mockDashboardRepository.getPaymentMethods(paymentType  : paymentType);

    // assert
    verify(mockDashboardRepository.getPaymentMethods(paymentType : paymentType));
    expect(result, Left(NetworkFailure()));
  });
}
