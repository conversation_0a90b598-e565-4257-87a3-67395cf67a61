
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/dashboard/domain/entities/withdraw_balance.dart';

import 'change_language_use_case_test.dart';


class MockWithdrawBalance extends Mock implements WithdrawBalance {}

void main() {

  late MockDashboardRepository mockDashboardRepository;
  late MockWithdrawBalance mockWithdrawBalance;

  setUp(() {
    mockWithdrawBalance = MockWithdrawBalance();
    mockDashboardRepository = MockDashboardRepository();
  });

  // test Get Withdraw Balance use case when return successfully
  test("test Get Withdraw Balance use case when return successfully", () async {
    // arrange
    when(mockDashboardRepository.getWithdrawBalance())
        .thenAnswer((_) async => Right(mockWithdrawBalance));

    // act
    var result = await mockDashboardRepository.getWithdrawBalance();

    // assert
    verify(mockDashboardRepository.getWithdrawBalance());
    expect(result, Right(mockWithdrawBalance));
  });

  // test Get Withdraw Balance use case when return Server failure
  test("test Get Withdraw Balance use case when return Server failure", () async {
    // arrange
    when(mockDashboardRepository.getWithdrawBalance())
        .thenAnswer((_) async => Left(ServerFailure()));

    // act
    var result = await mockDashboardRepository.getWithdrawBalance();

    // assert
    verify(mockDashboardRepository.getWithdrawBalance());
    expect(result, Left(ServerFailure()));
  });

  // test Get Withdraw Balance use case when return Network failure
  test("test Get Withdraw Balance use case when return Network failure", () async {
    // arrange
    when(mockDashboardRepository.getWithdrawBalance())
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act
    var result = await mockDashboardRepository.getWithdrawBalance();

    // assert
    verify(mockDashboardRepository.getWithdrawBalance());
    expect(result, Left(NetworkFailure()));
  });

}