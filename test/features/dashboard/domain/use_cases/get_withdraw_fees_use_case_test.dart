import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';

import 'change_language_use_case_test.dart';

void main() {
  late MockDashboardRepository mockDashboardRepository;
  setUp(() {
    mockDashboardRepository = MockDashboardRepository();
  });

  var mockWithdrawFees = 2.0;
  var withdrawAmount = 10.0;

  // test Get Withdraw Fees use case when return successfully
  test("test Get Withdraw Fees use case when return successfully", () async {
    // arrange
    when(mockDashboardRepository.getWithdrawFees(amount: withdrawAmount))
        .thenAnswer((_) async => Right(mockWithdrawFees));

    // act
    var result =
        await mockDashboardRepository.getWithdrawFees(amount: withdrawAmount);

    // assert
    verify(mockDashboardRepository.getWithdrawFees(amount: withdrawAmount));
    expect(result, Right(mockWithdrawFees));
  });

  // test Get Withdraw Fees use case when return Server failure
  test("test Get Withdraw Fees use case when return Server failure", () async {
    // arrange
    when(mockDashboardRepository.getWithdrawFees(amount: withdrawAmount))
        .thenAnswer((_) async => Left(ServerFailure()));

    // act
    var result =
        await mockDashboardRepository.getWithdrawFees(amount: withdrawAmount);

    // assert
    verify(mockDashboardRepository.getWithdrawFees(amount: withdrawAmount));
    expect(result, Left(ServerFailure()));
  });

  // test Get Withdraw Fees use case when return Network failure
  test("test Get Withdraw Fees use case when return Network failure", () async {
    // arrange
    when(mockDashboardRepository.getWithdrawFees(amount: withdrawAmount))
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act
    var result =
        await mockDashboardRepository.getWithdrawFees(amount: withdrawAmount);

    // assert
    verify(mockDashboardRepository.getWithdrawFees(amount: withdrawAmount));
    expect(result, Left(NetworkFailure()));
  });
}
