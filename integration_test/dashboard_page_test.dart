import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:thrivve/core/injection_container.dart' as di;
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/notification_bell_widget.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/user_icon_widget.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/withdraw_balance_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  runTests();
}

Future<void> runTests() async {
  await di.init(enableProxy: true);

  Widget createTestableWidget(Widget child) {
    return MultiProvider(
      providers: [
        Provider<MainHomeBloc>(
          create: (context) => getIt<MainHomeBloc>(),
        ),
      ],
      child: MaterialApp(home: child),
    );
  }

  group('Dashboard Page Integration Test', () {
    testWidgets('Loads the Dashboard Page', (WidgetTester tester) async {
      await tester.pumpWidget(createTestableWidget(DashboardPage()));
      expect(find.byType(DashboardPage), findsOneWidget);
    });

    testWidgets('Taps on User Icon and Profile Status Widget',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestableWidget(DashboardPage()));
      await tester.tap(find.byType(UserIconAndProfileStatusWidget));
      await tester.pumpAndSettle();
      expect(find.byType(Drawer), findsOneWidget);
    });

    testWidgets('Taps on Complete Profile Button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestableWidget(DashboardPage()));
      await tester.tap(find.byType(UserIconAndProfileStatusWidget));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Complete Profile'));
      await tester.pumpAndSettle();
      expect(find.text('أكمل ملفك الشخصي'), findsOneWidget);
    });

    testWidgets('Taps on Notification Bell Widget',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestableWidget(DashboardPage()));
      await tester.tap(find.byType(NotificationBellWidget));
      await tester.pumpAndSettle();
      // Add assertions to verify expected behavior
    });

    testWidgets('Taps on Withdraw Balance Widget', (WidgetTester tester) async {
      await tester.pumpWidget(createTestableWidget(DashboardPage()));
      await tester.tap(find.byType(WithdrawBalanceWidget));
      await tester.pumpAndSettle();
      // Add assertions to verify expected behavior
    });
  });
}
